
[1mFrom:[0m /home/<USER>/mirraw_desktop/app/controllers/api_controller.rb @ line 387 ApiController#check_free_shipping:

    [1;34m382[0m: [32mdef[0m [1;34mcheck_free_shipping[0m
    [1;34m383[0m:   [32mbegin[0m
    [1;34m384[0m:     country = params[[33m:country[0m]
    [1;34m385[0m:     cart = [1;34;4mCart[0m.where([35mid[0m: params[[33m:cart_id[0m]).preload([35mline_items[0m: [[35mdesign[0m: [33m:categories[0m]).first
    [1;34m386[0m:     shipping = cart.get_international_shipping_cost(@conversion_rate, country)
 => [1;34m387[0m:     binding.pry
    [1;34m388[0m:     grandtotal, taxes, shipping, wallet_discount, platform_fee = @cart.get_all_the_cart_details(@country_code, country , [1;36mnil[0m ,@conversion_rate, [1;36mnil[0m, session[[33m:gift_wrapped[0m])
    [1;34m389[0m:     shipping_with_currency = cart.get_international_shipping_cost_in_currency_with_symbol(@conversion_rate, @symbol, country)
    [1;34m390[0m:     available = cart.get_free_shipping_rate(@conversion_rate, country) > [1;34m0[0m && cart.free_shipping_available?(@conversion_rate, country) && !cart.cart_has_bmgn_products? && !cart.shipping_categories_available?
    [1;34m391[0m:     apply_for_country = cart.get_free_shipping_rate(@conversion_rate, country) > [1;34m0[0m
    [1;34m392[0m:     shipping_text = cart.free_shipping_charges_text(country, @conversion_rate, @symbol)
    [1;34m393[0m:     express_shipping_charge = ([1;34;4mCountry[0m.get_express_delivery_charge(country)/@conversion_rate).round([1;34m2[0m)
    [1;34m394[0m:     express_shipping_with_currency = country == [31m[1;31m'[0m[31mIndia[1;31m'[0m[31m[0m ? [31m[1;31m"[0m[31m#{@symbol}[0m[31m #{express_shipping_charge}[0m[31m[1;31m"[0m[31m[0m : [31m[1;31m"[0m[31m#{@symbol}[0m[31m #{shipping + express_shipping_charge}[0m[31m[1;31m"[0m[31m[0m
    [1;34m395[0m:     response = { [35mexpress_shipping_charge[0m: express_shipping_charge, [35mexpress_shipping_with_currency[0m: express_shipping_with_currency, [35mavailable[0m: available, [35mshipping[0m: shipping, 
    [1;34m396[0m:       [35mshipping_with_currency[0m: shipping_with_currency, [35mapply_for_country[0m:  apply_for_country, [35mshipping_text[0m: shipping_text, [35mgrandtotal[0m: grandtotal, [35mtaxes[0m: taxes, [35mwallet_discount[0m: wallet_discount, [35mplatform_fee[0m: platform_fee}
    [1;34m397[0m:   [32mrescue[0m => error
    [1;34m398[0m:     response = {[35mfree_shipping[0m: [1;36mfalse[0m, [35merror_text[0m: error.message, [35merror[0m: [1;36mtrue[0m}
    [1;34m399[0m:   [32mend[0m
    [1;34m400[0m:    render [35mjson[0m: response
    [1;34m401[0m: [32mend[0m

