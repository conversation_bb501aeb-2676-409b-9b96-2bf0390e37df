When I  updated this 

_dmarc.notification.mirraw.in
TXT
Simple
-
No
"v=DMARC1; p=none"
300

then i triggred the mail I am getting passed from domain mirraw.com


Delivered-To: <EMAIL>
Received: by 2002:a05:6e04:6606:b0:363:eae2:a59f with SMTP id ee6csp859298imd;
        Thu, 24 Jul 2025 05:57:55 -0700 (PDT)
X-Forwarded-Encrypted: i=4; AJvYcCXU5eR64Ck+djX7IeEIV6cWTYNr6Fi9VI4aVoaaFmEHdzABr0ntAkpOh4j4VU9a794taLFQyr9SBkxoLkuCJLE=@mirraw.com
X-Received: by 2002:ac8:7e8f:0:b0:4ab:7755:adac with SMTP id d75a77b69052e-4ae80f3d9femr22499001cf.14.1753361875277;
        Thu, 24 Jul 2025 05:57:55 -0700 (PDT)
ARC-Seal: i=3; a=rsa-sha256; t=1753361875; cv=pass;
        d=google.com; s=arc-20240605;
        b=Qn3xsFlYzYiT1jlH7mxKRCDP+F2t3Ikg71pzWqkH861xmAs+Lv/FnIMrpnVVGwjJGD
         OVYGytw6eMdZTnaS5b/1Hy0s+UEitRsPCmmB8vKzF6ep77o7c3BHWQGMCD39ezDMBOd+
         rBzYPN6qUTztYQv16mVdckWgx+4u9CNtdKfHC4/wF8Oc74jp54cxKIC4pa40/g9wDB2L
         CZYVDPrATpgSr/6JxHzuuATLfXO8aSY9pk6TjU6/B96pJAgz5qXM2+il9sfWzoUweQI1
         6GLOZMNSuxx7OQr79wScTB/n1BQMqNvU2ecC5Ec0x1h34s3hia1Qsnrlird6lTmhlNYl
         foew==
ARC-Message-Signature: i=3; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20240605;
        h=list-unsubscribe:list-archive:list-help:list-post:list-id
         :mailing-list:precedence:feedback-id:content-transfer-encoding
         :mime-version:subject:message-id:to:from:date:sender:dkim-signature;
        bh=nwELYIOVmQ4+BCSYUUC07hr8yUj4R/9JkPT4igPQUxM=;
        fh=WZB7xuuNkjNdat51risPE+HGkV9JVdsCmgLcNziFgSI=;
        b=NXm/GmO4bWqPYSX5iPTSaIXbzSXJI+/ENbaP2uL4uFIgtdkzqOkpVNZbuFvpQSQ7yf
         7ck/P4YUgWLqMlw+XDuWp9cKtov0ToF6FYlRTGIg9Frw6UW6VP906N86bge4CY7egcEx
         +WHnGKZulEqgvyVGq1qKBoPOATGxiyQa686YjpnDeF5qT0Cg+5Sh2qPFJWg2fp8/Jy6m
         ar59H7W0mHqsK0h/z5h9c6zP5jRjkVjw61H7FQEbf/zvxEb1sCkXmJnYL4ldydpoMSFZ
         P3qB+DoojBWkp7hhBMIAllBr6xulPzLduNmDz2VNaOmRUlfIwJPddUCxtW+wVc1doFH/
         8DNQ==;
        dara=google.com
ARC-Authentication-Results: i=3; mx.google.com;
       dkim=pass header.i=@mirraw.com header.s=google header.b=D+fqPkps;
       arc=pass (i=2 spf=pass spfdomain=amazonses.com dkim=pass dkdomain=notification.mirraw.in dkim=pass dkdomain=amazonses.com dmarc=pass fromdomain=notification.mirraw.in);
       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=fail (p=NONE sp=NONE dis=NONE arc=pass) header.from=notification.mirraw.in;
       dara=fail header.i=@mirraw.com
Return-Path: <<EMAIL>>
Received: from mail-sor-f69.google.com (mail-sor-f69.google.com. [*************])
        by mx.google.com with SMTPS id d75a77b69052e-4ae80fc6042sor9704941cf.4.2025.***********.55
        for <<EMAIL>>
        (Google Transport Security);
        Thu, 24 Jul 2025 05:57:55 -0700 (PDT)
Received-SPF: pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) client-ip=*************;
Authentication-Results: mx.google.com;
       dkim=pass header.i=@mirraw.com header.s=google header.b=D+fqPkps;
       arc=pass (i=2 spf=pass spfdomain=amazonses.com dkim=pass dkdomain=notification.mirraw.in dkim=pass dkdomain=amazonses.com dmarc=pass fromdomain=notification.mirraw.in);
       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=fail (p=NONE sp=NONE dis=NONE arc=pass) header.from=notification.mirraw.in;
       dara=fail header.i=@mirraw.com
ARC-Seal: i=2; a=rsa-sha256; t=1753361875; cv=pass;
        d=google.com; s=arc-20240605;
        b=R8UCcXvGQOy1tdPXdDfjs8Wz2UTqqIR0yG/2xBlZNmQr9IzNgKF/3bZeg5s4QzNGvm
         nrBYgkzvcKz38Fm2H4IBUPWRXEp9n9Uz9GMjSs8P0vg8BUyTNLK7RuhPF9C7XgG1VY3t
         5SBIEZjtwRshYSn/6QY4QL2guATD88yMl3PE0GYECEcplcFoh7eKtYEO5v7navHXt2VQ
         y7c/FaGYXLhyOCZPc3CL62q+nZ5AJ+51gF16wbuxjt1WSFZlsluAgcLLqoTXYIq0S+KI
         d7UTR/Zy/H/7dfa48hb38qhN70DyuMs0MuSyDfq4tJ/RXOqYN+hvWKCETkYNkY8YXOaI
         Kx6Q==
ARC-Message-Signature: i=2; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20240605;
        h=list-unsubscribe:list-archive:list-help:list-post:list-id
         :mailing-list:precedence:feedback-id:content-transfer-encoding
         :mime-version:subject:message-id:to:from:date:sender:dkim-signature;
        bh=nwELYIOVmQ4+BCSYUUC07hr8yUj4R/9JkPT4igPQUxM=;
        fh=WZB7xuuNkjNdat51risPE+HGkV9JVdsCmgLcNziFgSI=;
        b=UVXHK/6j6Lr2A3h4vl3q2kUkklchC43Izdpv8Xpf2mblhmLCM1HPmbw4VrfC4frSrW
         hP3zb5WocHMNGC5w4Dks1T6AEK4Z6K9qDRFZhpiwbM+YjceoSPlVPhzkQ/ovznktVOMB
         yC6BcMZ66h9Pk+ZR3NhAKq9XLWwjdQhNi7QKtHXFitfCBS9X6wMxyfCLKX9h3Nx4HU5P
         SA76vxClq3szaHO049TQI3jsXbyDwOw2CxoCjgCQe7ekvzKkBlwAWQhHHIyH0kEsirU7
         qiFywvezFtHsYxrFLSIN89bO17jzwpegyO3p/C+Nc4l+t0MYR6RLsqr1UOeOQY/SpWno
         IQzw==;
        darn=mirraw.com
ARC-Authentication-Results: i=2; mx.google.com;
       dkim=pass header.i=@notification.mirraw.in header.s=4a76lqmwhziwuuohll3a6xzdz5vvr6do header.b=lH2yMqKk;
       dkim=pass header.i=@amazonses.com header.s=6gbrjpgwjskckoa6a5zn6fwqkn67xbtw header.b=ERIN8Siy;
       spf=pass (google.com: <NAME_EMAIL> designates *********** as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=pass (p=NONE sp=NONE dis=NONE) header.from=notification.mirraw.in
DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;
        d=mirraw.com; s=google; t=1753361875; x=1753966675; darn=mirraw.com;
        h=list-unsubscribe:list-archive:list-help:list-post:list-id
         :mailing-list:precedence:x-original-authentication-results
         :x-original-sender:feedback-id:content-transfer-encoding
         :mime-version:subject:message-id:to:from:date:sender:from:to:cc
         :subject:date:message-id:reply-to;
        bh=nwELYIOVmQ4+BCSYUUC07hr8yUj4R/9JkPT4igPQUxM=;
        b=D+fqPkps5P9SqnZNoAdkjRVXPAOb17KMZh7BLDyZummDjokwzIWoMORZS0Ui81Yl50
         MofDZtIZam4szsmtBPKRcPJ6Q/ac6vV8MFaOJJyQJ8rxhHM9RvQ8Wnw4Gz98MvI33eMM
         U0mZ79yms7xHqIYMVsCgh6E4HmLBM/DPj8/GE=
X-Google-DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;
        d=1e100.net; s=20230601; t=1753361875; x=1753966675;
        h=list-unsubscribe:list-archive:list-help:list-post
         :x-spam-checked-in-group:list-id:mailing-list:precedence
         :x-original-authentication-results:x-original-sender:feedback-id
         :content-transfer-encoding:mime-version:subject:message-id:to:from
         :date:x-beenthere:x-gm-message-state:sender:from:to:cc:subject:date
         :message-id:reply-to;
        bh=nwELYIOVmQ4+BCSYUUC07hr8yUj4R/9JkPT4igPQUxM=;
        b=FXjuudsvJFrtPTadqYaLO/4ozRjZhUMtSkwOZVt8BNkTJqeAuwj9PdIfylRDBHJXUD
         rOfHpHxIUCoIrjhW97xn60NGUI2GX+S377ReQNpwBR+b4QdnlBhyPmJihLnvLOhuVs1D
         zUab4+yMVN7zu3B4q6kP9idG1WqSjQO1PY72oY/3wamM+NaQz/tCdbY6fZLpyfVoUi9/
         Fi81ozAhcO9qCXQtZymy6jUjs0EVM0O9+5VVjuhAlGTDAsixK/WzQOWk96M2JNUKhZHm
         0+vhTOi9mt13ZWhl2WC6fKMWCn/m33Yt+6m8LLjqrLzoo0FVmhsPRRkjdpnA7sn4sdey
         kugQ==
Sender: <EMAIL>
X-Forwarded-Encrypted: i=2; AJvYcCU5xUgW6J8jFtbYWEQC6VbFUskiO6c62C5PPTw+IWCIx/aDSGAdSl6QexFFjCq3DzOJRV5ZkWwnuWikv7nL7DU=@mirraw.com
X-Gm-Message-State: AOJu0YwpUSAEocXwgLTNXRhYSEonPfSsNzYKOfWS4XaIkuiOxdd7VgQ/ GxO61GqgOLm5GSpFS0XVRXP1hCNmvhgS7ZDyjLsoKcWgJvv7bY6kVpbvPzVkLEmoTMU3Cw==
X-Google-Smtp-Source: AGHT+IE+zSfdlCyFcRTkaEZgnailbdUO/h82hpNLfdKeubV/9vLG9qcgEoCroNLZC2bnRDPe7w6PTA==
X-Received: by 2002:a05:622a:1451:b0:4ab:7173:ce8c with SMTP id d75a77b69052e-4ae80c3aa27mr24494531cf.0.1753361873521;
        Thu, 24 Jul 2025 05:57:53 -0700 (PDT)
X-BeenThere: <EMAIL>; h=AZMbMZe/60vyzcZg4zjGWkBuLcbt92x4RyuqSa8AC3LIMWLn8Q==
Received: by 2002:a05:622a:48e:b0:4ab:785d:5c50 with SMTP id d75a77b69052e-4ae7bd63a87ls8613681cf.1.-pod-prod-00-us; Thu, 24 Jul 2025 05:57:53 -0700 (PDT)
X-Received: by 2002:ac8:5803:0:b0:4ab:53d1:101b with SMTP id d75a77b69052e-4ae80e5a7cdmr25142201cf.11.1753361872844;
        Thu, 24 Jul 2025 05:57:52 -0700 (PDT)
ARC-Seal: i=1; a=rsa-sha256; t=1753361872; cv=none;
        d=google.com; s=arc-20240605;
        b=f8FB4fLPQ2MY0fF4xC2lDCJFul9kk92bZIW3aub79omAwxH7+OpU2WcyhMb280FpMv
         KIPT1jrRXDYjcZosRjlBGC2Uj+VNiUYvE2vq0KvHvXZMg/AV4b8tys0JUHGD+vKPISw4
         /XlldMsHKutPwovkGB0XmJDFb45JrepODBR2iKkQnHNzMjOmBw+u0ZpKraEBJeTJbZ72
         McJCO+apxHM3J08B4rb+XO2rE0Wob3Ip6/xXu789wk3EN86ThsNhh2NCmqe2kCosDGy8
         5t/maAaDe4Hb3rlub6rncKYpfayCjtuhDTVMaS/eKTjIUKj3z7a/ZSD3YWBWY0G5PPwx
         xJeQ==
ARC-Message-Signature: i=1; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20240605;
        h=feedback-id:content-transfer-encoding:mime-version:subject
         :message-id:to:from:date:dkim-signature:dkim-signature;
        bh=nwELYIOVmQ4+BCSYUUC07hr8yUj4R/9JkPT4igPQUxM=;
        fh=cdM3dzycIJliYOF5cScOwW2zngpSCCiPxeWiFKiqjec=;
        b=Ge/GhVeGLP7fFNwgvnpHDZzxLV1Bz4qyZcOGu6ZXlTRo0s98CF70TIGiq6SNhdg4Hk
         0TYoKp9LBg1/6KfNXjnDmIrLoiNg2pUJe7xdUjSn7V13LeUqh7xCX2NsE3OavZJDLmpI
         yAbH+6np3rytikVnWD5F9OpxDffFgKc3/GZ7tkmVjNOWMFiYu4q/EFbKL+xgwZEkmmC0
         2Wxs3KBiNLFcnYbWttvJ85/Y3r86OaDZR6q9ItadvXmB1yaNzSrylADVgiP1koSVST1M
         VsftV/SmvAXO4hc1Aum/V3+jYBImpvHrzYhMk4OtIONGgWUFTj5K/BmcW5qqzG4H5DhM
         GVTg==;
        dara=google.com
ARC-Authentication-Results: i=1; mx.google.com;
       dkim=pass header.i=@notification.mirraw.in header.s=4a76lqmwhziwuuohll3a6xzdz5vvr6do header.b=lH2yMqKk;
       dkim=pass header.i=@amazonses.com header.s=6gbrjpgwjskckoa6a5zn6fwqkn67xbtw header.b=ERIN8Siy;
       spf=pass (google.com: <NAME_EMAIL> designates *********** as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=pass (p=NONE sp=NONE dis=NONE) header.from=notification.mirraw.in
Received: from a9-38.smtp-out.amazonses.com (a9-38.smtp-out.amazonses.com. [***********])
        by mx.google.com with ESMTPS id d75a77b69052e-4ae815c0e7esi6499071cf.913.2025.***********.52
        for <<EMAIL>>
        (version=TLS1_3 cipher=TLS_AES_128_GCM_SHA256 bits=128/128);
        Thu, 24 Jul 2025 05:57:52 -0700 (PDT)
Received-SPF: pass (google.com: <NAME_EMAIL> designates *********** as permitted sender) client-ip=***********;
Date: Thu, 24 Jul 2025 12:57:52 +0000
From: notifier <<EMAIL>>
To: <EMAIL>
Message-ID: <<EMAIL>>
Subject: Prod Exception - Desktop - ADMIN (2 times)designs show (ActionView::Template::Error) "undefined method `name' for nil:Ni...
Mime-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 7bit
Feedback-ID: ::1.us-east-1.r63d/Xpyw/bQRtX6j7lTiOG6X4g6/Vaydbox7zDW+hM=:AmazonSES
X-SES-Outgoing: 2025.07.24-***********
X-Original-Sender: <EMAIL>
X-Original-Authentication-Results: mx.google.com;
       dkim=pass header.i=@notification.mirraw.in header.s=4a76lqmwhziwuuohll3a6xzdz5vvr6do header.b=lH2yMqKk;
       dkim=pass header.i=@amazonses.com header.s=6gbrjpgwjskckoa6a5zn6fwqkn67xbtw header.b=ERIN8Siy;
       spf=pass (google.com: <NAME_EMAIL> designates *********** as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=pass (p=NONE sp=NONE dis=NONE) header.from=notification.mirraw.in
Precedence: list
Mailing-list: list <EMAIL>; contact <EMAIL>
List-ID: <tech.mirraw.com>
X-Spam-Checked-In-Group: <EMAIL>
X-Google-Group-Id: 472167733174
List-Post: <https://groups.google.com/a/mirraw.com/group/tech/post>, <mailto:<EMAIL>>
List-Help: <https://support.google.com/a/mirraw.com/bin/topic.py?topic=25838>, <mailto:<EMAIL>>
List-Archive: <https://groups.google.com/a/mirraw.com/group/tech/>
List-Unsubscribe: <mailto:<EMAIL>>, <https://groups.google.com/a/mirraw.com/group/tech/subscribe>

An ActionView::Template::Error occurred in designs#show:

  undefined method `name' for nil:NilClass
  app/models/design.rb:1108:in `generate_design_hash'


-------------------------------
Request:
-------------------------------

...

>>>


All DKIM Record 

	
Record name
	
Type
	
Routing policy
	
Differentiator
	
Alias
	
Value/Route traffic to
	
TTL (seconds)
	
Health check ID
	
Evaluate target health
	
Record ID

Record name
	
Type
	
Routing policy
	
Differentiator
	
Alias
	
Value/Route traffic to
	
TTL (seconds)
	
Health check ID
	
Evaluate target health
	

2zcanuerfg7gazasnxx24l4guj5jlwjp._domainkey.mirraw.in
CNAME
Simple
-
No
2zcanuerfg7gazasnxx24l4guj5jlwjp.dkim.amazonses.com
300
-
-
-
aihwtjyvz73b3znif4wlrwpv6cr447nq._domainkey.mirraw.in
CNAME
Simple
-
No
aihwtjyvz73b3znif4wlrwpv6cr447nq.dkim.amazonses.com
300
-
-
-
fw7g6yydcwbithvitirj72hl6petl6re._domainkey.mirraw.in
CNAME
Simple
-
No
fw7g6yydcwbithvitirj72hl6petl6re.dkim.amazonses.com
1800
-
-
-
k1._domainkey.mirraw.in
CNAME
Simple
-
No
dkim.mcsv.net.
86400
-
-
-
mqqutvtihayxvdbqflkgizcpyjd4n7gp._domainkey.mirraw.in
CNAME
Simple
-
No
mqqutvtihayxvdbqflkgizcpyjd4n7gp.dkim.amazonses.com
1800
-
-
-
pyys6ujl2rqjbq7yk56jdshpswpadhs5._domainkey.mirraw.in
CNAME
Simple
-
No
pyys6ujl2rqjbq7yk56jdshpswpadhs5.dkim.amazonses.com
1800
-
-
-
ran5pvdcdvaodkfb2uxvfpvpokqjvzxw._domainkey.mirraw.in
CNAME
Simple
-
No
ran5pvdcdvaodkfb2uxvfpvpokqjvzxw.dkim.amazonses.com
300
-
-
-
3gmvpg366bamb3tedtrh2yc2fgdh226f._domainkey.dis.mirraw.in
CNAME
Simple
-
No
3gmvpg366bamb3tedtrh2yc2fgdh226f.dkim.amazonses.com
1800
-
-
-
5vzgae65f7cdyal6thnda5h3arh3o2om._domainkey.dis.mirraw.in
CNAME
Simple
-
No
5vzgae65f7cdyal6thnda5h3arh3o2om.dkim.amazonses.com
1800
-
-
-
64bgqtze7c2o5tscbwjm4o5osvlg7ifm._domainkey.dis.mirraw.in
CNAME
Simple
-
No
64bgqtze7c2o5tscbwjm4o5osvlg7ifm.dkim.amazonses.com
1800
-
-
-
gd2s7zkx2mhsobxgb2sqvxho3awk6giy._domainkey.dis.mirraw.in
CNAME
Simple
-
No
gd2s7zkx2mhsobxgb2sqvxho3awk6giy.dkim.amazonses.com
1800
-
-
-
r74ps74azbghtsu543kzxruicghctcz5._domainkey.dis.mirraw.in
CNAME
Simple
-
No
r74ps74azbghtsu543kzxruicghctcz5.dkim.amazonses.com
1800
-
-
-
wqk7ouq36nrhopkk2yklrrqyfllvu3qs._domainkey.dis.mirraw.in
CNAME
Simple
-
No
wqk7ouq36nrhopkk2yklrrqyfllvu3qs.dkim.amazonses.com
1800
-
-
-
6usw6vfwmvqeujb2vmcz6e7k6evdsbju._domainkey.e.mirraw.in
CNAME
Simple
-
No
6usw6vfwmvqeujb2vmcz6e7k6evdsbju.dkim.amazonses.com
1800
-
-
-
q4vmyqscuq433fbvxxhns6kjvddwffcy._domainkey.e.mirraw.in
CNAME
Simple
-
No
q4vmyqscuq433fbvxxhns6kjvddwffcy.dkim.amazonses.com
1800
-
-
-
xvc6iw3mqcprtofntmgxbvcvb2dz4rzf._domainkey.e.mirraw.in
CNAME
Simple
-
No
xvc6iw3mqcprtofntmgxbvcvb2dz4rzf.dkim.amazonses.com
1800
-
-
-
emm._domainkey.www.mirraw.in.mirraw.in
CNAME
Simple
-
No
dkim.nczc.asia.
3600
-
-
-
23tgz6tbm5jjrwfirdgsfrf2fn5xyzs6._domainkey.notification.mirraw.in
CNAME
Simple
-
No
23tgz6tbm5jjrwfirdgsfrf2fn5xyzs6.dkim.amazonses.com
1800
-
-
-
4a76lqmwhziwuuohll3a6xzdz5vvr6do._domainkey.notification.mirraw.in
CNAME
Simple
-
No
4a76lqmwhziwuuohll3a6xzdz5vvr6do.dkim.amazonses.com
1800
-
-
-
q5mnvakhjtjo22fvyp67jlr3jm7ggd2f._domainkey.notification.mirraw.in
CNAME
Simple
-
No
q5mnvakhjtjo22fvyp67jlr3jm7ggd2f.dkim.amazonses.com
1800
-
-


All SPF 

mirraw.in
TXT
Simple
-
No
"google-site-verification=RUHBByhn2th1X50RZ9OrDd_uXQ_7vTSm8p15gXiWD4I"
"v=spf1 include:servers.mcsv.net ?all include:nczc.asia"
3600
-
-
-
e.e.mirraw.in
TXT
Simple
-
No
"v=spf1 include:amazonses.com ~all"
300
-
-
-
notification.mirraw.in
TXT
Simple
-
No
"v=spf1 include:amazonses.com ~all"
300
-
-
-
reports.mirraw.in
TXT
Simple
-
No
"v=spf1 include:amazonses.com ~all"

