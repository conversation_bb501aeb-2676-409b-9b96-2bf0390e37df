Hii Clevertap





4:40
https://developer.clevertap.com/docs/get-event-count-api

CleverTap Developer DocsCleverTap Developer Docs
Get Event Count
Overview This endpoint is used to retrieve counts for an event in a specified duration. For example, you can get the total number of charge events in the past day. Base URL Here is an example base URL from the account in the India region: https://in1.api.clevertap.com/1/counts/events.json Region Ref... (5 kB)
https://developer.clevertap.com/docs/get-event-count-api

4:43
Your Event Count API doesn't work
4:44


curl -v -X POST 'https://api.clevertap.com/1/events/counts.json' \
-H 'X-CleverTap-Account-Id: XXX-XXX-XXX' \
-H 'X-CleverTap-Passcode: XXX-XXX-XXX' \
-H 'Content-Type: application/json' \
-d '{
  "event_name": "App Installed",
  "from": **********,
  "to": **********,
  "group_by": "date"
}'


Note: Unnecessary use of -X or --request, POST is already inferred.
*   Trying 18.239.153.4:443...
* Connected to api.clevertap.com (18.239.153.4) port 443 (#0)
* ALPN, offering h2
* ALPN, offering http/1.1
*  CAfile: /etc/ssl/certs/ca-certificates.crt
*  CApath: /etc/ssl/certs
* TLSv1.0 (OUT), TLS header, Certificate Status (22):
* TLSv1.3 (OUT), TLS handshake, Client hello (1):
* TLSv1.2 (IN), TLS header, Certificate Status (22):
* TLSv1.3 (IN), TLS handshake, Server hello (2):
* TLSv1.2 (IN), TLS header, Finished (20):
* TLSv1.2 (IN), TLS header, Supplemental data (23):
* TLSv1.3 (IN), TLS handshake, Encrypted Extensions (8):
* TLSv1.2 (IN), TLS header, Supplemental data (23):
* TLSv1.3 (IN), TLS handshake, Certificate (11):
* TLSv1.2 (IN), TLS header, Supplemental data (23):
* TLSv1.3 (IN), TLS handshake, CERT verify (15):
* TLSv1.2 (IN), TLS header, Supplemental data (23):
* TLSv1.3 (IN), TLS handshake, Finished (20):
* TLSv1.2 (OUT), TLS header, Finished (20):
* TLSv1.3 (OUT), TLS change cipher, Change cipher spec (1):
* TLSv1.2 (OUT), TLS header, Supplemental data (23):
* TLSv1.3 (OUT), TLS handshake, Finished (20):
* SSL connection using TLSv1.3 / TLS_AES_128_GCM_SHA256
* ALPN, server did not agree to a protocol
* Server certificate:
*  subject: CN=api.clevertap.com
*  start date: Jul 14 00:00:00 2025 GMT
*  expire date: Aug 12 23:59:59 2026 GMT
*  subjectAltName: host "api.clevertap.com" matched cert's "api.clevertap.com"
*  issuer: C=US; O=Amazon; CN=Amazon RSA 2048 M04
*  SSL certificate verify ok.
* TLSv1.2 (OUT), TLS header, Supplemental data (23):
> POST /1/events/counts.json HTTP/1.1
> Host: api.clevertap.com
> User-Agent: curl/7.81.0
> Accept: */*
> X-CleverTap-Account-Id: 7K7-8R4-Z74Z
> X-CleverTap-Passcode: UHQ-KSA-STAL
> Content-Type: application/json
> Content-Length: 99
> 
* TLSv1.2 (IN), TLS header, Supplemental data (23):
* TLSv1.3 (IN), TLS handshake, Newsession Ticket (4):
* TLSv1.2 (IN), TLS header, Supplemental data (23):
* Mark bundle as not supporting multiuse
< HTTP/1.1 405 Method Not Allowed
< Content-Type: text/html;charset=iso-8859-1
< Content-Length: 523
< Connection: keep-alive
< Date: Tue, 22 Jul 2025 11:14:03 GMT
< Cache-Control: must-revalidate,no-cache,no-store
< X-Cache: Error from cloudfront
< Via: 1.1 ********************************.cloudfront.net (CloudFront)
< X-Amz-Cf-Pop: BOM54-P1
< X-Amz-Cf-Id: u1tdtJQYL_TizY1pMYHasHRpwac93p7rJHm4Q2rExD_IAf_AIK8-VQ==
< 
<html>
<head>
<meta http-equiv="Content-Type" content="text/html;charset=ISO-8859-1"/>
<title>Error 405 HTTP method POST is not supported by this URL</title>
</head>
<body><h2>HTTP ERROR 405 HTTP method POST is not supported by this URL</h2>
<table>
<tr><th>URI:</th><td>/1/events/counts.json</td></tr>
<tr><th>STATUS:</th><td>405</td></tr>
<tr><th>MESSAGE:</th><td>HTTP method POST is not supported by this URL</td></tr>
<tr><th>SERVLET:</th><td>com.wizrocket.lc.unused.CatchAllServlet</td></tr>
</table>

</body>
</html>
* Connection #0 to host api.clevertap.com left intact
