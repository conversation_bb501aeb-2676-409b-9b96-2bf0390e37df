class MirrawAdmin::DesignGradingsController < MirrawAdmin::BaseController
	before_action :authenticate_user

  def index
    @grading_tags = GradingTag
                      .filter_by_app_name(params[:app_name])
                      .filter_by_country_code(params[:country_code])
                      .filter_by_platform(params[:platform])
                      .filter_by_taggable_type(params[:grading_taggable_type], params[:grading_taggable_id])
                      .filter_by_rpv_enabled(params[:rpv_enabled])
                      .order(created_at: :desc)
                      .paginate(page: params[:page], per_page: 10)
  
    if request.xhr?
      render partial: 'grading_tags', locals: { grading_tags: @grading_tags }
    else
      render :index
    end
  end
  
  def fetch_grading_taggable_ids_and_names
    case params[:type]
    when 'Category'
      render json: Category.order(:name).pluck(:id, :name).map { |id, name| { id: id, name: name } }
    when 'Designer'
      designers = Designer.where(state_machine: ['review', 'approved'])
                    .where(banned: [nil, false])
                    .order(:name)
                    .pluck(:id, :name)
      render json: designers.map { |id, name| { id: id, name: name } }
    when 'Collection'
      collection_tags = ActsAsTaggableOn::Tag.joins(:taggings)
                                             .where(taggings: { taggable_type: 'Design', context: 'collections' })
                                             .distinct
                                             .order(:name) # Sort by name
                                             .pluck(:id, :name)
      render json: collection_tags.map { |id, name| { id: id, name: name } }
    when 'Catalogue'
      catalogue_tags = ActsAsTaggableOn::Tag.joins(:taggings)
                                            .where(taggings: { taggable_type: 'Design', context: 'catalogues' })
                                            .distinct
                                            .order(:name) # Sort by name
                                            .pluck(:id, :name)
      render json: catalogue_tags.map { |id, name| { id: id, name: name } }
    else
      render json: []
    end
  end
  

  def new
    @grading_tag = GradingTag.new
  end

  def create
    platform = params[:platform]
    country_code = params[:country_code]
    grading_taggable_type = params[:grading_taggable_type]
    grading_taggable_id = params[:grading_taggable_id]
    app_name = params[:app_name]
    rpv_enabled = params[:rpv_enabled]
    # Generate a unique name based on provided parameters
    name = "#{app_name.downcase}_#{platform.downcase}_#{country_code.downcase}_#{grading_taggable_type}_#{grading_taggable_id}"

    # Check if the GradingTag with the same name already exists
    existing_grading_tag = GradingTag.find_by(name: name)
  
    if existing_grading_tag
      flash[:notice] = "Grading tag with this name already exists. Please choose a different combination."
      render :new
    else
      # Create the grading tag if no duplicate exists
      @grading_tag = GradingTag.new(
        app_name: app_name,
        platform: platform,
        country_code: country_code,
        grading_taggable_type: grading_taggable_type,
        grading_taggable_id: grading_taggable_id,
        rpv_enabled: rpv_enabled
      )
      if @grading_tag.save
        flash[:success] = "Grading tag was successfully created having #{grading_taggable_type} id-#{grading_taggable_id} and name - '#{@grading_tag.name}'"
        redirect_to mirraw_admin_design_gradings_path
      else
        flash[:notice] = 'Failed to create grading tag. Please check the errors below.'
        render :new
      end
    end
  end

  def upload_grading_tag
    grading_tag = GradingTag.find_by(id: params[:id])
    csv_file = params[:grading_tag][:csv_file]
    if csv_file.present? && csv_file.content_type == 'text/csv'
      begin
        directories = AwsOperations.get_directory(bucket: 'design-grade-bucket', new_connection: false)
        filename = "designgrading-#{Time.now.strftime("%m-%d-%Y")}-#{current_account.email}-#{csv_file.original_filename}"
        AwsOperations.create_aws_file(filename, csv_file)
        csv_file_path = AwsOperations.get_aws_file_path(filename)
        grading_log = GradingLog.create(
          grading_tag_id: grading_tag.id,
          file_path: csv_file_path,
          status: 'pending',
          created_by: current_account.email,
          message: ''
        )
        DesignGradingUploadJob.perform_async(grading_tag.id, csv_file_path, grading_log.id, current_account.email)
        
        flash[:success] = "CSV file Uploaded successfully for grading tag name - #{grading_tag.name} .It will take some time to reflect the changes"
        redirect_to mirraw_admin_design_gradings_url(id: grading_tag.id)
      rescue => exception
        MirrawAdminMailer.send_progress_notification("Design Grading Failed due to #{exception}").deliver_now!
        flash[:notice] = "Error: Please upload a valid CSV file. Error: #{exception}"
        redirect_to mirraw_admin_design_gradings_url(id: grading_tag.id)
      end
    else
      flash[:notice] = "Please upload a valid CSV file."
      redirect_to mirraw_admin_design_gradings_url(id: grading_tag.id)
    end
  end

  def show
    @grading_tag = GradingTag.find_by(id: params[:id])
    if @grading_tag.nil?
      flash[:notice] = "Grading tag not found."
      redirect_to mirraw_admin_design_gradings_url and return
    end
    if params[:design_id].present?
      @design_gradings = DesignGradingTag.where(grading_tag_id: @grading_tag.id, design_id: params[:design_id])
                                         .order(rank: :desc)
                                         .paginate(page: params[:page], per_page: 10)
    else
      @design_gradings = DesignGradingTag.where(grading_tag_id: @grading_tag.id)
                                         .order(rank: :desc)
                                         .paginate(page: params[:page], per_page: 10)
    end
  end

  def export_csv
    @grading_tag = GradingTag.find_by(id: params[:id])
    if @grading_tag.nil?
      flash[:notice] = "Grading tag not found."
      redirect_to mirraw_admin_design_gradings_url and return
    end
    @design_gradings = DesignGradingTag.where(grading_tag_id: @grading_tag.id).order(rank: :desc)
    csv_data = CSV.generate(headers: true) do |csv|
      csv << ['design_ids', 'ranks']  # Column headers
      @design_gradings.find_each do |grading|
        csv << [grading.design_id, grading.rank]  # CSV data
      end
    end
    send_data csv_data, filename: "design_gradings_#{@grading_tag.name}.csv", type: 'text/csv', disposition: 'attachment'
  end

  def edit
    @grading_tag = GradingTag.find_by(id: params[:id])
    unless @grading_tag
      flash[:notice] = "Grading tag not found."
      redirect_to mirraw_admin_design_gradings_path
    end
  end
  
  def update
    @grading_tag = GradingTag.find_by(id: params[:id])
    unless @grading_tag
      flash[:notice] = "Grading tag not found."
      redirect_to mirraw_admin_design_gradings_path and return
    end
  
    if @grading_tag.update(rpv_enabled: params[:grading_tag][:rpv_enabled] == "1")
      flash[:success] = "Grading tag updated successfully."
      redirect_to mirraw_admin_design_gradings_path
    else
      flash[:error] = "Failed to update grading tag."
      render :edit
    end
  end

  
	private
		def authenticate_user
			unless ACCESSIBLE_EMAIL_ID['manual_grading_panel_access'].to_a.include?(current_account.try(:email))
        redirect_to root_path, notice: 'You are not authorized to access this page.'
      end
    end
end