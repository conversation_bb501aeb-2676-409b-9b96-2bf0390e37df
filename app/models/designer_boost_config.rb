class DesignerBoostConfig < ActiveRecord::Base
  belongs_to :designer

  validates :designer_id, presence: true, uniqueness: true
  validates :boost_fee, numericality: { greater_than_or_equal_to: 0 }
  validates :duration, numericality: { only_integer: true, greater_than_or_equal_to: 0 } # Note: `duration` might become less relevant for boost_end_time if it's user-selected, but still good to have.
  validate :duration_must_be_multiple_of_12 # This validation can remain if `duration` is still used for other purposes.

  # Assuming daily_category_slots is a JSONB column (PostgreSQL)
  # or serialized as Hash for other databases.
  # If it's a simple hash stored in a text column, you might need:
  # serialize :daily_category_slots, Hash

  def name
    "Designer ##{designer_id}"
  end

  private

  def duration_must_be_multiple_of_12
    errors.add(:duration, "must be a multiple of 12") if duration.present? && duration % 12 != 0
  end
end