# == Schema Information
#
# Table name: carts
#
#  id         :integer         not null, primary key
#  created_at :datetime
#  updated_at :datetime
#  email      :string(255)
#
class Cart < ActiveRecord::Base
  include LocalyticsNotification
  #attr_accessible :email, :hash1
  has_many :line_items
  belongs_to :coupon
  belongs_to :wallet
  belongs_to :user, polymorphic: true
  has_many :designs, through: :line_items
  has_one :push_engage_subscriber, -> {order "id DESC"}, inverse_of: :cart
  has_one :gokwik_data
  after_initialize :set_default_user_type
  scope :all_rel, -> { includes([:line_items => [:variant, :design => [:images, :categories, :dynamic_prices, :sized_designs,{ designer: :seller_campaigns },:design_campaign_discounts ], :line_item_addons => [:size_chart, addon_type_value: [:addon_option_types,:addon_type_value_group]]]] ) }
  scope :user,-> { where("user_type = 'User' or user_type is null") }
  scope :current_month, -> { where(created_at: Date.today.prev_month.beginning_of_day..Date.today.end_of_day) }
  validates :email,
            :format => { :with => /\A([^@\s]+)@((?:[-a-z0-9]+\.)+[a-z]{2,})\z/i }, :if => Proc.new { email.present?}

  LOCALYTICS_BATCH_SIZE = SystemConstant.get('LOCALYTICS_BATCH_SIZE').to_i
  ABANDONED_CART_COUPON = SystemConstant.get('ABANDONED_CART_COUPON') #value of this constant should be the name of respective cart coupon
  ALLOWED_CART_COUPON_COUNTRIES = SystemConstant.get('ALLOWED_CART_COUPON_COUNTRIES').to_s.split(',')
  attr_accessor :country_code

  def add_product(design: nil, variant: nil, pair_product: nil)
    unless (current_item = self.line_items.find{|li| li.design_id ==  design.try(:id) && li.variant_id == variant.try(:id)}).present?
      current_item = LineItem.new(design: design, snapshot_price: (variant || design).effective_price(RETURN_NORMAL), scaling_factor: design.get_scale, vendor_selling_price: (variant || design).get_vendor_selling_amount, pair_product: pair_product)
      line_items << current_item
    end
    current_item
  end

  def designer_stores
    line_items = self.line_items.includes(design: [{ designer: :seller_campaigns },:design_campaign_discounts,:categories])
    designer_stores = line_items.sort{|li_a,li_b| li_b.created_at <=> li_a.created_at}.group_by { |li| li.design.designer.name }  # Mapping of name and line items
    stores = designer_stores.map { |name, items| {:name => name, :items => items, :cod => items.first.design.designer.cod, :shipping => items.sum(&:sub_total) > MIN_TOTAL_PER_STORE ? 0 : SHIPPING_CHARGE
    }}
  end

  def ready_to_ship_designs?
    line_items.all? { |item| item.design.ready_to_ship? && !item.paid_addons? }
  end

  def warehouse_available_designs?
    line_items.all? { |item| (item.variant.presence || item.design).sor_available? }
  end

  def all_products_in_warehouse?
    line_items.all? { |item| (item.variant.presence || item.design).sor_available? || (item.design.designer_id  == 12727) }
  end

  def coupon_discounts
    return 0 if coupon.nil?
    coupon.apply_coupon(self)
  end

  def additional_discounts
    discount_percent, amount = Promotions.additional_discount_percent(Design.country_code)
    total = self.items_total_without_addons(1) - self.coupon_discounts.to_f
    additional_discounts = 0
    if (bmgn_discounts = self.bmgnx_discounts) > 0
      additional_discounts += bmgn_discounts
    elsif (qpm_disc_percent = self.qpm_disc_percent) > 0
      additional_discounts += total * qpm_disc_percent.to_f / 100.0
    end
    total -= additional_discounts
    promo_additional_discounts = 0
    if coupon.nil?
      discount_percent.each_with_index do |discount,index|
        if (discount_percent[index].to_i > 0) && (total > amount[index].to_i)
          promo_additional_discounts = total * discount_percent[index].to_i / 100.0
        end
      end
    end
    additional_discounts += promo_additional_discounts
    additional_discounts.round
  end

  def bmgnx_discounts(bmgnx_hash = PromotionPipeLine.bmgnx_hash, return_type: RETURN_SCALED)
    bmgnx_hash.present? ? get_free_items_bmgnx(bmgnx_hash, return_type).sum{|k,v| v[0] * v[1] * (bmgnx_hash[:x]/100.to_f)} : 0
  end

  def details_for_gtm
    {cartProductIds: design_ids.join(',')}
  end

  def shipping_inr_conversion(price, currency_code)
    market_rate = CurrencyConvert.find_by_country_code(currency_code).try(:market_rate) || 1
    (price * market_rate).round(2)
  end

  def get_free_items_bmgnx(bmgnx_hash = nil, return_type = RETURN_SCALED)
    total_quantity, bmgnx_items_hash = 0, {}
    line_items.each do |item|
      if item.buy_get_free == 1
        bmgnx_items_hash[item.id] = [item.snapshot_price(return_type), item.quantity]
        total_quantity += item.quantity
      end
    end
    if bmgnx_items_hash.present?
      bmgnx_items_hash = bmgnx_items_hash.sort_by {|k,v| -v[0]}.to_h
      bmgnx_hash = PromotionPipeLine.bmgnx_hash unless bmgnx_hash.present?
      factor = ((total_quantity + bmgnx_hash[:n]) / (bmgnx_hash[:m] + bmgnx_hash[:n])).to_i
      mx, nx, count = factor * bmgnx_hash[:m], factor * bmgnx_hash[:n], 0
      # total_charged_quantity are items with charge as full amount (mx)  
      total_charged_quantity = total_quantity > (mx + nx) ? (total_quantity - nx) : mx  
      # remove first mx items and keep only last nx items which are eligible for offer
      bmgnx_items_hash.each do |k,v|
        remaining = total_charged_quantity - count
        if count < total_charged_quantity
          if v[1] <= remaining
            count += v[1]
            bmgnx_items_hash.delete(k)
          else
            count += remaining
            v[1] -= remaining
            break
          end
        end
      end
    end
    bmgnx_items_hash
  end

  def get_bmgnx_notice
    bmgnx_hash = PromotionPipeLine.bmgnx_hash
    if (total_quantity = line_items.inject(0){|sum, item| sum += item.quantity if item.buy_get_free == 1; sum}) > 0
      factor = ((total_quantity + bmgnx_hash[:n]) / (bmgnx_hash[:m] + bmgnx_hash[:n])).to_i
      round_quantity = (bmgnx_hash[:m] + bmgnx_hash[:n]) * factor
      discount_type = bmgnx_hash[:x] != 100 ? "at #{bmgnx_hash[:x]} % off" : 'free'
      if total_quantity > round_quantity && total_quantity <  bmgnx_hash[:m] + round_quantity
        quantity_need_to_add = bmgnx_hash[:m] + round_quantity - total_quantity
        "Add #{quantity_need_to_add} more #{'item'.pluralize(quantity_need_to_add)} to be eligible for the B#{bmgnx_hash[:m]}G#{bmgnx_hash[:n]} offer."
      elsif total_quantity >= round_quantity - bmgnx_hash[:n] && total_quantity < round_quantity
        "You have added #{total_quantity} #{'item'.pluralize(bmgnx_hash[:n])} in the cart. Add #{round_quantity - total_quantity} more #{'item'.pluralize(round_quantity - total_quantity)} to avail B#{bmgnx_hash[:m]}G#{bmgnx_hash[:n]}!"
      end
    end
  end

  def additional_discount_on_amount
    discount_percent, amount = Promotions.additional_discount_percent(Design.country_code)
  end

  # call with force_update = true if discount_may_have changed due to modifications
  def discounts(force_update=false)
    @discounts = (!force_update && @discounts) || (self.coupon_discounts + self.additional_discounts).round(2)
  end

  def wallet_discounts(type=nil)
    if (wallet.present? && wallet.currency_convert.country_code == Design.country_code)
      referral_discount = (type.nil? || type == :referral) ? referral_amount : 0
      return_discount = (type.nil? || type == :return) ? wallet.total_amount(:return) : 0
      referral_discount + return_discount
    end.to_f
  end

  def applicable_referral_amount
    # fetch amount dynamically from users wallet
    @referral_amount ||= if user.present? && user.is_a?(User) && user.wallet.present?
      conversion_rate = CONVERSION_RATES[user.wallet.currency_convert.country_code]
      user.wallet.usable_referral_amount(total_price_currency(conversion_rate, :none)).round(2)
    else
      0
    end
  end

  def referral_applied?
    #NOTE: currently cart's wallet is used just to check if referral is applied or not
    wallet.present? && wallet.referral_amount > 0
  end

  def referral_amount
    referral_applied? ? applicable_referral_amount : 0
  end

  def item_total
    line_items.to_a.sum(&:sub_total) + designer_stores.sum { |store| store[:shipping] }
  end

  def vacation_day_count
    list = [0]
    line_items.each do  |item|
      vendor = item.design.designer
      list << vendor.vacation_days_count if vendor.vacation_mode_on?
    end
    list.max
  end

  # to get total amount do not pass wallet type but in case you want to check free shipping or coupons which depend on cart value use :referral as 2nd option
  # this is to ensure that only referral money is used as discount and not the return money
  # check wallet_discount before changing this method
  def total_price_currency(conversion_rate = 1, wallet_type = nil)
    total = items_total_price(conversion_rate).round(2) - (self.discounts/conversion_rate).round(2)
    wallet_type == :none ? total : total - wallet_discounts(wallet_type)
  end

  def get_total_price_with_discount
    total = items_total_price(conversion_rate).round(2) - (self.discounts/conversion_rate).round(2)
  end

  def get_all_the_cart_details(country_code, actual_country, shipping = nil, conversion_rate = 1, wallet_type = nil, gift_wrap = false, subscription_applied = false)
    total = items_total_price(conversion_rate).round(2) - (self.discounts/conversion_rate).round(2)
    gift_wrap_price = gift_wrap && GIFT_WRAP_PRICE.to_f > 0 ? (GIFT_WRAP_PRICE.to_f/conversion_rate).round(2) : 0
    if !shipping
      shipping = actual_country.downcase != 'india' ? get_international_shipping_cost(conversion_rate,  actual_country) : ((Country.shipping_cost_for(nil,'India',total, line_items).to_f / conversion_rate).to_f).round(2)
    end
    total = total + shipping + gift_wrap_price
    tax_rate = get_tax_rate(country_code) 
    total_tax = (total * tax_rate)
    total = total + total_tax
    wallet_discount = wallet_discounts(wallet_type)
    total = wallet_type == :none ? total : total - wallet_discounts(wallet_type)
    total = 0 if total < 0
    platform_fee = 0
    # If subscription is applied, platform fee is waived off
    unless subscription_applied
      platform_fee = get_platform_fee(country_code, conversion_rate)
      total += platform_fee if platform_fee.present? && !subscription_applied
    end
    return [total,total_tax,shipping, wallet_discount, platform_fee] 
  end
  
    
  def get_platform_fee(country_code, conversion_rate)
    total =  self.items_total_without_addons(conversion_rate)
    PlatformFee.for_country(country_code, total).try(:price)  
  end

  def get_tax_rate(country_code)
    Rails.cache.fetch("tax_rate_#{country_code}", expires_in: 12.hours){
    country = Country.find_by_iso3166_alpha2 country_code
    country.tax_rate.present? && country.tax_enable ? (country.tax_rate.to_f / 100.0) : 0.0
    }
  end

  def total_price_currency_without_addons(conversion_rate = 1, wallet_type = nil)
    total = items_total_without_addons(conversion_rate).round(2) - (self.discounts/conversion_rate).round(2)
    wallet_type == :none ? total.round(2) : (total - wallet_discounts(wallet_type)).round(2)
  end

  # call with force_update = true if content in cart has changed due to modifications
  def items_total_price(conversion_rate = 1, force_update=false)
    @items_total_price ||= {}
    @items_total_price[conversion_rate] = (!force_update && @items_total_price[conversion_rate]) || begin
      total = 0
      self.line_items.each do |item|
        total += (item.snapshot_price/conversion_rate).round(2) * item.quantity
        item.line_item_addons.each do |addon|
          total += (addon.snapshot_price/conversion_rate).round(2) * item.quantity
        end
      end
      total
    end
  end

  def addons_total(conversion_rate = 1)
    (self.line_items.flatten.sum{ |item| (item.quantity * item.line_item_addons.flatten.sum{|addon| (addon.snapshot_price/conversion_rate).round(2)})}).round(2)
  end

  def items_total_without_addons(conversion_rate = 1, line_item_array = [])
    line_items = line_item_array.present? ? line_item_array : self.line_items
    total = 0
    line_items.each do |item|
      total += (item.snapshot_price/conversion_rate).round(2) * item.quantity
    end
    total.round(2)
  end

  def recommendations(conversion_rate,country)
    designs = []
    if CART_RECOMM_ENABLE == "true"
      free_shipping_rate = self.free_shipping_on_country?(country) ? self.get_free_shipping_rate(1, country) : 0
      total = items_total_price - discounts
      if self.line_items.count > 0 && free_shipping_rate > 0 && (recommended_price = free_shipping_rate - total) > 0
        designs = Rails.cache.fetch("recommended_bestsellers_designs_#{recommended_price.to_i}", expires_in: 3.hours) do
          bestseller_designs = Bestseller.bestsellers_by_price(10,recommended_price)
          Design.includes(:images,:designer,:categories).where(id: bestseller_designs).entries || []
        end
      end
    end
    return designs
  end

  def cart_cod_serviceable?(pincode)
    designer_ids = Cart.select('designers.id').joins(:line_items => [:design => :designer]).where(:id => self.id)
    designers = Designer.where(:id => designer_ids)
    response  = {cod: true}
    designers.each do |designer|
      if designer.cod
        if pincode.present?
          unless designer.can_cod?(pincode)
            response[:cod] = false
            break
          end
        end
      else
        response[:cod] = false
        break
      end
    end
    self.cod_available = response[:cod]
    self.save
  end

  def shipping
    designer_stores.sum { |store| store[:shipping]}
  end
  
  def total_items
    total = 0
    if self.id != -1 && self.line_items.present?
      self.line_items.each {|lt| total += lt.quantity}
    end
    total
  end

  def get_international_shipping_cost(conversion_rate, country, is_free_shipping=false)
    if coupon.present? && coupon.is_shipping? && total_price_currency(1, :referral) >= coupon.min_amount && !is_free_shipping
      cost = 0.0
    else
      weight = is_free_shipping ?  get_category_weight : get_weight
      cost = Country.shipping_cost_for(weight, country)
    end
    return (cost/conversion_rate).round(2)
  end

  def shipping_categories_available?
    skip_free_shipping = self.line_items.any?{|line_item| (line_item.design.categories.pluck(:id) & EXCLUDE_FREE_SHIPPING_CATEGORIES).present?}
  end

  def cart_has_bmgn_products?
    line_items.where(buy_get_free: 1).present? && PromotionPipeLine.bmgnx_hash.present?
  end

  def get_international_shipping_cost_in_currency_with_symbol(conversion_rate,symbol,country)
    symbol +' '+get_international_shipping_cost(conversion_rate, country).to_s
  end
  
  def get_category_weight
    value = 0
    line_items.each do |line_item|
      value += line_item.approx_weight if ((line_item.design.categories.pluck(:id) & EXCLUDE_FREE_SHIPPING_CATEGORIES).present? || [1,2,3].include?(line_item.buy_get_free))
    end
    value
  end

  def get_weight
    line_items.to_a.sum(&:approx_weight)
  end

  def is_luxe_cart?
    self.app_name=='luxe'
  end


  def total_price_currency_for_exclude_shipping(conversion_rate = 1)
    line_items = self.line_items.map{|line_item| line_item if (line_item.design.categories.pluck(:id) & EXCLUDE_FREE_SHIPPING_CATEGORIES).blank? && ![1,2,3].include?(line_item.buy_get_free)}.compact
    total = items_total_without_addons(conversion_rate, line_items)
    (total - wallet_discounts(:referral)).round(2)
  end

  def check_out_of_stock?(country = nil)
    self.line_items.each do |item|
      return true if (design = item.design).on_hold?
      return true if design.designer.inactive_designer_state?
      klass = item.variant || item.design
      # Enable it when you are maintaining warehouse instock
      if false #country.try(:downcase) == 'india' && item.quantity > klass.designer_quantity
        return true
      elsif item.quantity > klass.quantity
        return true
      end
    end
    return false
  end

  def remove_oos_items
    oos_items = Array.new
    self.line_items.each do |item|
      if item.design.designer.inactive_designer_state?
        item.cart_id = nil
        item.save!
        oos_items << item
      elsif item.variant.present?
        if item.variant.quantity <= 0
          item.cart_id = nil
          item.save!
          oos_items << item
        elsif item.quantity > item.variant.quantity
          item.quantity = item.variant.quantity
          item.save!
          oos_items << item
        end
      else
        if item.design.quantity <= 0
          item.cart_id = nil
          item.save!
          oos_items << item
        elsif item.quantity > item.design.quantity
          item.quantity = item.design.quantity
          item.save!
          oos_items << item
        end
      end
    end
    if oos_items.count > 0
      self.save!
      return oos_items
    else
      return false
    end
  end


  # input params - none
  # Please keep logic consistent with designer_order method - same named
  #
  # Returns true if mirraw_payable_addons present? else false
  def mirraw_payable_addons?     
    addons = LineItemAddon.joins(:line_item).where(:line_item => {:cart_id => self.id}, :snapshot_payable_to => 'mirraw').count(:id)
    addons > 0 ? true : false
  end

  def get_user_message(design, account_id, country_code, coupon)
    name = 'You can'
    if account_id && (user = Account.find_by_id(account_id).user).present?
      name = user.short_name
    end
    img = design.master_image ? design.master_image.photo(:large_m) : design.images.first.photo(:large_m)
    if ALLOWED_CART_COUPON_COUNTRIES.include?(country_code) && api_notification_count.to_i > 2 && coupon
      discount = (percent_off = coupon.percent_off) > 0 ? "#{percent_off}%" : coupon.flat_off
      img = Collection.find_by_title('ABANDONED_CART_COUPON_CODE').try(:banner).try{ |b| b.url(:original) } || img  #do not change title (i.e ABANDONED_CART_COUPON_CODE) of collection
      msg = {
              title:  "Use #{coupon.code} coupon code to get #{discount} off on your cart!",
              body: "#{name} go grab it before offer runs out"
            }
    elsif country_code && (response = promotion_message(country_code)).first
      msg = {
              title: "#{JSON.parse(response.last.variables_hash)['global_discount_percent']}% off on #{design.categories.first.p_name.singularize} in your cart for limited time!",
              body: "#{name} go grab it before offer runs out"
            }
    else
      msg = {
              title: "The #{design.categories.first.p_name.singularize} in your cart is a fabulous choice!",
              body: "#{name} go grab it by placing the order"
            }
    end
    return msg,img
  end

  def promotion_message(country_code)
    if (promotions = PROMOTIONS_FOR_CART.split(',')).present? && (active_promotions = PromotionPipeLine.active_promotions.where(name: promotions)).present?
      active_promotions.each do |p|
        return true, p if p.country_code.split(',').include?(country_code)
      end
      return false, nil
    else
      return false, nil
    end
  end


  def cart_message(target_id, msg, img, utm_campaign = 'Abandoned_Cart_Notification')
    extra = {
      bundleFrom: "localytics",
      type: "Cart",
      PushImageHandler: img,
      ignore_threshold: true,
      notificationMainTitle: msg[:title],
      cart_id: self.id,
      utm_source: 'android_or_ios',
      utm_medium: 'auto_notification',
      utm_campaign: utm_campaign
    }

    {
      target: target_id.to_s,
      alert: msg.stringify_keys,
      android: {extra: extra.stringify_keys}.stringify_keys
    }.stringify_keys
  end

  def prepare_message(account_id, cart, country_code, coupon=nil)
    design = cart.line_items.first.design
    msg, img = get_user_message(design, account_id, country_code, coupon)
    FCM_NOTIFICATION ? [msg[:title], msg[:body], img] : [cart_message(account_id || cart.device_id, msg, img, 'Abandoned_Cart_Notification')]
  end

  def self.notification_image_url(img)
    "#{ActionController::Base.helpers.image_path(img)}"
  end

  def prepare_message_for_cart_timer(device_id, api_account_id)
    coupon = Coupon.find_by_name('user_cart_coupon')
    img = Cart.notification_image_url('Cart_Timer_Notify.png')
    title_msg = if api_account_id && (user = Account.find_by_id(api_account_id).user).present?
                  "Hey #{user.short_name}, Special Offer for you!"
                else
                  "Hey, Special Offer for you!"
                end
    body = "Coupon \"#{ coupon.code.upcase }\" | Get \"#{coupon.percent_off}%\" off | Ends in  #{CART_COUPON_CONSTANT['Cart_coupon_valid_Minutes']} minutes"
    if FCM_NOTIFICATION
      return [title_msg, body, img]
    else
      msg = {
              title: "#{title_msg}",
              body: body
            }
      return cart_message(api_account_id || device_id, msg, img, 'Abandoned_Cart_Timer_Notification')
    end
  end

  def self.send_cart_timer_notification(cart_id, app_version, app_source, device_id, api_account_id, fcm_registration_token=nil)
    app_versions = ALLOWED_APP_VERSIONS[10..-1] | ALLOWED_IOS_APP_VERSIONS[5..-1]
    cart = Cart.find_by_id(cart_id)
    if NOTIFICATIONS_ENABLE == 'true' && cart && device_id && app_versions.include?(app_version) && app_source != nil
      unless cart.used || cart.line_items.blank?
        msg = [cart.prepare_message_for_cart_timer(device_id, api_account_id)]
        if FCM_NOTIFICATION && app_source.exclude?('Designer')
          if api_account_id.present?
            account = Account.find(api_account_id)
            FirebaseNotification.fcm_push_notification(msg[0], msg[1], msg[2], account.fcm_registration_token, app_source)
          elsif fcm_registration_token.present?
            FirebaseNotification.fcm_push_notification(msg[0], msg[1], msg[2], fcm_registration_token, app_source)
          end
        else
          campaign_key = 'cart_timer_notification'
          ClevertapNotification.modify_and_push_notification(msg, campaign_key, app_source.downcase) if CLEVERTAP_NOTIFICATION == 'true'
        end
      end
    end
  end


  def self.send_notification(cart_id, app_version, app_source, account_id)
    app_versions = ALLOWED_APP_VERSIONS[10..-1] | ALLOWED_IOS_APP_VERSIONS[5..-1]
    cart = Cart.find_by_id(cart_id)
    country_code = ApiData.where(account_id: account_id).first.currency_convert.country_code if account_id
    cart.update_attribute(:notified, nil)
    if NOTIFICATIONS_ENABLE == 'true' && cart && (account_id || app_versions.include?(app_version)) && (app_source.include?('Android') || app_source.include?('iOS'))
      cart.update_attribute(:notified_at, 2.days.ago) if cart.notified_at.nil?
      unless cart.used || cart.line_items.blank? || cart.notified_at > 1.days.ago
        msg = cart.prepare_message(account_id, cart, country_code) if app_source.include?('Android') || app_source.include?('iOS')
        if FCM_NOTIFICATION
          account = Account.find_by_id(account_id)
          FirebaseNotification.fcm_push_notification(msg[0], msg[1], msg[2], account.fcm_registration_token, app_source) if account.try(:fcm_registration_token)
        else
          campaign_key = 'Unused_Cart_Since_1_Hr'
          LocalyticsNotification.customer_notification(msg, campaign_key, app_source) if app_source.include?('Android') && LOCALYTICS_NOTIFICATION == 'true'
          ClevertapNotification.modify_and_push_notification(msg, campaign_key, app_source.downcase) if app_source.include?('iOS') || CLEVERTAP_NOTIFICATION == 'true'
        end
        cart.update_attribute(:notified_at, Time.now)
      end
    end
  end


 def self.send_abandoned_cart_notification(notify_accounts)
    if notify_accounts && (account_ids = notify_accounts.keys.uniq.compact).present?
      app_versions = ALLOWED_APP_VERSIONS[10..-1] | ALLOWED_IOS_APP_VERSIONS[5..-1]
      coupon = Coupon.find_by_name(ABANDONED_CART_COUPON)
      country_wise_api_data = ApiData.where(account_id: account_ids, app_version: app_versions).select([:account_id, :app_source, :currency_convert_id]).group_by(&:currency_convert_id)
      country_wise_api_data.each do |currency_convert_id, values|
        delayed_hours = CurrencyConvert.find_by_id(currency_convert_id).try(:delayed_hours_for_notification).to_i
        account_ids = values.collect(&:account_id)
        if account_ids.length > 0
          app_source_wise_filtered_account = notify_accounts.slice(*account_ids).group_by {|k,v| v[:app_source]}
          app_source_wise_filtered_account.each do |app_source, values|
            values.each_slice(LOCALYTICS_BATCH_SIZE) do |batch|
              if FCM_NOTIFICATION
                batch.each do |account|
                  msg = account[1][:cart].prepare_message(account[0],account[1][:cart], account[1][:country_code],coupon)
                  FirebaseNotification.fcm_push_notification(msg[0], msg[1], msg[2], account.fcm_registration_token, app_source) if account.try(:fcm_registration_token) && app_source.exclude?('Designer')
                end
              else
                message = batch.collect do |account|
                            account[1][:cart].prepare_message(account[0],account[1][:cart], account[1][:country_code],coupon)
                          end
                LocalyticsNotification
                  .sidekiq_delay_until(delayed_hours.hours.from_now)
                  .customer_notification(
                    message.compact,
                    'Abandoned_Cart_Notification',
                    app_source
                  ) if app_source.include?('Android') && LOCALYTICS_NOTIFICATION == 'true'
                ClevertapNotification.sidekiq_delay_until(delayed_hours.hours.from_now).modify_and_push_notification(message.compact, 'Abandoned_Cart_Notification', app_source.downcase) if app_source.include?('iOS') || CLEVERTAP_NOTIFICATION == 'true'
                cart_ids = batch.collect {|account| account[1][:cart][:id]}
                Cart.where(id: cart_ids).update_all(['api_notification_count = coalesce(api_notification_count,0) + 1, notified_at = ?', DateTime.now])
              end
            end
          end
        end
      end
    end
  end

  #will return total for designer with essential flag 
  def self.get_designers_total_with_essential_flag(line_items)
    designer_hash={}
    line_items.each do |li|
        de = li.design 
        d = de.designer
        designer_hash[d.id] = {sum: 0, essential: false}  if !designer_hash.key?(d.id)
        designer_hash[d.id][:sum]+=(li.snapshot_price).round(2) * li.quantity
        designer_hash[d.id][:essential] = true if ESSENTIAL_DESIGNERS["designer_ids"].map(&:to_i).include?(d.id)
    end
    return designer_hash
  end

  def amazon_data(cart_data={})
    xml= to_amazon_xml(cart_data)
    xml_base64 = Base64.strict_encode64(xml)
    signature = PayWithAmazon.generate_signature(xml)
    "type:merchant-signed-order/aws-accesskey/1;order:#{xml_base64};signature:#{signature};aws-access-key-id:#{PayWithAmazon.aws_access_key}"
  end

  def amazon_rectify_cart(line_items)
    cart_rectification_hash = {}
    line_items.each do |item|
      id = item["ItemCustomData"]["LineItemId"].to_i
      cart_rectification_hash[id]={
        snapshot_price: item["Price"]["Amount"].to_i,
        quantity: item["Quantity"].to_i,
        vendor_selling_price: item["ItemCustomData"]["VendorSellingPrice"].to_i,
        variant_id: (var_id = item["ItemCustomData"]["VariantId"]).present? ? var_id.to_i : nil,
        design_id: item["SKU"].to_i,
        amazon_order_item_code: item["AmazonOrderItemCode"],
        note: item["ItemCustomData"]["Note"]
      }
    end
    rectify_cart(cart_rectification_hash,[:quantity,:amazon_order_item_code])
  end

  # clear already ordered items and create duplicates for them
  def refresh_ordered_items!
    if (ordered_items = line_items.select{|li| li.designer_order_id.present?}).present?
      line_items.delete(*ordered_items.collect(&:id))
      new_items = ordered_items.collect do |item|
        line_item = item.dup
        line_item.line_item_addons << item.line_item_addons.collect(&:dup)
        line_item.designer_order_id = nil
        line_item
      end
      line_items << new_items
    end
  end

  def generate_otp(phone, resend)
    if otp == 'verified' || otp.blank? || resend == 'false'
      return false unless update_attribute('otp', '%05d' % SecureRandom.random_number(100000))
    end
    phone = get_mobile_num(phone)
    if phone && phone != 0 && phone.length == 12
      template = "#{otp} is your verification code. Use this code to confirm your COD order. Thanks, Mirraw.com."
      return SmsNotification::NotificationService.notify(phone, template)
    end
    return false
  end

  def has_minimun_quantity
    minimum_quantity = PAY_WITH_AMAZON_MIN_QUANTITY.to_i
    line_items.all? do|line_item|
      (line_item.variant.try(:quantity) || line_item.design.quantity) > line_item.quantity + minimum_quantity
    end
  end

  def apply_shipping_coupon
    if coupon.present?
      if coupon.is_shipping? && total_price_currency(1, :referral) < coupon.min_amount
        coupon_id = nil
        errors.add(:coupon,'minimum amount not satisfied')
      end
    end
  end

  def show_stitching_offer?
    line_items.includes(:design).each do |line_item|
      return true if Design::STITCHING_ALLOWED_DESIGNABLE_TYPES.include?(line_item.design.designable_type)
    end
    false
  end

  def design_ids
    line_items.loaded? ? line_items.collect(&:design_id) : super
  end

  def free_stiching_coupon?
    if coupon.present?
      (coupon.is_stitching? && coupon.eligible?(self))
    end
  end

  def cart_addon_designs(country_code)
    design_ids = line_items.group_by(&:design_id)
    designable_types = line_items.map(&:design).map(&:designable_type).uniq
    geo = country_code.downcase == 'in' ? 'domestic' : 'international'
    CART_ADDON[geo].to_h.slice(*designable_types).map do |designable_type, design_ids|
      Rails.cache.fetch("#{designable_type}_designs_for_cart_addon",expires_in: 24.hours) do
        [designable_type, Design.includes(:designer, :master_img).where(id: design_ids, state: 'in_stock').uniq.to_a]
      end
    end.map do |designable_type, designs|
      [designable_type, designs.map do |design|
        design_ids[design.id].try(:first) || design
      end]
    end.to_h
  end

  def cart_addon_item(country_code)
    @cart_addon_item ||= cart_addon_designs(country_code).values.flatten.select do |item|
      item.is_a?(LineItem)
    end
  end

  include Promotions::Cart

  private

  def set_default_user_type
    self.user_type||='User' if user_id.present?
  end

  def rectify_cart(line_items_hash,check=[])
    line_items.each do |li|
      if(li_raw_data = line_items_hash.delete(li.id)).present?
        check.each do |attribute|
          li[attribute] = li_raw_data[attribute] if li.send(attribute) != li_raw_data[attribute]
        end
        li.save if li.changed?
      else # delete extra line items
        li.destroy
      end
    end
    remaining_line_item=[]
    line_items_hash.each do |key,value|
      line_item = LineItem.new(value)
      line_item.cart_id = id
      remaining_line_item << line_item
    end
    LineItem.import remaining_line_item,validate: false
  end

  def to_amazon_xml(cart_data)
    to_hash(cart_data).to_xml(root: 'Order',indent:0,skip_types: true).gsub!('<Order',"<Order xmlns='http://payments.amazon.com/checkout/2009-05-15/'")
  end

  def to_hash(cart_data)
    order={
      Cart: { Items: []},
      Promotions: [],
      ReturnUrl: PayWithAmazon.return_url,
      CancelUrl: PayWithAmazon.cancel_url
    }
    order[:Cart][:Items] = line_items.collect(&:to_amazon_hash)
    if (discount = discounts + wallet_discounts) > 0
      order[:Cart][:CartPromotionId] = "Mirraw discount"
      order[:Promotions]<<{
        PromotionId:"Mirraw discount",
        Benefit:{
          FixedAmountDiscount:{
            Amount: discount,
            CurrencyCode: 'INR'
          }
        }
      }
    end
    order[:Cart][:CartCustomData]={cart_id: id,total_amount: total_price_currency(1)}.merge!(cart_data)
    order
  end

  # Returns mobile number given differnet valid representation returns 0 for invalid representation
  def get_mobile_num(phone_num)
    phone = phone_num.tr(' +-', '')
    unless phone.match(/^[\d]{10,12}$/)
      return 0
    end
    plength = phone.length
    if plength == 10
      '91' + phone
    elsif plength == 11 && phone[0] == '0'
      '91' + phone[1..-1]
    elsif plength == 12 && phone[0..1] == '91'
      phone
    else
      0
    end
  end
end
