class PlatformFee < ActiveRecord::Base
  belongs_to :country

  validates :country_id, presence: true
  validates :price, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :min_order_value, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :max_order_value, presence: true, numericality: { greater_than_or_equal_to: 0 }


  scope :active, -> { where(status: 'active') }
  state_machine :status, initial: :active do
    state :active
    state :inactive

    event :deactivate do
      transition :active => :inactive
    end

    event :reactivate do
      transition :inactive => :active
    end
  end

  def self.for_country(country_code, amount)
    plans_by_country = Rails.cache.fetch("all_country_platform_fee", expires_in: 6.hours) do
      PlatformFee.active
                      .includes(:country)
                      .group_by { |plan| plan.country.iso3166_alpha2.upcase }
    end
  
    plans = plans_by_country[country_code] || []
    plans.find do |plan|
      amount >= plan.min_order_value && amount <= plan.max_order_value
    end
  end
end