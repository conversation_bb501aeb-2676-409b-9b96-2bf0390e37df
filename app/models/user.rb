class User < ActiveRecord::Base
  extend FriendlyId
  has_many :user_segment_rules, dependent: :destroy
  has_many :segment_rules, through: :user_segment_rules
  has_one :account, :as => :accountable
  has_one :juspay_customer
  has_many :carts, -> { where("carts.user_type = 'User' or carts.user_type is null") }
  has_many :friends
  has_many :addresses
  has_many :orders
  has_one :latest_order, -> { order("orders.confirmed_at DESC") }, foreign_key: :user_id, class_name: 'Order'
  has_many :returns
  belongs_to :wallet
  has_many :stitching_measurements
  has_many :kyc_documents
  has_many :reviews
  has_many :wishlists
  has_many :unordered_wishlist_designs, -> { where(wishlists: {wish: true, state: 'added'}).order('wishlists.updated_at desc') }, through: :wishlists, source: :design
  has_one :notification

  has_many :sent_gift_card_orders, foreign_key: :sender_user_id, class_name: 'GiftCardOrder'
  has_many :received_gift_card_orders, foreign_key: :recipient_user_id, class_name: 'GiftCardOrder'

  has_many :user_subscriptions, dependent: :destroy
  has_many :subscription_plans, through: :user_subscriptions
  has_one :active_subscription, -> { where(status: 'active').current }, class_name: 'UserSubscription'

  serialize :history
     
  acts_as_follower
  acts_as_followable
  
  friendly_id :first_and_last, use: :slugged

  paperclip_hash={
  storage: :s3,
  styles: {small: "225x257#",zoom: "800x1100"},
  s3_credentials: AWS_ACCESS_KEYS,
  path: ":class/:id/:basename_:style.:extension",
  bucket: ENV['S3_BUCKET'],
  url: ":s3_alias_url",
  s3_headers: { 'Cache-Control' => 'max-age=315576000', 'Expires' => 10.years.from_now.httpdate },
  s3_host_alias: "s3-#{ENV['AWS_REGION']}.amazonaws.com/#{ENV['S3_BUCKET']}"}

  has_attached_file :full_size_photo, paperclip_hash
  validates_attachment_size :full_size_photo, less_than: 4.megabytes
  validates_attachment_content_type :full_size_photo, content_type: ['image/jpeg', 'image/png', 'image/jpg']
  process_in_background :full_size_photo
  
  def segment_tag
    rule = segment_rules.where(active: true, rule_type: 'user').first if segment_rules.present?
    rule ? rule.tag_list_on(:segments).first : nil
  end
  
  # Helper method to check if user belongs to a specific segment
  def in_segment?(segment_name)
    segment_rules.where(active: true, name: segment_name).exists?
  end

  def first_and_last
    name.humanize
  end
  
  def name
    if first_name.present? && last_name.present?
      return first_name.camelize + " " + last_name.camelize
    elsif first_name.present? && last_name.blank?
      return first_name.camelize
    elsif first_name.blank? && last_name.present?
      return last_name.camelize
    else
      return email.split('@')[0]
    end
  end
  
  def short_name
    if first_name.present?
      return first_name
    elsif last_name.present?
      return last_name
    elsif email.present?
      return email.split(/[@,.,_,\d]/)[0]
    else
      return " "
    end
  end
  
  def normal_url
    if self.image_url.present?
      url, type =  self.image_url.split('?')
      normal_url = url + "?type=normal&width=150&height=150"
    else
      normal_url = "/assets/fb_default.jpg"
    end
  end
  
  def small_url
    if self.image_url.present?
      url, type =  self.image_url.split('?')
      normal_url = url + "?type=normal&width=75&height=75"
    else
      normal_url = "/assets/fb_default.jpg"
    end
  end
  
  def default_address
    self.addresses.where('addresses.default > 0').first
  end

  def add_item_to_history(id)
    self.history = [] unless self.history.present?
    self.history << id
    self.history.delete_at(0) if self.history.size >= 100
    self.save
  end


  def save_address_if_absent(order)
    # Check if given address doesn't exist
    found = false
    self.addresses.each do |address|
      if address.street_address.chomp == order.billing_street.chomp
        found = true 
        break
      end
    end
     
    unless found
      address = self.addresses.new
      address.name = order.billing_name
      address.phone = order.billing_phone
      address.pincode = order.billing_pincode
      address.street_address = order.billing_street
      address.country = order.billing_country
      address.state   = order.billing_state 
      address.city = order.billing_city
      address.save  
    end
  end

  def friends_count
    self.friends.count
  end

  def get_address
    return self.default_address if self.default_address.present?
    return self.addresses.first
  end

  def get_additional_keys(type, extra)
    case type
    when 'new_uploads'
      {
        designer_ids: extra,
        ignore_threshold: false
      }
    when 'hot_selling'
      {
        designs: extra,
        ignore_threshold: false
      }
    when 'new_coupons', 'expiring_coupons'
      {
        coupon_ids: extra,
        ignore_threshold: false
      }
    when 'followers_ratings'
      {
        review_ids: extra,
        ignore_threshold: false
      }
    when 'followers_wishlist'
      {
        wishlist_ids: extra,
        ignore_threshold: false
      }
    else
      {}
    end
  end

  def extra_params(img, msg, type)
    {
      notificationMainTitle: msg['title']
    }
  end

  def get_message(account_id, extra, img, msg, type)
    {
      'target' => account_id.to_s,
      'alert' => msg,
      'android' => {'extra' => { 'notificationMainTitle' => msg['title'] }}
    }
  end

  def get_user_name(msg, amt = nil)
    msg['body'] = "#{self.short_name} #{msg['body']}"
    msg['title'] = "#{amt} #{msg['title']}" if amt
    return msg
  end

  def notification_image_url(img)
    "#{ActionController::Base.helpers.image_path(img)}"
  end

  %w(users_wishlist users_ratings expiring_coupons new_coupons hot_selling_products new_uploads).each do |method_name|
    define_method("prepare_#{method_name}_message".to_sym) do |msg_key, account_id, extra, type, img = nil|
      LOCALYTICS_MESSAGES.replace YAML.load_file("#{Rails.root.to_s}/db/data/localytics_notification_messages.yml")
      img = img || notification_image_url('coupon.jpg')
      msg = get_user_name(LOCALYTICS_MESSAGES[msg_key])
      get_message(account_id, extra, img, msg, type)
    end
  end

  def send_wallet_notification(total, symbol, app_source)
    LOCALYTICS_MESSAGES.replace YAML.load_file("#{Rails.root.to_s}/db/data/localytics_notification_messages.yml")
    img = notification_image_url('coupon.jpg')
    app_source = app_source.to_s.split('-')
    app_source -= [app_source.last]
    app_source = app_source.join('-')
    msg = get_user_name(LOCALYTICS_MESSAGES['wallet_refund'], "#{symbol} #{total}")
    #LocalyticsNotification.customer_notification([] << get_message(self.account.id, nil, img, msg, 'refund_money_credited'), 'refund', app_source) if api_data.app_source.include?('Android') && LOCALYTICS_NOTIFICATION == 'true'
    if FCM_NOTIFICATION
      fcm_token = self.account.try(:fcm_registration_token)
      FirebaseNotification.fcm_push_notification(msg['title'], msg['body'], img, fcm_token, app_source) if fcm_token && app_source.exclude?('Designer')
    else
      ClevertapNotification.modify_and_push_notification([] << get_message(self.account.id, nil, img, msg, 'refund_money_credited'), 'refund', app_source.downcase) if CLEVERTAP_NOTIFICATION == 'true'
    end
  end
  
  def wallet_amounts
    self.wallet ||= assign_wallet(Design.country_code)
  end

  def assign_wallet(country_code)
    wallet = create_wallet(currency_convert_id: CurrencyConvert.where(country_code: country_code).first.id)
    update_column(:wallet_id, wallet.id)
    wallet
  end

  def can_review?(design_id)
    orders.where(state: ['sane','dispatched','complete','ready_for_dispatch','pickedup']).joins(:line_items).where('line_items.design_id = ?',design_id).exists?
  end

  def reward_pending_gift_cards
    if received_gift_card_orders.where(state: 'sane').exists?
      self.wallet ||= Wallet.create(currency_convert_id: CurrencyConvert.find_by_country_code(Design.country_code).id)
      wallet.reward_pending_gift_cards
    end
  end

  include ForceDowncaseWriters.new(:email)
end
