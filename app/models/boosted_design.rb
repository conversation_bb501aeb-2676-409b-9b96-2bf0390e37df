# app/models/boosted_design.rb
class BoostedDesign < ActiveRecord::Base
  belongs_to :design
  belongs_to :designer
  belongs_to :category # Ensure this association is correctly maintained if category_id is stored

  validates :design_id, presence: true
  validates :designer_id, presence: true
  validates :boost_fee, numericality: { greater_than_or_equal_to: 0 }
  validates :boost_start_time, presence: true # Now required directly

  # A boost is considered active if its state is 'active' AND
  # its end time is in the future or it has no end time (continuous).
  scope :active, -> {
    where(state: 'active')
    .where("boost_end_time IS NULL OR boost_end_time >= ?", Time.current)
  }

  # Upcoming boosts within the next day (e.g., for cron jobs to activate them)
  scope :upcoming_day, -> {
    where(state: 'scheduled')
      .where(boost_start_time: Time.current.change(hour: 12, min: 30)..Time.current.tomorrow.change(hour: 13, min: 0))
  }

  scope :scheduled, -> {
    where(state: 'scheduled')
  }

  scope :error, -> {
    where(state: 'error')
  }

  scope :completed, -> {
    where(state: 'expired')
  }

  # Finds boosts whose duration overlaps with the given start_time and end_time range
  scope :within_period, ->(start_time, end_time) {
    where("(boost_start_time >= ? AND boost_start_time < ?) OR
           (boost_end_time > ? AND boost_end_time <= ?) OR
           (boost_start_time <= ? AND (boost_end_time IS NULL OR boost_end_time >= ?))", # Adjusted for continuous boosts
          start_time, end_time,
          start_time, end_time,
          start_time, end_time)
  }

  # Counts active or scheduled boosts for a given category on a specific date.
  # This is crucial for determining available slots.
  def self.active_or_scheduled_count_for_category_on_date(category_id, check_date, exclude_boost_id = nil)
    # Ensure check_date is a Date object (if it's a DateTime/Time, strip time part)
    check_date = check_date.to_date

    # Define the 24-hour period for the check_date in IST (12:50 PM on check_date to 12:50 PM on check_date + 1 day)
    # This aligns with the "New boosts go live daily at 12:50 PM IST" and "boost will run ... until 12:50 PM IST the following day"
    # For simplicity of counting 'slots on a date', we consider a slot occupied if
    # any part of the 24-hour period (starting 12:50 PM) for `check_date` is occupied by a boost.
    # The current `active_or_scheduled_count_for_category_on_date` logic correctly
    # checks if a boost *overlaps* with the `check_date` (from start of day to end of day).
    # Since boosts are daily, if a boost starts on `check_date` or ends on `check_date` (or is continuous),
    # it occupies a slot for that `check_date`.

    # A boost occupies a slot on `check_date` if:
    # 1. It starts on or before `check_date` AND
    # 2. It ends on or after `check_date`, OR it has no end date (continuous).

    query = scheduled_or_active
              .where(category_id: category_id)
              .where(
                "boost_start_time <= ? AND (boost_end_time IS NULL OR boost_end_time >= ?)",
                check_date.end_of_day.in_time_zone('Asia/Kolkata'), # Boost must start on or before the end of check_date
                check_date.beginning_of_day.in_time_zone('Asia/Kolkata') # Boost must end on or after the start of check_date, or be continuous
              )

    if exclude_boost_id.present?
      query = query.where.not(id: exclude_boost_id)
    end

    query.count
  end

  # Scope to get boosts that are either scheduled or active
  scope :scheduled_or_active, -> { where(state: ['scheduled', 'active']) }


  state_machine :state, initial: :scheduled do
    event :activate do
      transition scheduled: :active
    end

    event :expire do
      transition active: :expired
    end

    event :cancel do
      transition scheduled: :canceled
    end

    event :boost_error do
      transition any => :error
    end
  end

  def boost_start_time_formatted
    boost_start_time.present? ? boost_start_time.in_time_zone('Asia/Kolkata').strftime('%m/%d/%Y') : nil
  end

  def boost_end_time_formatted
    boost_end_time.present? ? boost_end_time.in_time_zone('Asia/Kolkata').strftime('%m/%d/%Y') : nil
  end

end