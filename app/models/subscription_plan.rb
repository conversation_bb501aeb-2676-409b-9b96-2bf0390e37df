class SubscriptionPlan < ActiveRecord::Base
  belongs_to :country
  has_many :user_subscriptions, dependent: :destroy
  has_many :users, through: :user_subscriptions
  has_many :subscription_plan_services, dependent: :destroy
  has_many :subscription_services, through: :subscription_plan_services

  validates :name, presence: true
  validates :price, presence: true, numericality: { greater_than: 0 }
  validates :country_id, presence: true
  validates :plan_duration, presence: true, inclusion: { in: ['daily', 'weekly', 'monthly', '3_month', '6_month', 'annually'] }

  state_machine :status, initial: :active do
    state :active
    state :inactive

    event :deactivate do
      transition :active => :inactive
    end

    event :reactivate do
      transition :inactive => :active
    end
  end
end