class SubscriptionPlan < ActiveRecord::Base
  belongs_to :country
  has_many :user_subscriptions, dependent: :destroy
  has_many :users, through: :user_subscriptions
  has_many :subscription_plan_services, dependent: :destroy
  has_many :subscription_services, through: :subscription_plan_services

  validates :name, presence: true
  validates :price, presence: true, numericality: { greater_than: 0 }
  validates :country_id, presence: true
  validates :plan_duration, presence: true, inclusion: { in: ['daily', 'weekly', 'monthly', '3_month', '6_month', 'annually'] }

  state_machine :status, initial: :active do
    state :active
    state :inactive

    event :deactivate do
      transition :active => :inactive
    end

    event :reactivate do
      transition :inactive => :active
    end
  end

  def self.for_country(country_code, amount)
    plans_by_country = Rails.cache.fetch("all_subscription_plans", expires_in: 6.hours) do
      SubscriptionPlan.where(status: 'active')
                      .includes(:country)
                      .group_by { |plan| plan.country.iso3166_alpha2.upcase }
    end
  
    plans = plans_by_country[country_code] || []
    plans.find do |plan|
      amount >= plan.minimum_order_value && amount <= plan.maximum_order_value
    end
  end

  def self.for_id(id)
    Rails.cache.fetch("subscription_plan_#{id}", expires_in: 6.hours) do
      SubscriptionPlan.find_by_id(id).try(:attributes)
    end
  end
end