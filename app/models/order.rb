require 'item_search'

# == Schema Information
#
# Table name: orders
#
#  id                     :integer         not null, primary key
#  pay_type               :string(255)
#  buyer_id               :integer
#  created_at             :datetime
#  updated_at             :datetime
#  total                  :integer
#  shipping               :integer
#  state                  :string(255)
#  name                   :string(255)
#  email                  :string(255)
#  phone                  :string(255)
#  street                 :string(255)
#  city                   :string(255)
#  buyer_state            :string(255)
#  country                :string(255)
#  pincode                :string(255)
#  number                 :string(255)
#  notes                  :text
#  billing_name           :string(255)
#  billing_street         :string(255)
#  billing_email          :string(255)
#  billing_phone          :string(255)
#  billing_city           :string(255)
#  billing_state          :string(255)
#  billing_country        :string(255)
#  billing_pincode        :string(255)
#  ccavenue_authdesc      :string(255)
#  ccavenue_nb_order_no   :string(255)
#  ccavenue_nb_bid        :string(255)
#  ccavenue_card_category :string(255)
#  ccavenue_bank_name     :string(255)
#
class Order < ActiveRecord::Base
  # attr_accessible :billing_name, :billing_email, :billing_country, :billing_pincode,
  # :billing_street, :billing_city, :billing_state, :billing_phone, :name, :country,
  # :pincode, :street, :city, :buyer_state, :phone, :pay_type
  has_paper_trail
  acts_as_taggable
  acts_as_paranoid
  has_shortened_urls
  
  include SidekiqHandleAsynchronous
  include MobileGenericSidekiqConcern
  include HasBarcode
  include LocalyticsNotification
  include BulkQueue
  include CourierAutomation
  include StripeServices
  include Shortener::ShortenerHelper
  include Rails.application.routes.url_helpers
  extend GokwikConcern
  extend PaypalRelatedDetails
  include PostPaymentService
  require 'money'
  require 'money/bank/google_currency'
  require 'paytm/encryption_new_pg' # for paytm checksum geneartion and validation
  has_barcode :order_number_barcode,
    :outputter => :svg,
    :type => :code_128,
    :value => Proc.new { |p| p.number }
  default_scope { order('orders.confirmed_at DESC') }
  scope :unfraud, -> { where(state: [:new, :sane]) }
  scope :orders_in_three_months, -> { where(confirmed_at: 3.months.ago..DateTime.current) }
  scoped_search :on => [:number, :email, :name, :phone]
  scope :dated, -> { order('orders.id DESC') }
  scope :international_orders, -> { where("lower(country) <> 'india'") }
  serialize :order_notification, Hash
  # serialize :other_details, Hash
  # Remove payu money option temporary: PAYU_MONEY
  PAYMENT_OPTIONS = SystemConstant.get_config('payment_option')
  INT_PAYMENT_OPTIONS = PAYMENT_OPTIONS.select{|option, values| values['int_enable'] }.sort_by{|k,v| v['int_position']}.to_h
  DOM_PAYMENT_OPTIONS = PAYMENT_OPTIONS.select{|option, values| values['dom_enable'] }.sort_by{|k,v| v['dom_position']}.to_h

  PAY_TYPE_INFO = {COD => 'Cash on Delivery customers will receive a phone call to reconfirm your order before processing begins.',
  PAYMENT_GATEWAY => 'After clicking on the "Place Order" button, you will be directed to a secure payment gateway.',
  PAYPAL => 'You have selected Paypal payment option.',
  BANK_DEPOSIT => 'Payment must be made to the account within 3 days of placing the order. We shall email you bank details.',
  PAYU_MONEY => 'PayUmoney - Get Cash-Back on PayUmoney Website.',
  PAYTM => 'PAYTM - Pay with your paytm wallet. After clicking on the "Place Order" button, you will be directed to a secure payment gateway'}
  #PAY_TYPE_INFO[GHARPAY] = "Your product will be dispatched only after the payment has been collected by gharpay executive. " + gharpay_link_text
  NON_PRIORITY_COUNTRIES = Country.where('priority IS NULL').order('name ASC').pluck(:name)
  PRIORITY_COUNTRIES = Country.where('priority IS NOT NULL').order('priority ASC').pluck(:name)
  PAYMENT_TYPES = PAYMENT_OPTIONS.keys
  PAYPAL_ALLOWED_CURRENCIES = ['AUD', 'CAD', 'CZK', 'DKK', 'EUR', 'HKD', 'ILS', 'MXN', 'NOK', 'NZD', 'PHP', 'PLN', 'GBP', 'SGD', 'SEK', 'CHF', 'THB', 'USD'] + PAYPAL_PRESENTMENT_CURRENCIES
  INDIAN_CUSTOM_ALLOWED_CURRENCIES = ['AED','AUD','BHD','CAD','CHF','CNY','DKK','EUR','GBP','HKD','INR','JPY','KES','KWD','NOK','NZD','SAR','SEK','SGD','USD','ZAR']
  CONVERT_TO_USD_CURRENCIES = ['THB', 'PHP', 'PKR', 'BBD', 'MXN', 'LKR', 'MUR', 'TTD', 'FJD', 'GYD']
  ATLANTIC_SUPPORTED_CURRENCIES = ['GBP', 'USD', 'INR', 'AED', 'AUD', 'SGD', 'EUR', 'CAD', 'BHD','CNY', 'DKK', 'HKD','KWD','NZD','NOK','QAR','SAR', 'ZAR', 'SEK', 'CHF', 'TRY', 'MUR', 'MYR', 'LKR', 'FJD', 'TTD', 'GYD', 'NPR', 'KRW', 'KES', 'BND', 'MVR', 'KYD', 'EGP', 'TZS']
  EU_COUNTRIES = promise{ Country.where{time_zone =~ '%europe%'}.pluck('lower(name)')}
  EU_COUNTRY_CODE = promise{Country.where{time_zone =~ '%europe%'}.pluck('lower(iso3166_alpha2)') }
  ADDONS_FOR_STITCHING = ['Custom Blouse Stitching', 'Regular Blouse Stitching', 'Standard Stitching', 'Custom Stitching']
  CUSTOMS_VERIFICATION_COUNTRIES_LIST = ['ZA', 'SG', 'MY', 'KE', 'AE', 'MU']
  ORDER_NUMBER_REGEX =  /[mM][\d]{9}/
  Paypal.sandbox! unless Rails.env.production? || Rails.env.admin?
  PAYPAL_CONFIG = {
    username: ENV["PAYPAL_USERNAME"],
    password: ENV["PAYPAL_PASSWORD"],
    signature: ENV["PAYPAL_SIGNATURE"],
    sandbox: ENV['PAYPAL_PAYMENT_MODE']
  }
  COUNTRY_DIAL_CODE = Country.pluck(:name,:dial_code).to_h
  ADMIN_ORDER_CANCEL_REASON = [
    'Out of Stock',
    'Got better product at the same price',
    'Vendor canceled',
    'Tailor Issue',
    'Customer not interested', 
    'Duplicate order', 
    'Test order', 
    'Wrong price fetched', 
    'Order placed in INR', 
    'Cancel within 24 hr', 
    'Replacement not required', 
    'lost by courier', 
    'Disputed order',
    'Order delay',    
    'Operations call',
    'Warehouse Issue',
    'Quality Issue',
    'Plus size unable to stitch',    
    'Customer Requested - Not needed anymore',
    'Customer Requested - Other Reason'
  ]
  #1 -> RFD Block
  #2 -> Order Check Block
  #3 -> Both Block
  ORDER_RFD_BLOCK_TAG_TYPES = [1, 3]
  ORDER_CHECK_BLOCK_TAG_TYPES = [2, 3]
  ORDER_PARTIAL_DISPATCH_BLOCK_TAG_NAME_PREFIXES = ['dnd']

  #set true for cod_otp validation
  attr_accessor :validate_cod_otp
  attr_accessor :increment_item_quantity
  attr_accessor :skip_before_filter
  attr_accessor :is_duplicate
  attr_accessor :order_cancel_reason
  # @@per_page = 40
  before_save :deduct_amount_from_wallet, if: :new_record? # can not use before_create as must run before update_order_fields_trigger
  before_save :apply_subscription_fee
  before_save :update_order_fields_trigger, unless: :skip_before_filter
  # FIXME: +notification_count+ should return 0 by default
  before_save lambda { affiliate_order.payable if affiliate_order.present? && notification_count_was.to_i == 0 }, if: :notification_count_changed?
  after_create :update_design_score, if: -> (order){ order.multi_channel_marketing.to_s.include?('autopost')}
  after_create :update_coupon_used_on
  after_create lambda { |order| broadcast(:order_created, self.id) }
  validates_acceptance_of :newsletter_checkbox

  has_one :transaction_order
  has_one :gokwik_data
  has_one :order_estimation
  has_many :events, as: :eventable
  has_one :latest_event, -> {where('events.eventable_type = ?', 'Order').order('events.event_timestamp DESC')}, class_name: 'Event', foreign_key: :eventable_id
  has_many :shipments, -> { where(service_type: [nil,'ClickpostForward']) }
  has_many :export_shipments, -> { where(designer_order_id: nil, service_type: nil) }, class_name: 'Shipment'
  has_many :reverse_shipments, -> { where(designer_order_id: nil, service_type: 'ReverseShipment') }, class_name: 'Shipment'
  has_many :shipment_buckets
  has_many :not_shipped_invoiced_buckets, -> { where(sent_to_invoice: true, shipment_id: nil) }, class_name: 'ShipmentBucket'
  has_many :bucket_line_items, through: :shipment_buckets, class_name: 'LineItem'
  has_many :designer_issues
  has_many :order_discounts
  has_many :rfd_designer_issues, -> { where.not(issue_type: ['critical order'])}, class_name: 'DesignerIssue'
  has_many :designer_invoices,through: :designer_orders
  has_many :line_items, :dependent => :destroy, :through => :designer_orders  do
    def not_canceled
      select{|lineitem| lineitem.status != 'cancel'}
    end
  end
  has_many :reverse_commissions
  has_many :designer_orders, :dependent => :destroy
  has_many :designs,:through => :line_items
  belongs_to :coupon
  has_many :shipment_allocations
  has_many :outbound_coupons, :class_name => 'Coupon', :foreign_key => :source_order_id
  belongs_to :buyer
  belongs_to :user
  belongs_to :parent_order, class_name: 'Order', foreign_key: :duplicated_from_id
  belongs_to :cart
  has_one :referral
  has_one :payment_gateway_transaction
  has_one :survey
  has_one :order_issue
  has_one :order_addon
  has_one :order_dispute
  has_many :tickets
  has_many :freshdesk_tickets
  has_many :adjustments
  has_many :sales_registers
  has_one :delivery_nps_info,as: :element
  has_many :additional_payments
  has_many :paid_additional_payments, -> { where(payment_state: 'paid') }, class_name: 'AdditionalPayment'
  belongs_to :stylist
  has_many :tailoring_infos
  has_and_belongs_to_many :reviews
  # has_many :line_item_addons,:through => :line_items
  # has_many :addon_type_values,:through => :line_item_addons
  # has_many :addon_type_value_groups,:through => :addon_type_values

  has_many :returns
  has_many :return_designer_orders, :through => :returns
  has_many :survey_answers, :as => :surveyable
  has_many :wallet_transactions
  has_many :stitching_addons
  has_many :process_dates, as: :processable
  has_one :rfd_process_date, -> {where('processable_type = ? and operation_process_id = ?', 'Order', OperationProcess::PROCESS_IDS['RFD'][0])}, class_name: 'ProcessDate', foreign_key: 'processable_id'
  has_one :affiliate_order
  has_one :searcher_entity, as: :searchable_entity

  has_many :logs, as: :entity
  
  has_many :gokwik_logs, -> {where('entity_type = ? and source = ?', 'Order', 'GoKwik')}, class_name: 'Log', foreign_key: 'entity_id'
  has_many :gokwik_successful_order, -> {where('entity_type = ? and description = ?', 'Order', 'Order Create')}, class_name: 'Log', foreign_key: 'entity_id'
  has_many :gokwik_error_order, -> {where('entity_type = ? and description = ?', 'Order', 'Order Failed')}, class_name: 'Log', foreign_key: 'entity_id'

  validates :billing_name, :presence => {:message => "Billing Details: Please specify a Name"}, :on => :create
  validates :billing_email,
            :presence => {:message => "Billing Details: Please specify an Email Address"},
            :format => { :with => VALID_EMAIL_REGEX, :message => "Billing Details: Please correct your Email Address"}, :on => [:create,:update], if: :billing_email_changed?

  validates :billing_pincode, :presence => {:message => "Billing Details: Please specify a Pincode"}

  validates :billing_street, :presence => {:message => "Billing Details: Please specify a Street"}, :length => {:maximum => 255, :too_long => "Billing Details: Please specify Address upto 255 characters."}, :on => :create
  validates :billing_city, :presence => {:message => "Billing Details: Please specify a City"}, :on => :create
  validates :billing_state, :presence => {:message => "Billing Details: Please specify a State"}, :on => :create
  validates :billing_country, :presence => {:message => "Billing Details: Please specify a Country"}, :on => :create
  validates :billing_phone, :presence => {:message => "Billing Details: Please specify a Phone Number"}, :on => :create

  validates :name, :presence =>  {:message => "Shipping Details: Please specify a Name"}
  validates :phone, :presence => {:message => "Shipping Details: Please specify a Phone Number"}
  validates :pincode, :presence => {:message => "Shipping Details: Please specify a Pincode"}
  validates :street, :presence => {:message => "Shipping Details: Please specify a Street"},  :length => {:maximum => 255}
  validates :city, :presence => {:message => "Shipping Details: Please specify a City"}
  validates :buyer_state, :presence => {:message => "Shipping Details: Please specify a State"}
  validates :country, :presence =>  {:message => "Shipping Details: Please specify a Country"}
  validate :validate_phone_number
  validates :pay_type, :presence => true
  validate :can_do_cod?
  validate :otp_verified_cod_order?, if: :validate_cod_otp
  validate :check_address_validation, if: :new_record?

  has_many :email_logs, :as => :loggable
  has_many :stitching_measurements
  scope :eager_load_query, -> { includes([:tags, :shipments, :returns, [:designer_orders => [:designer, :shipment, :rack_list, [:line_items => [:line_item_addons, :shipment, [:design => [:variants, :images]]]]]]])}
  scope :all_rel_min, -> { includes([:designer_orders => [[:line_items => [[:line_item_addons => :addon_type_value], :design]]]]) }
  scope :all_rel, -> { includes([:tickets,:referral, :returns, :stitching_measurements, :stylist,shipments: :shipper ,designer_orders: [:events, :rack_list, :designer, line_items: [ :latest_replacement_dispatched_scan, :events,:stitching_addons, :tailored_measurement, line_item_addons: [addon_type_value: :addon_type_value_group], design: [:categories, :variants, :images], tailoring_info: [:tailoring_inscan_bags]]]]) }
  scope :list_orders, -> { includes([:events, :designer_orders => :line_items]) }
  scope :stitching, -> { joins([line_items: [line_item_addons: [addon_type_value: :addon_type_value_group]]]).where(addon_type_value_groups: {name: ['custom_stitching','standard_stitching']}) }
  scope :orders_between, lambda {|start_date, end_date| where('orders.created_at >= ? AND orders.created_at <= ?', start_date, end_date)}
  scope :by_email, lambda { |email| where(email: email) if email.present? }
  scope :by_phone, lambda { |phone| where(phone: phone) if phone.present? }
  scope :by_number, lambda { |number| where(number: number) if number.present? }
  scope :by_name, lambda { |name| where(name: name) if name.present? }
  attr_accessor :should_assign_segment # A virtual attribute

  before_save :set_segment_flag
  after_commit :assign_segment_if_needed, on: [:update]
  
  def set_segment_flag
    # Only set flag if it matches the segmentation logic
    if relevant_state_change?
      self.should_assign_segment = true
    end
  end
  
  def assign_segment_if_needed
    if self.should_assign_segment && user.present?
      SegmentJob.perform_async(user.id)
      # No need to reset virtual attribute, since object is discarded after request
    end
  end
  
  def relevant_state_change?
    # Dispatch condition: must be a state change
    return true if state_changed? && state == 'dispatched' && country_code.to_s.upcase != 'IN'
  
    # Sane condition: trigger if
    # - state changed to sane OR state is already sane (and not changing)
    if country_code.to_s.upcase == 'IN'
      return state == 'sane'
    end
  
    false
  end

  # (1..2) signifies total number of address lines for each street address
  def self.street_address_lines
    @street_address_lines ||= (1..2).collect do |index|
      %w(billing_street street).collect do |st_address|
        "#{st_address}_line_#{index}"
      end
    end.flatten
  end

  def validate_phone_number
    {phone: :country, billing_phone: :billing_country}.each do |number, country|
      phone = public_send(number)
      country = public_send(country)
      if !phone.blank? && !country.blank?
        if country.downcase == "india" && (phone.length < 10 || phone.length > 13)
          errors.add("#{number}", "Please specify 10 digit Phone Number")
        elsif country.downcase != "india" && phone.length < 8
          errors.add("#{number}", "Please specify a Correct Phone Number(too short)")
        elsif country.downcase != "india" && phone.length > 20
          errors.add("#{number}", "Please specify a Correct Phone Number(too long)")
        end
      end
    end if new_record? || billing_phone_changed? || phone_changed?
  end

  def update_order_fields_trigger
    # DesignerOrder save callbacks are required before order save as order uses designer order total
    trigger_dos_call_back
    self.set_total_order_value
    if new_record? && Wallet.cashback_percent > 0 && !is_duplicate && (Design.country_code != 'IN' || prepaid?)
      self.other_details['loyalty_rewards_credit'] = Wallet.cashback_for_amount(total)
    end
    return true
  end

  # ensure it is called before calculating total
  def deduct_amount_from_wallet
    if !is_duplicate && user && cart && cart.wallet.present? && !cod? && cart.wallet.currency_convert.country_code == actual_country_code
      trigger_dos_call_back
      self[:shipping] = get_shipping_cost(country) if self.international?
      self.referral_discount = cart.wallet_discounts(:referral).round(2)
      cart_return_amount = cart.wallet_discounts(:return)
      self.refund_discount = cart_return_amount
      wallet_transaction_event = bank_deposit? ? :wait : nil
      do_wallet_transaction(wallet_transaction_event)
    end
  end

  def trigger_dos_call_back
    if designer_orders.any?(&:new_record?)
      designer_orders.each do |dos|
        dos.skip_before_after_filter = true
        dos.update_fields_trigger
      end
    end
  end

  def used_account_ids
    acc_ids = (line_items.collect{|i| i.attributes.values_at(*['qc_done_by','fabric_measured_by', 'measuremnet_received_by','stitching_done_by','check_items_done_by']) + i.stitching_addons.collect(&:details_added_by)}.flatten + (tickets.collect{|t| [t.created_by_id, t.resolved_by_id]}.flatten)).compact.uniq
    Account.select('id,email,role_id').preload(:role).where(id: acc_ids).map{|a| [a.id, [a.email.split('@')[0], a.role.try(:name)]]}.to_h
  end

  def self.attr_visible
    @attr_visible ||= %w(id number total name email)
  end

  def set_total_order_value
    self.total = 0
    designer_shipping = 0
    self.mirraw_addon_charges = 0
    self.total_tax = 0 if (!self.total_tax.present? || self.total_tax == 0) 
    flag_for_add_notes = false
    flag_for_one_minute_saree = false
    self.designer_orders.each do |d|
      unless d.state == "canceled" || self.state == 'cancel'
        d.line_items.not_canceled.each do |item|
          if item.paid_addons? && (self.new? or self.pending?)
            self.tag_list.add('addon') unless self.tag_list.include?('addon')
            flag_for_add_notes = true
            item.stitching_required = 'Y'
            flag_for_one_minute_saree = true if (!flag_for_one_minute_saree && item.inhouse_pre_stitching)
            item.save
          end
          if item.line_item_addons.loaded?
            self.mirraw_addon_charges += (item.line_item_addons.select{|lia| lia.snapshot_payable_to == 'mirraw'}.sum(&:snapshot_price)) * item.quantity
          else
            self.mirraw_addon_charges += (item.line_item_addons.where(:snapshot_payable_to => 'mirraw').sum(:snapshot_price)) * item.quantity
          end
        end
        self.tag_list.add('one_minute_saree') if flag_for_one_minute_saree
        self.total += d.scaled_total
        designer_shipping += d.shipping
      end
    end
    self.add_notes_without_callback('Added tag addon' , 'stitching') if !(self.new_record?) && flag_for_add_notes && !(self.notes.include?('Added tag addon'))
    self.additional_discount = 0 if self.additional_discount.blank?
    self.discount = 0 if self.discount.blank?
    deduce_discount = (self.discount + self.additional_discount).round(1)
    self.total -= deduce_discount
    if ['cancel', 'cancel_complete'].exclude?(self.state)
      self.total += order_addon.get_total_order_addon_charges if order_addon.present?
    end

    if self.international? && (['new','pending','confirmed'].include?(state)) && (self[:shipping] != 0 || self.referral_discount_changed? || self.refund_discount_changed?)
      self[:shipping] = self.get_shipping_cost(self.country)
    elsif !self.international? && self.new_record?
      self[:shipping] = Country.shipping_cost_for(nil,self.country,self.total,self.designer_orders.collect(&:line_items).flatten)
    end
    self.increment(:shipping,designer_shipping)

    if self.state != 'cancel'
      self.total = self.total + self.shipping + self.mirraw_addon_charges
      self.total += self.cod_charge if self.cod_charge.present?
    end

    if self.new_record?
      self.other_details['tax_rate'] = cart.get_tax_rate(country_code) if cart.present?
    end
    
    if self.international? && (['new','pending','confirmed'].include?(state))
      self.total_tax = self.total * self.other_details['tax_rate'].to_f
    end
    
    if self.state != 'cancel'
      self.total += self.total_tax
    end
    
    self.total -= + self.wallet_discount(self.currency_rate.to_f).ceil
    self.total = 0 if self.total < 0 || (self.total <= 2 && self.pay_type == 'Mirraw Wallet')
    # if self.pay_type == COD
    #   if ['new', 'pending', 'confirmed', 'sane'].include?(self.state)
    #     designer_order_discount = self.designer_orders.where('designer_orders.discount > 0').sum(:discount)
    #     if false #(designer_order_discount > 0 || self.discount.to_i > 0)
    #       w_dos_state = 'designer_orders.state IN (?)', ['new', 'pending']
    #       self.designer_orders.where(w_dos_state).each {|d| d.update_line_item_discount_price(self)}
    #       t_dos_discounts_l = self.designer_orders.sum(:discount)
    #       t_discount_provided_l = self.designer_orders.sum(:discount_provided)
    #       discount_diff_l = discount.to_i + t_dos_discounts_l - t_discount_provided_l
    #       if discount_diff_l != 0
    #         self.change_discount_line_item(discount_diff_l)
    #       end
    #     end
    #   end
    # end
  end

  def apply_subscription_fee
    return unless self.subscription_fee.present?
    return if subscription_fee_applied?
  
    all_items = self.designer_orders.flat_map { |d| d.line_items.not_canceled }
  
    total_snapshot = all_items.sum { |item| item.snapshot_price(1).to_f * item.quantity.to_i }
    return if total_snapshot <= 0
    discount_ratio = (self.subscription_fee.to_f / total_snapshot.to_f).round(6)
    total_discount_given = 0.0
  
    all_items.each_with_index do |item, index|
      original_price = item.snapshot_price(1).to_f
      item_total = original_price * item.quantity.to_i
  
      discount_total = (item_total * discount_ratio).round(2)
      if index == all_items.size - 1
        discount_total = (self.subscription_fee.to_f - total_discount_given).round(2)
      end
      total_discount_given += discount_total
  
      per_unit_discount = (discount_total / item.quantity.to_f).round(2)
      new_price = (original_price - per_unit_discount).round(2)
      new_price = 0 if new_price < 0  # safety check
  
      item.snapshot_price = new_price
      item.save!(validate: false)
    end
  
    self.other_details['subscription_fee_applied'] = 'true'
  end
  
  def subscription_fee_applied?
    self.other_details['subscription_fee_applied'] == 'true'
  end
  
  def get_applied_tax_rate
    if self.other_details["tax_rate"].present?
      rate = self.other_details["tax_rate"].to_f * 100
    else
      conversion_rate = CONVERSION_RATES[self.country_code] 
      total = (self.paid_amount.to_f/conversion_rate).round(2)
      total = self.total if self.total.present?
      tax = (self.total_tax.to_f/conversion_rate).round(2)
      ac = total - tax
      rate = (100 * tax) / ac
    end
    return self.total_tax > 0 ? ((rate).round()) : 0
  end

  def check_for_po_box_presence
    self.international? && self.street.to_s.downcase.include?('po box')
  end

  def shipping
    self[:shipping] + (state != 'cancel' ? express_delivery.to_i : 0)
  end

  def update_payment_gateway_error_code(order_payment_gateway: nil, order_attempted_payment_gateway: nil, error: nil)
    self.skip_before_filter = true
    args={}
    args[:payment_gateway] = order_payment_gateway if order_payment_gateway.present?
    args[:attempted_payment_gateway] = order_attempted_payment_gateway if order_attempted_payment_gateway.present?
    args[:ccavenue_api_error] = error if error.present?
    update_attributes(args) if args.present?
    reload
  end

  def change_discount_line_item(change)
    w_dos_state = 'designer_orders.state IN (?)', ['new', 'pending']
    w_snapshot_discount_price = 'snapshot_discount_price > 0'
    self.line_items.where(w_dos_state).where(w_snapshot_discount_price).each do |item|
      if (additional_discount_piece = (change / item.quantity.to_f).floor) != 0
        if additional_discount_piece < 0
          additional_discount = additional_discount_piece * item.quantity
          new_discount_price = item.snapshot_discount_price - additional_discount_piece
        elsif item.snapshot_discount_price >= additional_discount_piece || additional_discount_piece < 0
          additional_discount = additional_discount_piece * item.quantity
          new_discount_price = item.snapshot_discount_price - additional_discount_piece
        elsif item.snapshot_discount_price < additional_discount_piece
          additional_discount = item.snapshot_discount_price * item.quantity
          new_discount_price = 0
        end
        change -= additional_discount
        LineItem.where(:id => item.id).update_all(:snapshot_discount_price => new_discount_price)
        designer_order = item.designer_order
        DesignerOrder.unscoped.where(:id => designer_order.id).update_all(:discount_provided => (designer_order.discount_provided + additional_discount))
      end
      if change == 0
        break
      end
    end
  end

  def discounts
    discounts = 0
    self.designer_orders.each do |d|
      unless d.state == "canceled"
        discounts += d.discount if d.discount.present?
      end
    end
    discounts
  end

  def rakhi_order?
    self.designer_orders.each do |designer_order|
      designer_order.line_items.each do |line_item|
        item = line_item.design
        return true if item.category_parents_name.include?('rakhi-online')
      end
    end
    return false
  end

  def touch_with_callbacks
    self.updated_at = DateTime.now # Updating so that save callbacks gets called
    self.save!
  end

  handle_asynchronously :touch_with_callbacks

  def cod?
    self.pay_type == COD
  end

  def online_payment?
    [PAYMENT_GATEWAY, PAYU_MONEY].include? self.pay_type
  end

  def paypal?
    self.pay_type == PAYPAL
  end

  def bank_transfer?
    self.pay_type == BANK_TRANSFER
  end

  def gharpay?
    self.pay_type == GHARPAY
  end

  def payu_money?
    self.pay_type == PAYU_MONEY
  end

  alias cbd? gharpay?

  def bank_deposit?
    self.pay_type == BANK_DEPOSIT
  end

  def wallet?
    self.pay_type == WALLET
  end

  def payment_completed?
    self.payment_state == 'completed'
  end

  def paytm?
    self.pay_type == PAYTM
  end

  def self.post_domestic_cod_charge(pincode, designer_orders)
    return 0 if ENABLE_COD_CHARGE == 'false'
    pin = Pincode.find_by_pin_code(pincode)
    cost_to_ship = (pin.cost_to_ship.present? && pin.cost_to_ship != 0) ? pin.cost_to_ship : COD_DEFAULT_COST_TO_SHIP.to_i
    total_cod = 0
    total_cost_to_ship = 0
    total_vendor_cost = 0
    total_payout = 0
    base_cod_charge = (pin.cod_charge.present? && pin.cod_charge != 0) ? pin.cod_charge : COD_DEFAULT_COD_CHARGE.to_i
    rto_percent = (pin.rto_percent.present? && pin.rto_percent != 0) ? pin.rto_percent : COD_DEFAULT_RTO_PERCENT.to_i
    designer_orders.each do |item|
      total_vendor_cost += COD_TOTAL_VENDOR_COST.to_i
      total_cost_to_ship += cost_to_ship
      total_payout += item.total - item.payout
      charge_A = ( base_cod_charge  * ( 1 / (1- rto_percent) ) )
      charge_B = ((item.total.to_f/100)*COD_COMMISSION.to_i)
      total_cod += (charge_A > charge_B) ? charge_A : charge_B
    end
    total_cod_required = total_cost_to_ship + COD_MINIMUM_PROFIT.to_i - total_vendor_cost - total_payout
    if total_cod_required <= total_cod
      return total_cod.ceil
    else
      return total_cod_required.ceil
    end
  end

  def self.designer_orders_count(cart)
    if cart.kind_of? Cart
      design_ids = cart.line_items.pluck(:design_id)
      Design.select('DISTINCT(designer_id)').where(id: design_ids).count
    end
  end

  def return_order_details
    pay_types = {'bank_deposit_txn_id'=>'Bank Deposit', 'paypal_txn_id'=>'PayPal', 'payu_mihpayid'=>'PAYU', 'amazon_order_id'=>'Pay with Amazon', 'paytm_txn_id'=>'Paytm', 'razorpay_id'=>'Razorpay', 'duplicate_order'=>'Duplicate Order', 'g2a_txn_id'=>'G2A'}
    order_reasons,order_detail = [],[self.number,self.state,self.total,self.paid_amount,self.created_at.strftime('%d %b %Y'),self.returns.to_a.count,self.pay_type.to_s+'/'+self.payment_gateway.to_s]
    txn_ids = self.attributes.values_at(*pay_types.keys).compact
    order_detail += [txn_ids.first]
    if txn_ids.blank? && self.pay_type != COD
      order_reasons << 'Order Txn Id is not present.'
      order_reasons << 'PAYMENT NOT RECEIVED.' if ['cancel','cancel_complete'].include?(self.state)
    end
    order_reasons << 'Order is not marked Sane.' if ['sane','pickedup','dispatched','ready_for_dispatch','cancel','cancel_complete'].exclude?(self.state)
    return order_reasons,order_detail
  end

  def self.domestic_cod_charge(pincode,cart,duplicate_line_item=nil)
    return 0 if ENABLE_COD_CHARGE == 'false'
    pin = Pincode.find_by_pin_code(pincode)
    if pin.present? && ((cart.kind_of? Cart) || duplicate_line_item.present?)
      line_items = duplicate_line_item.present? ? duplicate_line_item : cart.line_items
      total_cod = 0
      designer_cod = 0
      total_cost_to_ship = 0
      total_vendor_cost = 0
      total_profit = 0
      cost_to_ship = (pin.cost_to_ship.present? && pin.cost_to_ship != 0) ? pin.cost_to_ship : COD_DEFAULT_COST_TO_SHIP.to_i
      designer_orders = {}
      designer_orders_total = {}
      line_items.each do |item|
        designer = item.design.designer
        if !designer_orders.include? designer.name
         total_vendor_cost += COD_TOTAL_VENDOR_COST.to_i
         total_cost_to_ship += cost_to_ship
        end
        #Calculate Charge A for pincode
        base_cod_charge = (pin.cod_charge.present? && pin.cod_charge != 0) ? pin.cod_charge : COD_DEFAULT_COD_CHARGE.to_i
        rto_percent = (pin.rto_percent.present? && pin.rto_percent != 0) ? pin.rto_percent : COD_DEFAULT_RTO_PERCENT.to_i
        designer_orders[designer.name] = [] unless designer_orders[designer.name].present?
        designer_orders_total[designer.name] = [] unless designer_orders[designer.name].present?

        charge_A = ( base_cod_charge  * ( 1 / (1- rto_percent) ) )
        # puts "***********************************CHARGE-A: #{charge_A}***********************************"
        #Calculate Charge B for the designer order value
        charge_B = ((item.price.to_f/100)*COD_COMMISSION.to_i)
        # puts "***********************************CHARGE-B: #{charge_B}***********************************"
        designer_cod = (charge_A > charge_B) ? charge_A : charge_B
        designer_orders[designer.name].push(designer_cod)
        designer_orders_total[designer.name].push(item.vendor_selling_price*item.quantity)
        designer_transaction_rate = designer.transaction_rate.present? ? designer.transaction_rate : TRANSACTION_RATE
        designer_orders_total[designer.name+'_transaction_rate'] = designer_transaction_rate
      end

      designer_orders.keys.each do |i|
       total_cod += designer_orders[i].max
       total = designer_orders_total[i].sum
       vendor_payout = total * (100 - designer_orders_total[i+'_transaction_rate']) / 100
       total_profit += total - vendor_payout
      end
      total_cod_required = total_cost_to_ship + COD_MINIMUM_PROFIT.to_i - total_vendor_cost - total_profit
      if total_cod_required <= total_cod
        return total_cod.ceil
      else
        return total_cod_required.ceil
      end
    else
      return false
    end
  end

  def get_currency_cod_charges(conversion_rate = 1)
    # cart = Cart.find_by_id(self.cart_id)
    # total_cod_charges = self.domestic_cod_charge(self.pincode, cart)
    # (total_cod_charges/conversion_rate).round(2)
    (COD_CHARGES/conversion_rate).round(2)
  end

  def gharpay_available?
    total_sum = self.line_items.to_a.sum(&:sub_total)
    if total_sum > GHARPAY_MIN_ORDER
      courier = Courier.where(:pincode => self.pincode).first
      if courier
        @exists = courier.cbd == 'Y'
      else
        @exists = false
      end
      return @exists
    else
      return false
    end
  end

  def eligible_for_automated_refund?
    AUTOMATED_COD_REFUND['enable'] && self.cod? && self.domestic?
  end

  def domestic?
    !international?
  end

  def international?
    self.country.try(:downcase).to_s != 'india'
  end

  def domestic_paypal?(country_code, currency_symbol)
    country_code == 'IN' && ['inr', 'rs'].include?(currency_symbol.try(:downcase)) && country.downcase == 'india' && billing_country.downcase == 'india'
  end

  def contact_number
    case self.country.downcase
    when 'united kingdom'
      return '+441-214-614-192'
    when 'canada'
      return '+15-817-054-535'
    when 'india'
      return MIRRAW_CONTACT_INFO
    else
      return '+1-949-464-5941'
    end
  end

  def billing_international?
    self.billing_country.downcase != 'india'
  end

  # save order once this method is called.
  def add_line_items_from_cart(cart)
    coupon = cart.coupon
    discounts, scaled_discounts = coupon.present? ? coupon.apply_coupon(cart,RETURN_NORMAL | RETURN_SCALED, self) : [0,0]
    discount_designer = nil
    if discounts > 0 && coupon.designer.present?
      discount_designer = coupon.designer
    end
    if (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present?
      bmgnx_offer_line_items_hash = cart.get_free_items_bmgnx(bmgnx_hash)
    end
    designer_items = cart.line_items.group_by { |li| li.design.designer }
    designer_items.each do |designer, items|
      designer_order = self.designer_orders.build
      designer_order.designer_payout_status = 'unpaid'
      designer_order.designer = designer
      designer_order.add_line_items(cart, items, bmgnx_offer_line_items_hash)
      if discounts > 0 && discount_designer.present? && designer_order.designer == discount_designer
        designer_order.discount = discounts
        designer_order.scaled_discount=scaled_discounts
        designer_order.coupon = coupon
      end
      active_promotions = PromotionPipeLine.active_promotions
      designer_order.promotion_discount = designer_order.sale_discount_percent if designer_order.sale_on_country?(active_promotions,Design.country_code)
      designer_order.skip_order_callbacks = true
      # designer_order.save!
    end
    self.additional_discount = cart.additional_discounts
    if coupon && (discounts > 0 || cart.free_stiching_coupon?)
      if discount_designer.blank?
        self.coupon = coupon
        self.discount = scaled_discounts
      end
      coupon.use_count = coupon.use_count + 1
      coupon.save
      if DESTROY_CART
        cart.coupon = nil
        cart.save
      end
    end
    # self.save!
  end

  def get_free_items_bmgnx(bmgnx_hash = nil, return_type = RETURN_SCALED)
    total_quantity, bmgnx_items_hash = 0, {}, {}
    self.line_items.each do |item|
      if item.buy_get_free == 1
        bmgnx_items_hash[item.design_id] = [item.snapshot_price(return_type), item.quantity, item.design_id]
        total_quantity += item.quantity
      end
    end
    if bmgnx_items_hash.present?
      bmgnx_items_hash = bmgnx_items_hash.sort_by {|k,v| -v[0]}.to_h
      bmgnx_hash = PromotionPipeLine.bmgnx_hash unless bmgnx_hash.present?
      factor = ((total_quantity + bmgnx_hash[:n]) / (bmgnx_hash[:m] + bmgnx_hash[:n])).to_i
      mx, nx, count = factor * bmgnx_hash[:m], factor * bmgnx_hash[:n], 0
      # total_charged_quantity are items with charge as full amount (mx)  
      total_charged_quantity = total_quantity > (mx + nx) ? (total_quantity - nx) : mx  
      # remove first mx items and keep only last nx items which are eligible for offer
      bmgnx_items_hash.each do |k,v|
        remaining = total_charged_quantity - count
        if count < total_charged_quantity
          if v[1] <= remaining
            count += v[1]
            bmgnx_items_hash.delete(k)
          else
            count += remaining
            v[1] -= remaining
            break
          end
        end
      end
    end
    bmgnx_items_hash
  end

  def remove_line_item(item)
    designer_order = item.designer_order
    designer_order.remove_line_item(item)
    designer_order.save!
  end

  def update_line_item(item, quantity)
    designer_order = item.designer_order
    designer_order.update_line_item(item, quantity)
    designer_order.change_rack_quantity((quantity-item.quantity),'add')
    designer_order.save!
  end

  def self.get_potential_order_list
    orders_with_potential_des_ords = DesignerOrder.unscoped.joins(:line_items,:order,:designer).
      select('designer_orders.order_id').
      where('line_items.status is null AND orders.state = ? AND ( (designer_orders.pickup::date = ? AND lower(designers.city) IN (?) )
      OR (designer_orders.pickup::date = ? AND designers.city NOT IN (?) ) )
      AND orders.geo = ?', 'sane', Date.today, NEAR_BY_CITY , Date.today - 2, NEAR_BY_CITY, 'international').
      where(state: ['critical','dispatched','completed']).
      group('designer_orders.order_id').count('line_items.id')
    orders_with_total_li_count = Order.unscoped.
            select("orders.id, orders.number, COUNT(designer_orders.id) AS no_of_des_ords, COUNT(line_items.id) as total_line_items_count, COUNT(CASE WHEN line_items.received = 'Y' THEN line_items.id END ) AS received_count, COUNT(CASE WHEN line_items.stitching_required = 'Y' THEN line_items.id END) AS stitching_count, COUNT(CASE WHEN line_items.stitching_done = 'Y' THEN line_items.id END) AS stitching_done_count").
            joins(designer_orders: :line_items).
            where(id: orders_with_potential_des_ords.keys).
            where('line_items.status is null AND designer_orders.state IN (?)', ['new','pending','dispatched','critical','completed']).
            group('orders.id') if orders_with_potential_des_ords.present?
    potential_orders = []
    return false unless orders_with_total_li_count.present?
    orders_with_total_li_count.each do |order|
      if (order.total_line_items_count.to_i - order.received_count.to_i) == orders_with_potential_des_ords[order.id]
        potential_orders << [order.number, order['no_of_des_ords'], order['total_line_items_count']  ,order['received_count'] ,order['stitching_count'], order['stitching_done_count']]
      end
    end
    return false unless potential_orders.present?
    orders_csv = create_potential_order_csv(potential_orders)
  end

  def get_return_coupon_data(coupons)
    coupons_data = Hash.new('')
    coupons.each_with_index do |coupon,index|
      %w(code used expired apply).each {|key| coupons_data[key] += "\n"} if index >0
      coupons_data['code'] += coupon.code
      coupons_data['used'] += (coupon.use_count.to_i == coupon.limit.to_i ? 'YES' : 'NO')
      coupons_data['expired'] += (coupon.live? ? 'NO' : 'YES')
      coupons_data['apply'] += ((applied = coupon.coupon_used_on).present? ? applied.number : '')
    end
    coupons_data
  end

  def self.get_tailoring_potential_order_list
    orders = {}
    TailoringInfo.only_line_items.where('date(assigned_to_tailor_at) = ? or date(assigned_to_tailor_at_material2) = ?', 4.days.ago, 4.days.ago).where{material1_received_status.eq(nil)|material2_received_status.eq(nil)}.each do |tailoring_info|
      if tailoring_info.assigned_to_tailor_at.present? && tailoring_info.assigned_to_tailor_at.to_date == 4.days.ago.to_date &&
          (tailoring_info.material1_received_status || tailoring_info.assigned_to_tailor_at_material2.blank? || tailoring_info.assigned_to_tailor_at_material2 < 3.days.ago)
        (orders[tailoring_info.order_id] ||=[]) << tailoring_info.id
      elsif tailoring_info.assigned_to_tailor_at_material2.present? && tailoring_info.assigned_to_tailor_at_material2.to_date == 4.days.ago.to_date &&
          (tailoring_info.material2_received_status || tailoring_info.assigned_to_tailor_at.blank? || tailoring_info.assigned_to_tailor_at < 3.days.ago)
        (orders[tailoring_info.order_id] ||=[]) << tailoring_info.id
      end
    end
    potential_tailoring_orders = []
    Order.preload(:line_items).where(id: orders.keys).each do |order|
      dos_ids = order.designer_orders.select { |d| d.state != 'canceled'}.map(&:id)
      stitching_done_count,stitching_required_count,received = 0,0,0
      old_li = order.line_items
      line_items = []
      old_li.each do |li|
        if dos_ids.include?(li.designer_order_id) && li.status !='cancel'
          line_items << li
          stitching_done_count +=1 if li.stitching_done == 'Y'
          stitching_required_count +=1 if li.stitching_required == 'Y'
          received += 1 if li.received_by.present?
        end
      end
      if stitching_required_count == (stitching_done_count + orders[order.id].uniq.count)
        potential_tailoring_orders << [order.number, order.designer_orders.size,line_items.size,received, stitching_required_count, stitching_done_count]
      end
    end
    return false unless potential_tailoring_orders.present?
    orders_csv = create_potential_order_csv(potential_tailoring_orders)
  end

  def add_variant(variant, old_item_id=nil)
    design = variant.design
    designer_order = self.designer_orders.where(designer_id: design.designer_id, state: ['new', 'pending']).first
    line_item = self.line_items.where(variant_id: variant.id).first
    rev_com = false
    unless line_item.present? && line_item.status != 'cancel'
      old_item = LineItem.where(id: old_item_id).first
      if designer_order.blank? && (line_item.blank? || (line_item.status != 'cancel' && ['canceled', 'vendor_canceled'].exclude?(line_item.designer_order.state)))
        designer_order = DesignerOrder.new(designer_id: design.designer_id)
        designer_order.designer_payout_status = 'unpaid'
        designer_order.rack_list_id = old_item.designer_order.rack_list_id if old_item.present?
        self.designer_orders << designer_order
      end
      l = self.line_items.where(design_id: design.id,variant_id: variant.id).first_or_initialize(snapshot_price: variant.effective_price_for_country(self.country_code), scaling_factor: design.get_scale(self.country_code), vendor_selling_price: variant.get_vendor_selling_amount)
      l.variant = variant
      if l.status == 'cancel'
        l.cancel_reason = nil
        # l.designer_order = nil
        rev_com = true
      end
      l.status = nil
      l.replacement_item_id = old_item.get_replaced_product(l.id) if old_item.present? && old_item.variant_id != l.variant_id
      l.note = variant.option_type_values[0].option_type.p_name + " Size : " + variant.option_type_values[0].p_name
      if designer_order.present?
        designer_order.add_line_items(nil, [l])
        designer_order.change_rack_quantity(1,'add') if old_item.present?
      else
        designer_order = l.designer_order
      end
      rev_com = false unless l.save!
      l.warehouse_use_item(l.variant) if designer_order.save!
      SidekiqDelayGenericJob.set(queue: 'high').perform_async("ReverseCommission", nil, "add_reverse_commission", {"#{l.class}": l.id}, 1)  if rev_com && designer_order.try(:confirmed_at).present?
      #ReverseCommission.sidekiq_delay(queue: 'high').add_reverse_commission(l,1) if rev_com && designer_order.try(:confirmed_at).present?
      if designer_order.designer_payout_status == "paid" || designer_order.designer_payout_status == "processing" || designer_order.designer_payout_notes.present?
        # designer_order.sidekiq_delay.remove_adjustment_for_cancelation(l.id)
        SidekiqDelayGenericJob.perform_async(designer_order.class.to_s, designer_order.id, "remove_adjustment_for_cancelation", l.id)
      end
      SidekiqDelayGenericJob.perform_async("ScopeScoreEvent", nil, "apply_remove_cancel_event", {"#{l.class}": l.id}, :remove)
      #ScopeScoreEvent.sidekiq_delay.apply_remove_cancel_event(l,:remove)
      self.items_received_status = false
      self.items_received_on = nil
      self.save!
      if (['sane','ready_for_dispatch'].include? self.state)
        designer_shipping_time = designer_order.created_at.advance(days: designer_order.get_vendor_delivery_days)
        from,to = self.international? ? ['designer','mirraw'] : ['designer','user']
        DeliveryNpsInfo.where(element_id: designer_order.id,element_type: 'DesignerOrder').first_or_create(promised_delivery_date: designer_shipping_time,order_number: self.number,order_from: from,order_to: to)
      end
    end
    return true
  end

  def add_measurements(design_id,replaced_des_data,old_order = nil)
    new_line_item = line_items.where(design_id:design_id).first
    order = old_order.blank? ? self : old_order
    old_line_item = order.line_items.where(design_id:replaced_des_data[0].to_i).first
    stitching_measurements =
    if (length = replaced_des_data.length) == 2
      order.stitching_measurements.where('design_id = ?',replaced_des_data[0].to_i).limit(replaced_des_data[1].to_i)
    else
      mes_ids = replaced_des_data[2..(length-1)]
      StitchingMeasurement.where(id:mes_ids)
    end
    if stitching_measurements.present?
      measurement_array = []
      mes_update_hash = {design_id: design_id, line_item_id: new_line_item.id, order_id: id, stitching_label_url: nil, stitching_notes: nil, suggested_measurements: nil, reject_mail_sent_at: nil}
      stitching_measurements.each do |stitch_mes|
        stitching_measurement = stitch_mes.dup
        stitching_measurement.assign_attributes(mes_update_hash)
        measurement_array << stitching_measurement
      end
      StitchingMeasurement.import measurement_array, validate: false
      if old_order.present? && (stylist = old_order.stylist).present?
        self.stylist_id = old_order.stylist_id
        self.assigned_to_stylist_at = DateTime.now
        self.add_notes_without_callback("Assigned to Stylist:#{stylist.name}", 'stitching')
        self.skip_before_filter = true
        self.save
        stylist.orders_count += 1
        stylist.save
      end
      if measurement_array.present? && measurement_array.collect(&:state).all?{|state| state == 'approved'} && old_line_item.try(:fabric_measured_on) && old_line_item.try(:measuremnet_received_on)
        LineItem.sidekiq_delay(queue:'low').mark_fabric_measurement_done_and_confirmed(new_line_item.id, old_line_item.fabric_measured_by , 'System')
      end
      StitchingMeasurement.sidekiq_delay.create_standard_stylist_measurements(new_line_item.id)
      StitchingMeasurement.sidekiq_delay.generate_pdf(new_line_item.id)
    end
  end

  def get_old_item_id(design, params)
    search_clause = params[:old_variant_id].present? ? {variant_id: params[:old_variant_id]} : {design_id: params[:replace_data][0]}
    if (old_items = self.line_items.where(search_clause).preload(variant: :option_type_values)).present?
      if old_items.length == 1
        old_item_id = old_items.first.id
      else
        old_variants = []
        old_items.each {|item| old_variants << item.variant.option_type_values[0].name + " : " + item.variant.id.to_s}
        old_variants = "Select Which existing design variant you want to replace : #{old_variants.join(' , ')}"
      end
    end
    if design.variants_available && (params['add_design'] || old_items.length > 1 || (old_items.first.design_id != design.id && params[:variant_id].blank?))
      variants_options = []
      design.variants.each do |variant|
        variants_options << variant.option_type_values[0].name + " : " + variant.id.to_s if variant.quantity > 0
      end
      if variants_options.count > 0
        variants_options = 'Enter variant id of new design. For ' + variants_options.join(" , ")
        return {more_input: true, variants_available: true, variants: variants_options, old_variants: old_variants}
      else
        return {more_input: true, variants_available: false}
      end
    elsif old_variants.present?
      return {more_input: true, old_variants: old_variants}
    else
      return {more_input: false, old_item_id: old_item_id}
    end
  end

  def duplicate_order(design_id, current_account, old_item_id: nil, variant_id: nil)
    line_item = variant_id.present? ? self.line_items.where(variant_id: variant_id) : self.line_items.where(design_id: design_id)
    total_cod_charges = Order.domestic_cod_charge(self.pincode, nil,line_item)||0

    new_order = Order.new
    new_order.is_duplicate = true
    new_order.copy_billing_info_from_older_order(self)
    new_order.generate_order_number
    if order_addon.try(:gift_wrap_price)
      new_order.build_order_addon(gift_wrap_price: GIFT_WRAP_PRICE.to_f)
    end
    new_order.duplicated_from_id = self.id
    new_order.add_notes_without_callback("Duplicated from: #{self.number}.", 'duplicate_order') if new_order.save
    line_item_hash = old_item_id.present? ? {replacement_item_id: old_item_id} : {}
    if line_item.present?
      line_item_hash.merge!(line_item.first.attributes.slice('snapshot_price','snapshot_discount_price','scaling_factor', 'vendor_selling_price','variant_id','note','snapshot_country_code','snapshot_currency_rate').symbolize_keys)
    elsif variant_id.present?
      line_item_hash.merge!({variant_id: variant_id})
    end
    if new_order.add_design_by_id(design_id , total_cod_charges, current_account, line_item_hash)
      new_order.set_total_order_value
      new_order.save!
      event = self.add_notes_without_callback("Duplicate Order: #{new_order.number}.", 'duplicate_order')
      return [new_order.number, event]
    else
      return [false,nil]
    end
  end

  def designer_order_items(tracking_number,designer_order_id)
    designer_order = if designer_order_id.present?
      self.designer_orders.preload(:inward_bag, :rack_list).where('designer_orders.id = ? AND LOWER(designer_orders.tracking_num) = ?',designer_order_id,tracking_number.try(&:downcase))
    else
      self.designer_orders.preload(:inward_bag, :rack_list).where('LOWER(designer_orders.tracking_num) LIKE (?) or LOWER(designer_orders.recent_tracking_number) LIKE (?)', "%#{tracking_number.downcase}%", "%#{tracking_number.downcase}%")
    end
    if designer_order.present?
      additional_preload_array = (ENABLE_UNPACKING_BUCKET_PROCESS == 'true' ? [:stitching_measurements, [line_item_addons: [:addon_type_value]]] : [])
      preload_array = (DISABLE_ADMIN_FUCTIONALITY['detailed_unpacking'] ? [:qc_done_by_account, additional_preload_array, design: [:images, :property_values => :property]] : [])
      items = designer_order.first.line_items.sane_items.preload(preload_array)
    end
    return [designer_order.first, items]
  end

  def add_design(design, current_account = nil, old_item_id=nil)
    if design.quantity > 0
      designer_order = self.designer_orders.where(designer_id: design.designer, state: ['new', 'pending']).first
      rev_com = false
      line_item = self.line_items.where(design_id: design.id).first
      unless line_item.present? && line_item.status != 'cancel'
        old_item = LineItem.where(id: old_item_id).first
        if designer_order.blank? && (line_item.blank? || (line_item.status != 'cancel' && ['canceled', 'vendor_canceled'].exclude?(line_item.designer_order.state)))
          designer_order = DesignerOrder.new(designer_id: design.designer_id)
          designer_order.designer_payout_status = 'unpaid'
          designer_order.rack_list_id = old_item.designer_order.rack_list_id if old_item.present?
          self.designer_orders << designer_order
        end
        l = self.line_items.where(:design_id => design.id).first_or_initialize(:snapshot_price => design.effective_price_for_country(self.country_code),:scaling_factor => design.get_scale(self.country_code), vendor_selling_price: design.get_vendor_selling_amount)
        if l.status == 'cancel'
          l.cancel_reason = nil
          # l.designer_order = nil
          rev_com = true
        end
        l.status = nil
        l.replacement_item_id = old_item.get_replaced_product(l.id) if old_item.present? && old_item.design_id != l.design_id
        if designer_order.present?
          designer_order.add_line_items(nil, [l])
          designer_order.change_rack_quantity(1,'add') if old_item.present?
        else
          designer_order = l.designer_order
        end
        l.replacement_item_id = old_item.get_replaced_product(l.id)
        rev_com = false unless l.save!
        l.warehouse_use_item(l.design) if designer_order.save!
        SidekiqDelayGenericJob.set(queue: 'high').perform_async("ReverseCommission", nil, "add_reverse_commission", {"#{l.class}": l.id}, 1) if rev_com && designer_order.try(:confirmed_at).present?
        #ReverseCommission.sidekiq_delay(queue: 'high').add_reverse_commission(l,1) if rev_com && designer_order.try(:confirmed_at).present?
        if designer_order.designer_payout_status == "paid" || designer_order.designer_payout_status == "processing" || designer_order.designer_payout_notes.present?
          # designer_order.sidekiq_delay.remove_adjustment_for_cancelation(l.id)
          SidekiqDelayGenericJob.perform_async(designer_order.class.to_s, designer_order.id, "remove_adjustment_for_cancelation", l.id)
        end
        SidekiqDelayGenericJob.perform_async("ScopeScoreEvent", nil, "apply_remove_cancel_event", {"#{l.class}": l.id}, :remove)
        #ScopeScoreEvent.sidekiq_delay.apply_remove_cancel_event(l,:remove)
        self.items_received_status = false
        self.items_received_on = nil
        self.save!
        if (['sane','ready_for_dispatch'].include? self.state)
          designer_shipping_time = designer_order.created_at.advance(days: designer_order.get_vendor_delivery_days)
          from,to = self.international? ? ['designer','mirraw'] : ['designer','user']
          DeliveryNpsInfo.where(element_id: designer_order.id,element_type: 'DesignerOrder').first_or_create(promised_delivery_date: designer_shipping_time,order_number: self.number,order_from: from,order_to: to)
        end
        if old_item.id.present? && !l.new_record?
          l.copy_stitching_data(current_account, old_item.id)
        end
      end
      return true
    else
      return false
    end
  end

  def add_design_by_id(design_id, cod_charges, current_account,old_line_item_hash={})
    if (design = Design.where(:id => design_id).first).present?
      variant = Variant.where(id: old_line_item_hash[:variant_id]).first if old_line_item_hash[:variant_id].present?
      if (design.quantity > 0) || (variant.present? && variant.quantity.to_i > 0)
        designer_order = self.designer_orders.where(:designer_id => design.designer).first
        if designer_order.blank?
          designer_order = DesignerOrder.new
          designer_order.designer_payout_status = 'unpaid'
          designer_order.designer = design.designer
          self.designer_orders << designer_order
        end
        designer = Designer.where(:id => design.designer_id).first
        if self.cod? && designer.cod
          cart = Cart.find_by_id(self.cart_id)
          self.cod_charge = cod_charges
          #self.cod_charge = COD_CHARGES
        end
        l = LineItem.new(:design_id => design_id,:snapshot_price => old_line_item_hash[:snapshot_price] || design.effective_price_for_country(self.country_code) ,:scaling_factor => old_line_item_hash[:scaling_factor] || design.get_scale(self.country_code))
        l.variant_id = old_line_item_hash[:variant_id]
        if old_line_item_hash[:replacement_item_id].present?
          old_item = LineItem.where(id: old_line_item_hash[:replacement_item_id]).first
          l.replacement_item_id = old_item.get_replaced_product(l.id)
        end
        l.snapshot_discount_price = old_line_item_hash[:snapshot_discount_price]
        l.note = old_line_item_hash[:note]
        l.note = l.add_variant(variant) if variant.present? && l.note.blank?
        l.snapshot_country_code = old_line_item_hash[:snapshot_country_code]
        l.snapshot_currency_rate = old_line_item_hash[:snapshot_currency_rate]
        l.vendor_selling_price = old_line_item_hash[:vendor_selling_price] || design.discount_price
        l.warehouse_use_item(l.variant || l.design) if l.save!
        designer_order.add_line_items(nil, [l])
        designer_order.save!
        self.save!
        if old_line_item_hash[:replacement_item_id].present? && !l.new_record?
          l.copy_stitching_data(current_account, old_line_item_hash[:replacement_item_id])
        end
      else
        return false
      end
    else
      return false
    end
  end

  def self.mark_additional_payment_complete_paypal(receiver_email,parameters,status = nil)
    order_number,type,additional_pay_id = parameters['invoice'].split('_')
    paypal_allowed_currencies = Order::PAYPAL_ALLOWED_CURRENCIES
    additional_payment = AdditionalPayment.find_by_id(additional_pay_id)
    order = Order.find_by_number(order_number)
     if order.present? && additional_payment.present?
      if parameters['mc_currency'] && paypal_allowed_currencies.include?(order.currency_code)
        lower_total = (additional_payment.total/(additional_payment.currency_rate || order.currency_rate)).round(2) - 20
      else
        lower_total = CurrencyConvert.to_usd_paypal_rate(additional_payment.total,(additional_payment.currency_rate || order.currency_rate),(additional_payment.currency_code || order.currency_code),(additional_payment.country_code || order.country_code)).to_i - 20
      end
      additional_payment.payment_gateway = PAYPAL
      additional_payment.paypal_payer_id = parameters['payer_id']
      additional_payment.paypal_txn_id = parameters['txn_id']
      additional_payment.paypal_payment_type = parameters['payment_type']
      additional_payment.paypal_ipn_track_id = parameters['ipn_track_id']
      additional_payment.paypal_mc_gross = parameters['mc_gross']
      additional_payment.paypal_mc_currency = parameters['mc_currency']
      additional_payment.paypal_num_cart_items = parameters['num_cart_items']
      additional_payment.save
      if (parameters['receiver_email'] == receiver_email && (parameters['mc_gross'].to_i >= lower_total)) && status.blank?
        if ['custom_addon', 'post_order_stitching'].include?(additional_payment.charge_type)
          additional_payment.addon_payment_complete!
        else
          order.add_notes_without_callback('SUCCESS addon payment','payment')
          order.mark_addon_payment_complete(additional_payment.id)
        end
        OrderMailer.sidekiq_delay.send_addon_payment_success_mail(order.id)
      end
    end
  end

  def self.mark_additional_payment_complete_payu(parameters)
    order_number,type,additional_pay_id  = parameters[:txnid].split('_')
    if (order = Order.find_by_number(order_number)) && (additional_payment = AdditionalPayment.find_by_id(additional_pay_id))
      additional_payment.payment_gateway = PAYU_MONEY
      additional_payment.payu_mihpayid = parameters[:mihpayid]
      additional_payment.payu_status = parameters[:status].downcase
      additional_payment.payu_unmapped_status = parameters[:unmappedstatus]
      additional_payment.payu_payment_category_mode = parameters[:PG_TYPE]
      additional_payment.payu_bank_ref = parameters[:bank_ref_num]
      additional_payment.payu_bankcode = parameters[:bankcode]
      additional_payment.payu_error = parameters[:error]
      additional_payment.payu_error_message = parameters[:error_Message]
      additional_payment.payu_name_on_card = parameters[:name_on_card]
      additional_payment.payu_card_num = parameters[:cardnum]
      additional_payment.payu_payment_issuing_bank = parameters[:issuing_bank]
      additional_payment.payu_card_type = parameters[:card_type]
      additional_payment.payu_money_id = parameters[:payuMoneyId]
      additional_payment.save
      order.add_notes_without_callback('SUCCESS addon payment','payment')
      order.mark_addon_payment_complete(additional_payment.id)
      OrderMailer.sidekiq_delay.send_addon_payment_success_mail(order.id)
    end
  end

  def total_count
    self.line_items.to_a.sum(&:quantity)
  end

  def line_items_count
    self.line_items.to_a.count
  end

  def items_and_shipping_total
    price = 0
    cart.line_items.each do|line_item|
      price += (line_item.snapshot_price/self.currency_rate).round(2)*line_item.quantity
      line_item.line_item_addons.each do |li_addon|
        price += (li_addon.snapshot_price/self.currency_rate).round(2)*line_item.quantity
      end
    end

    price += (self.shipping/self.currency_rate).round(2)
    price -= (cart.discounts/self.currency_rate).round(2)
    price -= wallet_discount(self.currency_rate)
    price.round(2)
  end

  def g2a_ipn_hash
    total = self.currency_code == 'Rs' ? self.total : self.items_and_shipping_total
    g2a_args = [
      self.g2a_txn_id,
      self.number,
      total,
      Rails.application.config.g2a[:api_secret_key]
    ]
    Digest::SHA256.new.hexdigest(g2a_args.join)
  end

  def g2a_hash
    g2a_args = [
      Rails.application.config.g2a[:api_hash],
      Rails.application.config.g2a[:merchant_email],
      Rails.application.config.g2a[:api_secret_key]
    ]
    Digest::SHA256.new.hexdigest(g2a_args.join)
  end


  def paypal_payments_standard_url(return_url, currency_symbol, country_code,additional_payment_id = nil,additional_payment_data = nil)
    non_word_excluding_space_regex = Regexp.new(/[^\w\s]/)
    paypal_allowed_currencies = Order::PAYPAL_ALLOWED_CURRENCIES
    currency_is_allowed = paypal_allowed_currencies.include?(currency_symbol)
    currency_code = currency_is_allowed ? currency_symbol : 'USD'

    if domestic_paypal?(country_code, currency_symbol)
      currency_code = 'INR'
      if Rails.env.production? || Rails.env.admin?
        email = "<EMAIL>"
        return_url = 'http://www.mirraw.com/orders/' + number
        paypal_server_url = "https://www.paypal.com/cgi-bin/webscr?"
        notify_url = "http://www.mirraw.com/order/paypal_response"
      else
        email = "<EMAIL>"
        return_url = 'http://staging-mirraw.herokuapp.com/orders/' + number
        paypal_server_url = "https://www.sandbox.paypal.com/cgi-bin/webscr?"
        notify_url = "http://staging-mirraw.herokuapp.com/order/paypal_response"
      end
    else
      if Rails.env.production?
        email = "<EMAIL>"
        return_url = 'http://www.mirraw.com/orders/' + number
        paypal_server_url = "https://www.paypal.com/cgi-bin/webscr?"
        notify_url = "http://www.mirraw.com/order/paypal_response"
      elsif Rails.env.staging?
        email = "<EMAIL>"
        return_url = 'http://staging-mirraw.herokuapp.com/orders/' + number
        paypal_server_url = "https://www.sandbox.paypal.com/cgi-bin/webscr?"
        notify_url = "http://staging-mirraw.herokuapp.com/order/paypal_response"
      else
        # email = "<EMAIL>"
        email = "<EMAIL>"
        return_url = 'https://ed3f9460.ngrok.io/orders/' + number
        paypal_server_url = "https://www.sandbox.paypal.com/cgi-bin/webscr?"
        notify_url = "https://ed3f9460.ngrok.io/order/paypal_response"
      end
    end


    paypal_request = Payment::PaymentDetails.new(self, country_code, currency_symbol, currency_symbol)
    values = {
      :business => email,
      :cmd => "_cart",
      :upload => 1,
      :return => return_url,
      :invoice => number,
      :notify_url => notify_url,
      :currency_code => currency_code
    }
    line_items = self.designer_orders.collect(&:line_items).flatten
    if domestic_paypal?(country_code, currency_symbol)
      values[:address_override] = 1
      discount_price = self.total_discounts.round(2)
      shipping_cost = self.shipping.round(2)
      line_items_count = 0
      line_items.each_with_index do |item, index|
        values.merge!({
          "amount_#{index + 1}" => item.price_with_addons.round(2),
          "item_name_#{index + 1}" => item.design.title,
          "item_number_#{index + 1}" => item.design.id,
          "quantity_#{index + 1}" => item.quantity
        })
        line_items_count += 1
      end
    else
      if additional_payment_id.present?
        discount_price = 0.0
        shipping_cost = 0.0
        values[:invoice] = "#{number}_addon_#{additional_payment_id}"
        values[:return] = return_url
        if additional_payment_data.present? && additional_payment_data[:items].present?
          values[:return]  = additional_payment_data[:return_url] if additional_payment_data[:return_url].present?
          values.merge!(additional_payment_data[:items])
        else
          AdditionalPayment.find_by_id(additional_payment_id).line_item_addons.each_with_index do |item,index|
            values.merge!({
              "amount_#{index+1}" => CurrencyConvert.to_currency(currency_symbol, item.snapshot_price, country_code).round(2),
              "item_name_#{index + 1}" => item.addon_type_value.name,
              "item_number_#{index + 1}" => item.addon_type_value.id,
              "quantity_#{index + 1}" => item.line_item.quantity
            })
          end
        end
      else
        discount_price = CurrencyConvert.to_currency(currency_symbol, self.total_discounts, country_code).round(2)
        shipping_cost = CurrencyConvert.to_currency(currency_symbol, self.shipping, country_code).round(2)
        line_items_count = 0
        line_items.each_with_index do |item, index|
          values.merge!({
            "amount_#{index + 1}" => CurrencyConvert.to_currency(currency_symbol, item.price_with_addons, country_code).round(2),
            "item_name_#{index + 1}" => item.design.title,
            "item_number_#{index + 1}" => item.design.id,
            "quantity_#{index + 1}" => item.quantity
          })
          line_items_count += 1
        end
        if order_addon.try(:gift_wrap_price)
          index = line_items_count + 1
          values.merge!({
            "amount_#{index}" => CurrencyConvert.to_currency(currency_symbol, order_addon.gift_wrap_price, country_code).round(2),
            "item_name_#{index}" => 'Gift Wrap Charges',
            "item_number_#{index}" => order_addon.id,
            "quantity_#{index}" => 1
          })
        end
      end
    end
    values[:discount_amount_cart] = discount_price if discount_price.present?
    values[:handling_cart] = paypal_request.get_shipping_cost
    tax_amount = self.total_tax.present? ? self.total_tax : 0 
    tax_amount = CurrencyConvert.to_currency(currency_symbol, tax_amount, country_code).round(2)
    values[:handling_cart] += tax_amount 
    country = self.billing_country

    country_obj = Country.find_by_namei_cached(country)
    code = country_obj.try(:[], :iso3166_alpha2)
    state_code = paypal_request.get_state_code
    local_code = "en_US" #['FR','DE'].include?(country_code) ? 'US' : country_code

    if code == 'US'
      phone = Phonelib.parse(self.billing_phone,'US')
      if phone.valid_for_country? 'US'
        international_phone = phone.international.split(' ')[1].split('-')
        values[:night_phone_a] = international_phone[0]
        values[:night_phone_b] = international_phone[1]
        values[:night_phone_c] = international_phone[2]
      end
    end

    [:email, :H_PhoneNumber, :first_name, :last_name, :zip, :city, :address1, :address2].each do |request_key|
      if paypal_request.respond_to?(method_name = ("get_#{request_key}".to_sym))
        values[request_key] = paypal_request.send(method_name)
      end
    end
    values[:country] = country_code
    values[:lc] = local_code
    if self.pay_type == PAYMENT_GATEWAY
      values[:landing_page] = "Billing"
    else
      values[:landing_page] = "Login"
    end
    values[:state] = state_code

    paypal_server_url + values.to_query
  end

  def allow_reject!
    true
  end

  def state_enum
    Order.state_machine.states.map &:name
  end

  def reduce_quantity_from_warehouse
    if self.international? || self.domestic?
      line_items.map{|li| li.warehouse_use_item(li.variant || li.design)}
    elsif ALLOWED_DOMESTIC_SOR
      rack_list_wli = {}
      all_designer_orders = self.designer_orders
      all_designer_orders.each do |dos|
        not_all_sor_items = dos.line_items.any? do |li|
          item_design = (li.variant || li.design)
          item_design.in_stock_warehouse.to_i < li.quantity && (rack_list_wli[li.id] = li.get_new_rack_list_item).blank?
        end
        next if not_all_sor_items
        dos.line_items.each{|li| li.warehouse_use_item((li.variant || li.design), rack_list_wli[li.id])}
      end
    end
  end

  def create_scope_score
    ScopeScore::Scopes.each do |scope_name|
      send("create_#{scope_name}_score") unless send("#{scope_name}_score")
      designer_orders.preload("#{scope_name}_score").each do |designer_order|
        designer_order.send("create_#{scope_name}_score") unless designer_order.send("#{scope_name}_score")
      end
      line_items.preload(:order_quality_score).each do |line_item|
        line_item.create_order_quality_score unless line_item.order_quality_score
      end
    end
  end

  def is_cargo_shipment?
    confirmed_at.present? && international? && (total_paid_amount = get_user_total_paid_amount) > (CSB_LIMITS[best_shipper.to_s] || 25000)
  end

  handle_asynchronously :create_scope_score

  #  Cash on Delivery
  #
  #   pending state ----bad-data---> fraud state (found that data was bad. Never delete stuff.)
  #        |
  #        |----------> reject state (We called up customer / designer and order can't go through)
  #        |
  #        |----> scheduled state ---pickup--> dispatched state ---> paid --> completed state
  #
  #    State will be restored in [:fraud, :cancel] state.
  #
  #
  # order state machine (see http://github.com/pluginaweek/state_machine/tree/master for details)
  state_machine :initial => :new do
    event :bad_data do
      transition :new => :cancel
    end

    event :non_cod_area do
      transition :new => :pending
    end

    event :require_confirmation do
      transition new: :pending
    end

    event :require_followup do
      transition new: :followup
    end

    event :move_non_essential_order do
      transition [:new, :pending, :confirmed] => :followup
    end

    event :gharpay, :cod do
      transition :new => :pending
    end

    event :paypal_pending do
      transition :new => :pending
    end

    event :buyer_confirmation, :system_confirm do
      transition :pending => :sane
    end

    event :cancel do
      transition all - [:complete, :cancel] => :cancel
    end

    event :confirmed_order do
      transition [:new, :pending] => :confirmed
    end

    event :good_data do
      transition [:new, :pending, :confirmed, :fraud, :followup] => :sane
    end

    event :archive_data do
      transition all - [:cancel] => :complete
    end

    event :fraud_alert do
      transition all => :fraud
    end

    event :now_safe do
      transition :fraud => :sane
    end

    event :buyer_dissatisfied do
      transition :complete => :reject
    end

    event :reactivate do
      transition :cancel => :sane
    end

    event :move_to_pending do
      transition :cancel => :pending
    end

    event :move_to_followup do
      transition :cancel => :followup
    end

    event :package_shipped do
      transition [:sane, :ready_for_dispatch, :partial_dispatch] => :dispatched
    end

    event :partial_package_shipped do
      transition [:sane, :pickedup] => :partial_dispatch
    end

    event :package_pickedup do
      transition [:sane] => :pickedup
    end

    event :cancel_followed_up do
      transition [:cancel] => :cancel_complete
    end

    event :items_ready_dispatch do
      transition :sane => :ready_for_dispatch ######### do not allow partial_dispatch to ready_for_dispatch
    end

    state :sane do
      validate :order_not_in_blacklist
    end

    event :invalid_wallet do
      transition all => :pending
    end

    before_transition to: :ready_for_dispatch do |order|
      order.ready_for_dispatch_at = DateTime.now unless order.ready_for_dispatch_at.present?
    end

    after_transition :to => :ready_for_dispatch do |order|
      if order.ready_for_dispatch_at.try(:today?)
        OrderMailer.sidekiq_delay.all_line_items_notification(order.id, 'rfd')
        # order.sidekiq_delay.send_line_items_notification('rfd')
        SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "send_line_items_notification", 'rfd')
        if order.is_cargo_shipment? && !order.shipment_buckets.exists?
          # order.sidekiq_delay(queue: 'high').divide_order_in_shipment_buckets
          SidekiqDelayGenericJob.set({queue: 'high'}).perform_async(order.class.to_s, order.id, "divide_order_in_shipment_buckets")
        else
          unless order.is_pbr_order?
            process_date_rfd = ((process_date = order.rfd_process_date) ? process_date.lpst_date.to_date : nil)
            CreateSearchEntityJob.perform_async(order.class.to_s, order.id, 'RfdSearcherEntity', process_date_rfd)
          end
        end
      end
      AppEvent::OrderEvent.new(order.id, "Ready For Dispatch").trigger_clevertap_event_deliver_later
      order.ready_for_dispatch_at = DateTime.current
      order.skip_before_filter = true
      order.save
    end

    after_transition :to => :pickedup do |order|
      AppEvent::OrderEvent.new(order.id, "Order picked").trigger_clevertap_event_deliver_later
    end

    after_transition :to => :fraud do |order|
      order.waiting_on_confirmation!
      order.designer_orders.each do |d|
        d.move_to_new!
        OrderMailer.send_fraud_alert_to_designers(order, d).deliver
      end
      OrderMailer.send_fraud_alert_to_buyer(order).deliver
    end

    after_transition :to => :dispatched do |order,transition|
      unless order.pickup.present?
        # order.sidekiq_delay.send_confirmation_sms_international('dispatched') if order.billing_international? && order.tracking_number.present? 
        SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "send_confirmation_sms_international", 'dispatched') if order.billing_international? && order.tracking_number.present?
        SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "mail_tracking_info_to_international_buyer", {"#{order.class}": order.id},after_partial_dispatch = transition.from_name == :partial_dispatch)
        # OrderMailer.sidekiq_delay.mail_tracking_info_to_international_buyer(order)
        # order.sidekiq_delay(queue: 'critical').mark_order_as_stitching
        SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(order.class.to_s, order.id, "mark_order_as_stitching")
        order.pickup = DateTime.current
        order.save
        if (stylist = order.stylist).present?
          stylist.orders_count -= 1
          stylist.save
        end
        WarehouseBucket.bulk_update_warehouse_buckets(bucket_type: 'PackagingBucket', item_ids: order.line_items.to_a.map(&:id), item_type: 'LineItem', new_state: 'Order Dispatched')
        order.remove_rack_quantity
      end
      if ticket = order.tickets.active_tickets.where(department: 'operations',issue: 'Delayed Delivery').first
        ticket.resolved_at = Time.zone.now
        ticket.resolve_message = "Ticket Auto Resolved By System. Tracking Number: #{order.tracking_number} , Courier Partner: #{order.courier_company}"
        ticket.resolved_by = nil
        ticket.resolve_ticket!
      end
      # order.sidekiq_delay(queue: 'low').update_cost_estimation if order.international?
      SidekiqDelayGenericJob.set({queue: 'low'}).perform_async(order.class.to_s, order.id, "update_cost_estimation") if order.international?
      AffiliateOrder.notify_state_of(order)

      AppEvent::OrderEvent.new(order.id, "Order Dispatched").trigger_clevertap_event_deliver_later
    end

    after_transition :cancel => [:followup] do |order|
      order.calculate_shipping_for_pending_and_sane if order.international?
      order.designer_orders.each do |d|
        d.move_to_new! if d.line_items.present? && d.state == 'canceled'
      end
    end

    after_transition new: :followup do |order|
      if order.cart.present? && BANK_DEPOSIT == order.pay_type
        order.destroy_cart_references
      end
      order.designer_orders.each do |d|
        d.line_items.where(status: nil).each do |li|
          li.warehouse_cancel_item(li.variant || li.design)
        end
        d.restore_state
      end
    end

    before_transition followup: :sane do |order|
      order.other_details['valid_followup'] = 'true' if order.bank_deposit? && order.confirmed_at.blank?
    end

    after_transition :followup => [:sane] do |order|
      is_order_valid_for_unicommerce_vendor = (order.other_details['valid_followup'].present? || order.other_details['system_order_cancel'].present?)
      order.designer_orders.each do |d|
        if d.line_items.present? && d.state == 'new'
          d.order_looks_sane!
          d.change_lineitem_design_quantity if !d.designer.is_unicommerce_vendor || is_order_valid_for_unicommerce_vendor
        end
      end
      # order.sidekiq_delay.delete_system_order_keys if is_order_valid_for_unicommerce_vendor
      if order.country_code.downcase == 'in'
        Order.sidekiq_delay.call_create_gokwik_api(order.id)
        Order.sidekiq_delay.call_gokwik_split_api(order.id)
      end
      SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "delete_system_order_keys") if is_order_valid_for_unicommerce_vendor
      # order.sidekiq_delay(queue: 'critical').reduce_quantity_from_warehouse
      SidekiqDelayGenericJob.set(queue: 'critical').perform_async(order.class.to_s, order.id, "reduce_quantity_from_warehouse")
    end

    before_transition :to => [:pending] do |order|
      order.do_wallet_transaction(:wait)
      # Order was confirmed earlier but now it is canceled.
      order.waiting!

      if order.pending_at.blank?
        order.pending_at = DateTime.current
        order.save
      end

      if order.state == 'cancel'
        order.calculate_shipping_for_pending_and_sane if order.international?
        order.designer_orders.each do |d|
          if d.line_items.present? && d.state == 'canceled'
            d.move_to_new!
            d.post_order_design_quantity_reduce
          end
        end
      end
    end

    before_transition all - [:new] => :cancel do |order|
      # order.sidekiq_delay.send_line_items_notification('cancelled') unless order.tag_list.include?('oos')
      SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "send_line_items_notification", 'cancelled') unless order.tag_list.include?('oos')
    end

    before_transition to: :cancel do |order|
      order.other_details['system_order_cancel'] = 'Payment Issue' if order.increment_item_quantity
    end

    after_transition :to => [:pending] do |order|
      if order.cart.present? && [COD, GHARPAY, BANK_DEPOSIT].include?(order.pay_type)
        order.destroy_cart_references
      end
    end

    after_transition :to => :confirmed do |order|
      unless order.confirmed_at?
        # order.confirmed_at = DateTime.current
        order.paid_amount = order.total
        order.paid!
        order.save
      end
      order.designer_orders.each do |d|
        d.move_to_new! if d.canceled? && d.line_items.present?
      end
    end

    after_transition :to => :sane do |order|
      #
      # Data doesn't look like fraud. Let's send purchase order to designers.
      #
      order.reduce_quantity_from_warehouse unless order.bank_deposit?
      if order.geo == 'international' && !order.cod?
        ActiveRecord::Associations::Preloader.new.preload(order, [:tags, :events, line_items: [line_item_addons: :addon_type_value]])
        found = []
        if order.tags.map(&:name).include?('addon') && order.line_items.all?{|item| (item.stitching_required.blank?) ||(((values = item.line_item_addons.map(&:addon_type_value).map(&:name)) & ADDONS_FOR_STITCHING).blank? && (found << (values & ['Fall and Pico','Petticoat Stitching'])) && found.last.present? )}
            order.remove_tags_skip_callback('addon')
            found.flatten.uniq.each do |tag|
              specific_tag_name = (tag == 'Fall and Pico') ? 'FNP' : 'PCOT'
              order.add_tags_skip_callback(specific_tag_name)
              order.add_notes_without_callback("Added tag #{specific_tag_name} to Order",'stitching')
            end
        end
        # order.sidekiq_delay.assign_epst_lpst_process_dates if order.process_dates.blank?
        Order.sidekiq_delay.call_create_gokwik_api(order.id)
        Order.sidekiq_delay.call_gokwik_split_api(order.id)
        Order.send_document_to_shipper(order.id,'National ID') if order.country == 'South Africa' && (user = order.user).present? && user.kyc_documents.exists?(name: 'National ID') 
        SidekiqDelayGenericJob.perform_in(1.minutes.from_now,order.class.to_s, order.id, "assign_epst_lpst_process_dates") if order.process_dates.blank?
      end

      #Send an email to the customer asking him/her to send accurate address.
      if order.check_for_po_box_presence
        OrderMailer.sidekiq_delay.send_mail_to_customer_regarding_po_box_issue(order.name,order.email)
      end

      if order.gharpay?
        # Send confirmation email to buyer saying payment was collected.
        OrderMailer.gharpay_payment_confirmed(order).deliver
      end
      order.add_notes('Marked sane.', false)
      order.add_notes_without_callback('Marked sane.', 'state_change')

      unless order.confirmed_at?
        order.confirmed_at = DateTime.current
        if (order.international? || order.actual_country_code != 'IN') && !order.cod?
          # order.sidekiq_delay(queue: 'critical').generate_order_invoice
          SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(order.class.to_s, order.id, "generate_order_invoice")
        else
          # order.sidekiq_delay(queue: 'critical').mark_order_as_stitching
          SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(order.class.to_s, order.id, "mark_order_as_stitching")
        end
        if order.payment_state != 'completed'
          order.paid_amount = order.total
          order.paid!
        end
      end

      unless (notify = Notification.find_by_user_id(order.user_id)).present? && notify.body.present?
        Notification.create(user_id: order.user_id, body: [CustomerPanel.build_customer_notification(order, 'order', 'confirmed')])
      else
        notify.body << CustomerPanel.build_customer_notification(order, 'order', 'confirmed')
        notify.save!
      end
      if order.country == 'Canada'
        if order.city.include?('(')
          order.city = order.city.split(")").collect{|x| x.split("(")[0]}.join.strip()
        end
      end
      order.save
      order.calculate_shipping_for_pending_and_sane if order.international?

      # Send out sms for confirmation
      # if number belongs to India and check if it is mobile number
      # Remove all symbols and space from it.
      if order.billing_international?
        # order.sidekiq_delay.send_confirmation_sms_international('confirmed')
        SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "send_confirmation_sms_international", 'confirmed')
      else
        # order.sidekiq_delay.send_confirmation_sms
        SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "send_confirmation_sms")
      end
      # order.sidekiq_delay.send_line_items_notification('confirmed')
      SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "send_line_items_notification", 'confirmed')
      order.designer_orders.each do |d|
        if d.line_items.present? && d.total > 0 && d.state == "new"
          d.order_looks_sane!
        elsif d.line_items.present? && d.state == 'canceled'
          d.reactivate!
          SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "dispatch_order_to_designer", {"#{order.class}": order.id}, {"#{d.class}": d.id})
          #OrderMailer.sidekiq_delay.dispatch_order_to_designer(order, d)
          d.post_order_design_quantity_reduce
        end
      end
      SalesRegister.sidekiq_delay(queue: 'high')
                   .create_new_entry("order_sane",'sales',SalesRegister.object_to_json([order]))
      order.check_mailers_send
      # order.sidekiq_delay_until(STITCHING_NOTIFICATION_DELAY['delay_time'].hours.from_now).send_stitching_notification if order.line_items.any?{|l| l.stitching_required == 'Y' && l.status.blank?} && STITCHING_NOTIFICATION_DELAY['app_source'].include?(order.app_source.split('-').first) && order.international?
      SidekiqDelayGenericJob.perform_in(STITCHING_NOTIFICATION_DELAY['delay_time'].hours.from_now ,order.class.to_s, order.id, "send_stitching_notification") if order.line_items.any?{|l| l.stitching_required == 'Y' && l.status.blank?} && STITCHING_NOTIFICATION_DELAY['app_source'].include?(order.app_source.split('-').first) && order.international?
      # order.sidekiq_delay.update_expected_dates
      SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "update_expected_dates")
      # order.sidekiq_delay(queue: 'low').update_cost_estimation if order.international?
      SidekiqDelayGenericJob.set({queue: 'low'}).perform_async(order.class.to_s, order.id, "update_cost_estimation") if order.international?
      order.share_and_earn_reward

      design_ids = order.line_items.collect(&:design_id)
      Wishlist.where(design_id: design_ids, user_id: order.user_id).update_all(state: 'ordered') if design_ids.present? && order.user_id.present?

      AffiliateOrder.create_from(order)
   
      SidekiqDelayGenericJob.perform_async("StitchingMeasurement", nil, "create_measurement", {"#{order.class}": order.id})
      # StitchingMeasurement.sidekiq_delay.create_measurement(order)
      # order.sidekiq_delay.address_verification(true) if order.international?
      SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "address_verification", true) if order.international?      
      # order.sidekiq_delay(queue: 'low').gokwik_create_order_api(order) if order.domestic? && (Rails.env.production? || Rails.env.admin?)
      # SidekiqDelayGenericJob.set({queue: 'low'}).perform_async(order.class.to_s, order.id, "gokwik_create_order_api", {order.class.to_s => order.id}) if order.domestic? && (Rails.env.production? || Rails.env.admin?)
      if order.domestic?
        classiq_items = order.line_items.joins(:design).where(designs: { designer_id: 12727 }, qc_done: nil, received: nil)
        if classiq_items.present?
          classiq_items.update_all(qc_done: 'Y',received: 'Y')
        end
      end
    end

    after_transition :to => [:confirmed, :sane] do |order|
      if order.cart.present?
        order.destroy_cart_references
      end
      order.create_scope_score
      if (Time.current < Date.parse(REFERRALS_EXPIRY)) && REFERRAL_TYPES.include?('first_new_order') && (referral = Referral.find_by_order_id(order.id)).present? && !referral.credited && (!order.cod? && order.payment_completed?)
        referral.transfer_to_wallet
      end
      Order.sidekiq_delay.call_create_gokwik_api(order.id)
      Order.sidekiq_delay.call_gokwik_split_api(order.id)
      order.do_wallet_transaction(:deduct_wallet_amount)
      AppEvent::OrderEvent.new(order.id, "Order Confirmation").trigger_clevertap_event_deliver_later
    end

    before_transition followup: :cancel do |order|
      order.other_details[:previous_state] = 'followup'
    end
    
    after_transition :to => :cancel do |order|
      SalesRegister.sidekiq_delay(queue: 'high').create_new_entry("order_cancel",'sales_return',SalesRegister.object_to_json([order])) if order.paid_amount.to_f > 0
      order.save_shipping_cost if order.international?
      order.save_order_addons_cost if order.order_addon.present?
      if order.pay_type == PAYMENT_GATEWAY && order.international? && (token = order.token).present?
        # order.sidekiq_delay(queue: 'high').error_capture_through_ec_token(token)
        SidekiqDelayGenericJob.set({queue: 'high'}).perform_async(order.class.to_s, order.id, "error_capture_through_ec_token", token)
      end
      order.save_order_cancel_reason(*order.order_cancel_reason)
      order.cancel_each_designer_order
      order.failure!
      if order.geo == 'international'
        # order.sidekiq_delay.generate_trikon_api_url
        SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "generate_trikon_api_url")
      end
      # If the order was gharpay order, notify gharpay to no longer proceed with the order
      if order.gharpay?
        # You can no longer cancel order via API
        #@gharpay = Gharpay::Base.new(Mirraw::Application.config.gharpay_username, Mirraw::Application.config.gharpay_password)
        #@gharpay.cancel_order({:orderID => order.gharpay_order_id})
      end
      # order.sidekiq_delay(queue: 'low').update_cost_estimation if order.international?
      SidekiqDelayGenericJob.set({queue: 'low'}).perform_async(order.class.to_s, order.id, "update_cost_estimation") if order.international?
      order.wallet_transactions.find_by_fund_type('Loyalty_Reward').try(:cancel)
      order.wallet_transactions.find_by_fund_type('Share_And_Earn_Reward').try(:cancel)
      AppEvent::OrderEvent.new(order.id, "Order Cancelled").trigger_clevertap_event_deliver_later
    end
    
    after_transition [:new] => :cancel do |order|
      order.line_items.where(available_in_warehouse: [true, nil]).update_all(available_in_warehouse: false)
      order.events.where(notes: "Ready_To_Ship_Order_SOR", note_type: 'delivery').delete_all
    end

    after_transition [:new, :pending] => :cancel do |order|
      if !order.tag_list.include?('oos') && (order.created_at.to_date > 1.month.ago.to_date)
        OrderMailer.sidekiq_delay.convey_cancel_update_to_buyer(order.id)
      end
    end

    after_transition all - [:new, :pending] => :cancel do |order|
      if !order.tag_list.include?('oos') && (order.created_at.to_date > 1.month.ago.to_date)
        SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "cancel_order_update_to_user", {"#{order.class}": order.id})
        #OrderMailer.sidekiq_delay.cancel_order_update_to_user(order)
      end

      AffiliateOrder.notify_state_of(order)
    end

    after_transition :cancel => [:pending, :sane, :followup] do |order|
      SidekiqDelayGenericJob.perform_async("ScopeScoreEvent", nil, "apply_remove_cancel_event", {"#{order.class}": order.id}, :remove)
      #ScopeScoreEvent.sidekiq_delay.apply_remove_cancel_event(order, :remove)
      Order.sidekiq_delay.call_gokwik_split_api(order.id)
    end
    
    
    after_transition any => [:cancel, :followup] do |order| 
      order.set_total_order_value
      order.save
    end
  end

  # pending, completed, failed, void
  state_machine :payment_state, :initial => :pending_payment do
    event :paid do |designer|
      transition [:pending_payment, :failed] => :completed
    end

    event :failure do
      transition [:pending_payment, :completed, :failed, :processing] => :failed
    end

    event :discard do
      transition [:pending_payment, :completed] => :void
    end

    event :waiting do
      transition [:failed, :pending_payment] => :pending_payment
    end

    event :waiting_on_confirmation do
      transition [:completed] => :processing
    end

    event :payment_confirmed do
      transition [:processing] => :completed
    end
  end

  def self.notify_exceptions(title = nil, error_message = nil, params = {})
    ExceptionNotifier.notify_exception(
      Exception.new(title),
      data: {message: error_message ,params: params} 
    )
  end


  def order_not_in_blacklist
    errors.add(:id,'Customer is Blacklisted') if self.other_details.try(:[],"blacklist_user").present?
  end

  def deduct_shipping_from_wallet(cart_return_amount)
    if international? && (credit_left = user.wallet_amounts.return_amount - cart_return_amount) > 0
      converted_shipping = shipping/currency_rate
      [credit_left, converted_shipping].min
    end
  end

  def do_wallet_transaction(event=nil)
    if refund_discount.to_f > 0 || referral_discount.to_f > 0
      order_transaction = WalletTransaction.where(order_id: id, user_id: user_id, wallet_id: user.wallet_id, referral_amount: referral_discount.to_f, return_amount: refund_discount.to_f, return_id: nil).first_or_create
      unless order_transaction.state == 'order_complete' || event.blank?
        order_transaction.fire_state_event(event) if order_transaction.send("can_#{event}?")
      end
    end
  end

  def address_verification(during_sane=nil)
    state_code_countries = 'US', 'IN', 'CA', 'AE'
    order_street = street.tr("\n"," ").tr("\r"," ").gsub("&","and").strip
    street_address = multi_line_address([(order_street.length * 0.7).to_i,35].min, order_street)
    shipping_country_code = Country.find_by_namei_cached(self.country).try(:[], :iso3166_alpha2) || country_code
    dhl_service = IDHLService.new
    r = {'address1' => street_address[0],
    'address2' => street_address[1] || "",
    'address3' => street_address[2] || "",
    'postalCode' => pincode,
    'city' => city,
    'division' => (state_code_countries.include?(shipping_country_code) ? state_code : buyer_state ),
    'countryCode' => shipping_country_code,
    'countryName' => country
    }
    response = dhl_service.postCapability(r)
    service_area_code = Hash.from_xml(response.postCapabilityResult)['ServiceAreaCode']
    if service_area_code.present?
      self.remove_tags_skip_callback('Invalid Address')
    else
      add_tags_skip_callback('Invalid Address')
      OrderMailer.invalid_address_mail(self.id) if during_sane
    end
  rescue => error
    puts "DHL address verification api error"
  end

  def prepaid?
    !post_paid?
  end

  def post_paid?
    bank_deposit? || cod? || gharpay?
  end

  def wallet_transaction(custom_wallet_transactions=[])
    wallet_txn = custom_wallet_transactions.presence || wallet_transactions
    wallet_txn.select do |wt|
      wt.order_id == self.id && wt.return_id==nil && (custom_wallet_transactions.present? ? true : wt.user_id == user_id && wt.wallet_id == user.try(:wallet_id))
    end.first
  end

  def cancel_each_designer_order
    self.designer_orders.each do |designer_order|
      if designer_order.can_cancel?
        designer_order.cancel_reason = self.cancel_reason
        designer_order.cancel!
      end
    end
  end

  def eligible_for_shipconsole?
    ENV['ENABLE_SHIPCONSOLE'] == 'true' && best_shipper.to_s.downcase == 'ups' && country == 'United States' && currency_code == 'USD'
  end


  def post_order_confirmation_work
    SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "mail_order_details_to_buyer", {"#{self.class}": self.id})
    #OrderMailer.sidekiq_delay.mail_order_details_to_buyer(self)
    if bank_deposit? && self.can_require_followup?
      self.require_followup!
    elsif gharpay?
      SidekiqDelayGenericJob.perform_async("OrdersController", nil, "gharpay_create_order", {"#{self.class}": self.id})
      #OrdersController.sidekiq_delay.gharpay_create_order(self)
      designer_orders.each do |designer_order|
        SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "dispatch_order_to_designer", {"#{self.class}": self.id}, {"#{designer_order.class}": designer_order.id})
        #OrderMailer.sidekiq_delay.dispatch_order_to_designer(self, designer_order)
      end
      self.gharpay!
    end

  end

  def mark_order_as_stitching(to_save = true)
    reload
    li_count  = LineItem.unscoped.joins(:designer_order).select("count(distinct(case when stitching_required = 'Y' then line_items.id end)) as reqs_count, count(distinct (case when stitching_done = 'Y' then line_items.id end)) as done_count").where('line_items.status is null and designer_orders.order_id = ? and designer_orders.state not in (?)',self.id,['canceled','vendor_canceled'])[0]
    stitching = li_count.try(&:reqs_count).to_i > 0
    self.other_details['stitching_order'] = (stitching ? 'true'  : 'false')
    self.other_details['stitching_done'] = ((stitching && li_count.try(&:reqs_count).to_i == li_count.try(&:done_count).to_i)  ? 'true'  : 'false')
    if to_save
      self.skip_before_filter = true
      self.save!
    end
  end

  def save_shipping_cost
    new_notes = "#{self[:notes]}--- system_shipping_cost = #{self[:shipping]} ---"
    self.update_column(:notes,new_notes)
    self.notes = new_notes
  end

  def save_order_addons_cost
    new_notes = "--- order_addon_cost = #{order_addon.get_total_order_addon_charges} ---"
    add_notes(new_notes, false)
  end

  def blacklist_user_order
    order = self
    email = order.email
    billing_email = order.billing_email
    ip_address = order.ip_address
    pincode = order.pincode
    billing_pincode = order.billing_pincode
    blacklist = Blacklist.select('count(email) as email_count,count(ip_address) as ip_address_count,count(pincode) as pincode_count')
    .where("email in (?) or ip_address = ? or pincode in (?)",[email,billing_email],ip_address,[pincode,billing_pincode])[0]
    if blacklist.present? && ((email.present? && blacklist.email_count.to_i > 0) || (ip_address.present? && blacklist.ip_address_count.to_i > 0) || (billing_email.present? && blacklist.email_count.to_i > 0)|| (pincode.present? && blacklist.pincode_count.to_i > 0) || (billing_pincode.present? && blacklist.pincode_count.to_i > 0))
      order.other_details['blacklist_user'] = true
    end
  end

  # FIXME refactor this method and implement validation using validates_* utilities
  # def generate_order_number
  #   record = true
  #   while record
  #     random = "M#{Array.new(9){rand(10)}.join}"
  #     record = self.class.unscoped.where("number = ?", random).first
  #   end
  #   self.number = random if self.number.blank?
  #   self.number
  # end

  def self.generate_unique_order_number(length=9)
    record = true
    while record
      random = "M#{Array.new(9){rand(10)}.join}"
      record = Order.unscoped.where(number: random).exists?
    end
    random
  end

  def set_order_number
    # begin
    #   if (unique_order_number = Redis.current.spop('order_number')).present?
    #     order_number = unique_order_number
    #   else
    #     raise StandardError.new "Redis store empty"
    #   end
    # rescue RuntimeError, StandardError => e
    order_number = Order.generate_unique_order_number
    # end
    self.number = order_number if self.number.blank?
  end

  def cod_attempted_not_verified?
    self.pending? && self.cod_verify_msg_count > 0 ? true : false
  end

  def cod_verification_attempt_pending?
    self.cod_verify_msg_count == 0 ? true : false
  end

  def cancel_order
    self.order_cancel_reason = 'Cancelled By User'
    self.order_notification.delete('order_cancelled')
    self.cancel_order_increase_quantity if can_cancel?
  end

  def self.paypal_retry_from_mobile(order_id)
    if (order = Order.find_by_id(order_id))
      order.move_to_pending if order.cancel?
      order.cancel_unless_confirmed
      SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "mail_order_details_to_buyer", {"#{order.class}": order.id})
      #OrderMailer.sidekiq_delay.mail_order_details_to_buyer(order)
    else
      ExceptionNotify.sidekiq_delay.notify_exceptions('Paypal order retry', 'Paypal order not found!',{order_id: order_id})
    end
  end

  def cancel_unless_confirmed
    if self.state == 'cancel'
      return
    elsif self[:notes].to_s.exclude?('SUCCESS') && self.state != 'cancel'
      if self.cod? && self.cod_attempted_not_verified?
        self.order_cancel_reason = 'Order not confirmed'
        self.cancel_order_increase_quantity
      else
        if payment_status_under_process?
          delay({queue: 'high_priority', priority: -3, run_at: 60.minutes.from_now}).cancel_unless_confirmed
        else
          unless (self.state == 'pending' || self.state == 'sane' || self.state == 'followup')
            self.order_cancel_reason = 'Order not confirmed'
            self.cancel_order_increase_quantity
          end
        end
      end
    else
      self.confirmed_order! if self.can_confirmed_order?
      if (self.international? && self.has_home_decor?)
        # Do nothing
      elsif self.paypal_payment_type == 'echeck'
        # Do nothing
      else
        # This method purpose is to cancel unless confirmed
        # Check good_data here is not proper way.
        self.good_data! if self.can_good_data?
      end
    end
  end

  # input params - none
  #
  # returns true if home_decor designs present in order And returns false if ONLY 'home-furnishing items are present in order'
  def has_home_decor?
    home_decor_cat_ids = Category.find_by_namei('home-decor').self_and_descendants.pluck(:id)
    home_decor_allowed_categories_names = ['home-furnishing']
    home_decor_design_count = self.line_items.joins(:design => :categories).where('categories.id IN (?)', home_decor_cat_ids).count
    if home_decor_design_count > 0
      order_has_not_allowed_items = true
      temp = self.designs.map{|d| d.category_parents_name}
      temp.each do |temp|
        if (temp.include?('home-decor')&&(temp & home_decor_allowed_categories_names).empty?)
          order_has_not_allowed_items = true
          break
        else
          order_has_not_allowed_items = false
        end
      end
      return order_has_not_allowed_items
    else
      false
    end
  end

  def line_items_has_notes?
    self.line_items.where('line_items.note IS NOT NULL').count > 0 ? true : false
  end

  def self.gharpay_cancel_products(order, designs)
    # You can no longer cancel products from the orderID
  end

  def copy_billing_details_to_shipping
    self.name = self.billing_name
    self.email = self.billing_email
    self.phone = self.billing_phone
    self.street = self.billing_street
    self.city = self.billing_city
    self.buyer_state = self.billing_state
    self.country = self.billing_country
    self.pincode = self.billing_pincode
  end

  def copy_shipping_details_to_billing
    self.billing_name = self.name
    self.billing_email = self.email
    self.billing_phone = self.phone
    self.billing_street = self.street
    self.billing_city = self.city
    self.billing_state = self.buyer_state
    self.billing_country = self.country
    self.billing_pincode = self.pincode
  end

  def set_buyer_details_and_address(buyer_details,shipping_address,billing_address)
    self.name = buyer_details["BuyerName"] || shipping_address["Name"]
    self.billing_email = self.email = buyer_details["BuyerEmailAddress"]
    self.billing_name = billing_address["Name"]
    self.phone = shipping_address["PhoneNumber"] || billing_address["PhoneNumber"] || "Not received"
    self.billing_phone = billing_address["PhoneNumber"] || shipping_address["PhoneNumber"] || "Not received"
    self.street = "#{shipping_address['AddressFieldOne']} #{shipping_address['AddressFieldTwo']} #{shipping_address['AddressFieldThree']}".strip
    self.billing_street = "#{billing_address['AddressFieldOne']} #{billing_address['AddressFieldTwo']} #{billing_address['AddressFieldThree']}".strip
    self.city = shipping_address["City"]
    self.billing_city = billing_address["City"]
    self.buyer_state = shipping_address["State"]
    self.billing_state = billing_address["State"]
    self.country_code = self.actual_country_code = shipping_address["CountryCode"]
    self.country = Country.country_name(shipping_address["CountryCode"])
    self.billing_country = Country.country_name(shipping_address["CountryCode"])
    self.pincode = shipping_address["PostalCode"]
    self.billing_pincode = billing_address["PostalCode"]
  end

  def _billing_name
    self.billing_name? ? self.billing_name : self.name
  end

  def _billing_city
    self.billing_city? ? self.billing_city : self.city
  end

  def _billing_state
    self.billing_state? ? self.billing_state : self.buyer_state
  end

  def _billing_country
     self.billing_country? ? self.billing_country : self.country
  end

  def _billing_pincode
    self.billing_pincode? ? self.billing_pincode : self.pincode
  end

  def _billing_email
    self.billing_email? ? self.billing_email : self.email
  end

  def _billing_phone
    self.billing_phone? ? self.billing_phone : self.phone
  end

  def _billing_street
    self.billing_street? ? self.billing_street : self.street
  end

  def billing_address
    "Street: " + self._billing_street + " , " + self._billing_city + " - " +  self._billing_pincode.to_s  + " , " + self._billing_state + " , " + self._billing_country
  end

  def shipping_address
    "Street: " + self.street + " , " + self.city + " - " +  self.pincode.to_s  + " , " + self.buyer_state + " , " + self.country
  end

  def get_shipping_cost(is_free_shipping=false)
    ActiveRecord::Associations::Preloader.new.preload(self, designer_orders: {line_items: {design: :categories}})
    country_code = if (country_hash = CurrencyConvert.currency_convert_memcached.find{|c| c.country.downcase == country.downcase}).present?
      country_hash.country_code
    end
    weight = is_free_shipping ?  get_category_weight : get_weight
    Country.shipping_cost_for(weight, country)
  end
  
  def get_category_weight
    value = 0
    non_canceled_line_items = designer_orders.inject([]) do |lis, dos|
      lis.push(*dos.line_items.not_canceled) unless dos.state == "canceled"
      lis
    end
    non_canceled_line_items.each do |line_item|
      value += line_item.approx_weight if ((line_item.design.categories.pluck(:id) & EXCLUDE_FREE_SHIPPING_CATEGORIES).present? || [1,2,3].include?(line_item.buy_get_free))
    end
    value
  end
  
  def get_weight
    non_canceled_line_items = designer_orders.inject([]) do |lis, dos|
      lis.push(*dos.line_items.not_canceled) unless dos.state == "canceled"
      lis
    end
    return non_canceled_line_items.sum{|item| item.approx_weight(country_code) }
  end
  
  def total_price_currency_for_exclude_shipping
    line_items = self.get_cart_items.map{|line_item| line_item if (line_item.design.categories.pluck(:id) & EXCLUDE_FREE_SHIPPING_CATEGORIES).blank? && ![1,2,3].include?(line_item.buy_get_free)}.compact
    total = 0
    line_items.each do |item|
      total += (item.snapshot_price).round(2) * item.quantity
    end
    total = total.round(2)
    total = (total - self.referral_discount.to_f).round(2)
  end
  
  def order_has_bmgn_products?
    get_cart_items.where(buy_get_free: 1).present? && PromotionPipeLine.bmgnx_hash.present?
  end
  
  def get_cart_items
    if self.new_record? && self.cart.present?
      line_items =  self.cart.line_items
    else 
      line_items = self.line_items  
    end
    return line_items
  end

  def shipping_categories_available?
    skip_free_shipping = get_cart_items.any?{|line_item| (line_item.design.categories.pluck(:id) & EXCLUDE_FREE_SHIPPING_CATEGORIES).present?}
  end

  def get_warehouse_shipping_address(dos=nil)
    if dos.present?
      is_luxe_designer = dos.designer.designer_type == 'Tier 1 Designer'
      ids = is_luxe_designer ? LUXE_WAREHOUSE_ADDRESS_ID : DEFAULT_WAREHOUSE_ADDRESS_ID
    else
      ids = self.designer_orders.first.designer.warehouse_address_id
    end
    if ids.present? && (wa = WarehouseAddress.where(id: ids).first).present?
      return[wa.company_name, wa.phone, wa.address_line_1, wa.address_line_2, wa.city, wa.pincode, wa.state, wa.state_code]
    else
      return [COMPANY_NAME, SHIPPING_TELEPHONE, SHIPPING_ADDRESS, SHIPPING_ADDRESS_LINE_2, SHIPPING_CITY, SHIPPING_PINCODE, SHIPPING_STATE, SHIPPING_STATE_CODE]
    end
  end

  def total_discounts
    total_discount = 0
    total_discount = self.discount if self.discount.present?
    total_discount += self.additional_discount if self.additional_discount.present?
    total_discount += self.wallet_discount(self.currency_rate || 1)
    self.designer_orders.each do |d|
      unless d.state == "canceled"
        total_discount += d.get_scaled_discount
      end
    end
    total_discount
  end

  def shipping_time
    country.try(:downcase) == 'india' && actual_country_code == 'IN' ? DeliveryNpsInfo.get_city_based_pdd(self.city, Designer.where(id: designer_orders.collect(&:designer_id)).collect(&:pickup_location).map(&:downcase)) : Country.country_wise_delivery_time(country.try(:downcase))
  end

  def similar_designs
    design = self.line_items.any? ? self.line_items.sample(1)[0].design : Design.published.last
    RequestStore.cache_fetch("similar_designs_#{design.id}" , expires_in: 24.hour) do
      begin
        designs = Sunspot.more_like_this(design) do
          fields :category_name, :designer_name, :title, :description, :specification
          with(:state, 'in_stock')
          with(:average_rating, 4..5)
          with(:cluster_winner, 1)
        end
        designs.results
      rescue => e
        Design.published.retail.greater_than(1000).joins(:categories).in_category('jewellery').order('created_at DESC').limit(30)
      end
    end
  end

  def copy_billing_info_from_older_order(old_order)
    self.track = 0
    self.pay_type = old_order.pay_type
    self.billing_name = old_order.billing_name
    self.billing_phone = old_order.billing_phone
    self.billing_pincode = old_order.billing_pincode
    self.billing_street = old_order.billing_street
    self.billing_country = old_order.billing_country
    self.billing_state = old_order.billing_state
    self.billing_city = old_order.billing_city
    self.billing_email = old_order.billing_email
    self.email = old_order.email

    self.name = old_order.name
    self.phone = old_order.phone
    self.street = old_order.street
    self.city = old_order.city
    self.buyer_state = old_order.buyer_state
    self.country = old_order.country
    self.pincode = old_order.pincode
    self.currency_rate = old_order.currency_rate || 1
    self.currency_code = old_order.currency_code
    self.state_code = old_order.state_code if old_order.state_code.present?
    self.country_code = old_order.country_code if old_order.country_code.present?
    self.actual_country_code = old_order.actual_country_code if old_order.actual_country_code?
    self.currency_rate_market_value = old_order.currency_rate_market_value if old_order.currency_rate_market_value?
    self.paid_currency_rate = old_order.paid_currency_rate
    self.paid_currency_code = old_order.paid_currency_code
    self.geo = old_order.geo
    self.user_id = old_order.user_id
    self.order_notification['paypal_rate'] = old_order.order_notification['paypal_rate'] if old_order.order_notification['paypal_rate'].present?
    %w(app_source payment_gateway_details razorpay_id razorpay_status paypal_txn_id paypal_payer_id paypal_payment_type paypal_mc_gross paypal_mc_currency
     payu_mihpayid payu_payment_category_mode payu_status payu_error payu_bankcode payu_bank_ref payu_error_message payment_gateway
     amazon_order_id paytm_txn_id paytm_txn_status bank_deposit_txn_id cod_charge).each do |data|
      self.send("#{data}=",old_order[data])
    end
  end

  def total_line_items
    total = 0
    self.designer_orders.each {|des_order| total += des_order.line_items_count}
    total
  end

  def total_items_quantity
    total = 0
    self.designer_orders.each do |des_order|
      des_order.line_items.each {|item| total += item.quantity} if des_order.state != 'canceled'
    end
    total
  end

  def create_user_if_not_present(password: nil, send_newsletter: nil)
    return if self.user.present?
    user = User.find_by_email(billing_email.downcase)
    unless user
      user = User.new(email: billing_email, first_name: billing_name)
      account = user.build_account(
        email: user.email, terms_of_service: send_newsletter,
      )

      if password.present?
        account.guest_account = false
        account.password = account.password_confirmation = password
      else
        account.guest_account = true
        account.password = account.password_confirmation = Devise.friendly_token.first(6)
      end

      user.save!
    end
    self.user = user
    self.blacklist_user_order
    self.save!
    user
  end

  def save_user_details(send_newsletter: nil, ip_address: nil, source_url: nil, country_code: nil, password: nil)
    if self.user.present?
      # if user opted for seamless account, but account created by somewhere else
      # eg. while crediting cashback after order get sane
      if (account = user.account) && account.created_at > self.created_at && password.present?
        account.password = account.password_confirmation = password
        account.save
      end

      user.save_address_if_absent(self)
      user.account.update_column(:terms_of_service, send_newsletter)
    else
      create_user_if_not_present(send_newsletter: send_newsletter, password: password)
      self.user.save_address_if_absent(self)
    end

    Subscription.add_to_subscribers(self.email, ip_address, source_url, country_code)
  end

  def create_mirraw_subscription(subscription_plan_id)
    if subscription_plan_id.present? && self.user_subscriptions.active.empty?
      self.user.user_subscriptions.create(subscription_plan_id: subscription_plan_id)
    end
  end

  def order_summary(search_params= nil)
    if search_params.present?
      orders = Order.preload(designer_orders: :line_items).where(email: self.email).where('orders.created_at >=?', 3.months.ago).limit(30)
    else
      ds_state = []
      design_ids = []
      designer_order_ids = designer_orders.map(&:id)
      designer_orders.each do |designer_order|
        design_ids << designer_order.line_items.map(&:design_id)
        design_order_state = designer_order.state
        ds_state << design_order_state
      end
      return ds_state, designer_order_ids, design_ids
    end
  end

  def self.get_mobile_num(phone_num)
    phone = phone_num.gsub(' ', '').gsub('+','').gsub('-','')
    if phone.length >= 10 && phone.length <= 12
      if phone.length == 11 && phone[0] == '0'
        phone = phone[1..-1]
      elsif phone.length == 12 && phone[0..1] == '91'
        phone = phone[2..-1]
      end
      if phone.length == 10
        phone = "91" + phone
      end
    else
      return 0
    end
  end

  def get_mobile_num_international
    phone_parsed = Phonelib.parse(self.billing_phone, self.country_code)
    return phone_parsed.international(false).tr('+','')  if self.country_code.present? && phone_parsed.valid? && phone_parsed.possible? && (phone_parsed.types & [:mobile,:fixed_or_mobile]).present?
    false
  end

  def send_confirmation_sms_international(type)
    phone = self.get_mobile_num_international
    return false unless phone
    template = nil;
    case type
    when 'confirmed'
      total_with_symbol = ApplicationController.helpers.get_actual_price_in_currency_with_symbol(self.total,self.currency_rate, self.currency_code)
      template = "Thank You for shopping at MIRRAW Your Order #{self.number} of amount #{total_with_symbol} is confirmed and being processed. Download Our App https://bnc.lt/mirraw-app"
      use_alertbox = false
    when 'dispatched'
      if self.courier_company.present? && self.tracking_number.present?
        template = "Hi, Your Order #{self.number} has been shipped via #{self.courier_company} courier with Tracking No. #{self.tracking_number}. Download our App https://bnc.lt/mirraw-app"
        use_alertbox = false
      end
    when 'delivered'
      # Rails.application.routes.url_helpers.
      host_name  = Rails.application.config.action_mailer.default_url_options[:host]
      new_survey_link = new_survey_path(host: host_name)
      link = new_survey_link + "?order=#{Base64.urlsafe_encode64({order_number: self.number, email: self.email}.to_json)}&token=#{Survey.generate_token(self.email,self.number)}"
      shortened_link = get_order_shortned_link(link_to_be_shortned: link, host: host_name)
      template = "Hello, It seems like you are enjoying our product too much! We would love to hear from you. Please share your feedback #{shortened_link} -MIRRAW"
      use_alertbox = false
    when 'stitching_form'
      host_name = Rails.application.config.action_mailer.default_url_options[:host]
      order_show_link = order_path(host: host_name, number: number)
      shortened_link = get_order_shortned_link(link_to_be_shortned: order_show_link, host: host_name)
      template = "Greetings from Mirraw. Recently ordered products need your measurements for faster dispatch. Please fill details at #{shortened_link}"
      use_alertbox = false
    end
    return false unless template.present?
    if self.country_code.present? && self.country_code.downcase != 'in' && %w(production admin).include?(Rails.env)
      if use_alertbox && ALERT_SMS['countries'].include?(self.country_code.downcase)
        sms_api = ALERT_SMS['api_url']
        sms_api = sms_api.sub('{phone}', phone).sub('{template}', template)
        res = HTTParty.get(sms_api)
      else
        SmsNotification::NotificationService.notify_later(phone, template, 'INTERNATIONAL' ,'24x7')
      end
    end
  end

  def get_order_shortned_link(link_to_be_shortned: nil, host: nil)
    short_url(link_to_be_shortned, owner: self, url_options: {host: host})
  end

  def is_luxe_order?
    self.app_name=='luxe'
  end

  def send_confirmation_sms(issue = nil, phone_number = nil)
    phone = Order.get_mobile_num(phone_number.presence || self.billing_phone)
    order = self
    if phone && phone != 0 && phone.length == 12
      if issue == nil
        total_amount = order.actual_country_code != 'IN' ? international_cod_price(order.total) : order.total
        template = "Thank You for shopping at MIRRAW Your Order #{order.number} of amount #{total_amount} is confirmed and being processed. Download Our App https://bnc.lt/mirraw-app"
        use_alertbox = false
      else
        if self.cod? && issue == 'Out of Stock'
          template = "We regret to inform you that one of your product from  #{order.number}  is Out of Stock and has been cancelled because of the same. Please check your email for more details."
          use_alertbox = false
        elsif issue == 'delivered'
          host_name  = Rails.application.config.action_mailer.default_url_options[:host]
          new_survey_link = new_survey_path(host: host_name)
          link = new_survey_link + "?order=#{Base64.urlsafe_encode64({order_number: self.number, email: self.email}.to_json)}&token=#{Survey.generate_token(self.email,self.number)}"
          shortened_link = get_order_shortned_link(link_to_be_shortned: link, host: host_name)
          template = "Hello, It seems like you are enjoying our product too much! We would love to hear from you. Please share your feedback #{shortened_link} -MIRRAW"
          use_alertbox = false
        else
          template = "We regret to inform you that one of your product from Order #{order.number} has #{issue} and are in process of arranging for a replacement. Due to this your expected delivery date will be increased by 3 working days. Please check your email for more details."
        end
      end
      if use_alertbox
        sms_api = "http://alertbox.in/pushsms.php?username=9920668469&api_password=45c483mj8bec8287v&sender=MIRRAW&to={phone}&message={template}&priority=11"
        template = URI.encode(template)
        sms_api = sms_api.sub('{phone}', phone).sub('{template}', template)
        res = HTTParty.get(sms_api)
        self.add_notes_without_callback(res.parsed_response, 'nps sms response')
      else
        SmsNotification::NotificationService.notify_later(phone, template)
      end
    else
      [true, 'Invalid Phone Number.']
    end
  end

  def send_sms_cancel_order_code_dom
    phone_number = Order.get_mobile_num(self.phone.presence || self.billing_phone)
    if phone_number && phone_number != 0 && phone_number.length == 12
      template = "Hi #{self.name}, Your Mirraw order #{self.number}  has been cancelled as per your request"
      res = SmsNotification::NotificationService.notify(phone_number, template)
      [!res, '']
    else
      [true, 'Invalid Phone Number.']
    end
  end

  def send_cod_address_confirmation_sms
    phone = Order.get_mobile_num(self.billing_phone)
    order = self
    if phone && phone != 0 && phone.length == 12
      template = "Dear Customer, We are unable to verify the delivery address for your Mirraw Order %{order_number} and cannot ship the package yet, Please call on 02266484300 immediately to verify the address"
      template = template % {order_number: order.number, user_name: order.name}
      res = SmsNotification::NotificationService.notify(phone, template)
    end
  end

  def self.user_order_stats(user_id)
    return { total_count: 0, last_order_number: "Not Available" } if user_id.blank?
    valid_states = ['sane', 'dispatched', 'partial_dispatch', 'confirmed' , 'ready_for_dispatch']
    all_orders  = Order.unscoped.select('number,state').where(user_id: user_id).order(created_at: :desc)   
    return { total_count: 0, last_order_number: "No Previous Order placed" } if all_orders.blank?
    latest_order_state = all_orders[0].state
    valid_orders = all_orders.select { |order| valid_states.include?(order.state) }
    total_count = valid_orders.size
    total_count -= 1 unless ["cancel", "cancel_complete","followup"].include?(latest_order_state)
    total_count = [total_count, 0].max
    if ["cancel", "cancel_complete" , "followup"].include?(latest_order_state)
      last_order_number = valid_orders[0].present? && valid_orders[0].number.present? ? valid_orders[0].number : "No Previous Order placed"
    else
      last_order_number = valid_orders[1].present? && valid_orders[1].number.present? ? valid_orders[1].number : "No Previous Order placed"
    end
    { total_count: total_count, last_order_number: last_order_number }
  end
  
  def multi_line_address(line_length = 35, split_var = street)
    Order.multi_line_address(line_length, split_var)
  end

  def cod_available_on_order?
    if !self.international? && !self.billing_international?
      cod_available = true
      designers = Designer.joins(:designer_orders).where(designer_orders: {order_id: id}).where('designer_orders.state <> ?','canceled')
      designers.each do |designer|
        unless designer.can_cod?(pincode)
          cod_available = false
          break
        end
      end
      cod_available
    else
      false
    end
  end

  def cod_available_on_create_order
    self.cod_available = cod_available_on_order?
    self.save!
  end

  def convert_to_cod(account, total_cod_charges)
    if cod_available_on_order?
      self.pay_type = COD
      cart = Cart.find_by_id(self.cart_id)
      # total_cod_charges = self.domestic_cod_charge(self.pincode, cart)
      # self.cod_charge = total_cod_charges
      self.cod_charge = total_cod_charges
      #self.cod_charge = COD_CHARGES
      self.good_data
      first, last = account.email.split('@')
      self.add_notes_without_callback("#{first} converted to COD", 'payment')
    else
      false
    end
  end

  # def send_cod_verification
  def send_cod_verification(notify_host_url)
    if self.get_cod_token_increment_verification_count(notify_host_url)
      # self.sidekiq_delay_until(Time.now.advance(:minutes => 10)).send_zipdial_verify_sms
      SidekiqDelayGenericJob.perform_in(Time.now.advance(:minutes => 10) ,self.class.to_s, self.id, "send_zipdial_verify_sms")
      # self.sidekiq_delay(Time.now.advance(:minutes => 90)).clear_zipdial_details
      SidekiqDelayGenericJob.perform_in(Time.now.advance(:minutes => 90) ,self.class.to_s, self.id, "clear_zipdial_details")
      # self.sidekiq_delay(Time.now.advance(:hours => 5)).resend_verification_token_last_time_not_confirmed
      SidekiqDelayGenericJob.perform_in(Time.now.advance(:hours => 5) ,self.class.to_s, self.id, "resend_verification_token_last_time_not_confirmed")
    elsif self.send_cod_verification_email_increment_verification_count
      # self.sidekiq_delay( Time.now.advance(:days => 1)).cancel_unless_confirmed
      SidekiqDelayGenericJob.perform_in(Time.now.advance(:days => 1) ,self.class.to_s, self.id, "cancel_unless_confirmed")
    end
  end

  def send_cod_verification_email_increment_verification_count
    SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "request_cod_confirmation", {"#{self.class}": self.id})
    #OrderMailer.sidekiq_delay.request_cod_confirmation(self)
    self.cod_verify_msg_count += 1
    self.save!
  end

  # def get_cod_token
  def get_cod_token(notify_host_url)
    # return false
    z2v_token = Mirraw::Application.config.dial2verify_token
    phone = Order.get_mobile_num(self.phone)
    if phone && phone != 0 && phone.length == 12
      cod_api_params = DIAL2VERIFY_COD_API.sub('{phone}', phone).sub('{z2v_token}', z2v_token).sub('{notify_host_url}', notify_host_url)
      res = HTTParty.get(cod_api_params)
      res_hash = JSON.parse(res.body)
      if res_hash.present? && res_hash['APIStatus'] == 'Success'
        self.zipdial_transaction_token = res_hash['SessionId']
        self.zipdial_verify_image = res_hash['ImageUrl']
        self.zipdial_number = res_hash['VerificationNode']
        return true
      end
    else
      return false
    end
  end

  # def get_cod_token_increment_verification_count
  def get_cod_token_increment_verification_count(notify_host_url)
    if self.get_cod_token(notify_host_url)
    # if self.get_cod_token
      self.cod_verify_msg_count += 1
      self.save!
    else
      false
    end
  end

  def get_notes_by_department(department)
    return '' unless self.notes.present?
    notes_split = self.notes.split("...")
    emails = []
    notes_split.each do |note|
      name = note.split(' : ')[1]
      if name.present? && name != 'System'
        emails << name+'@mirraw.com'
      end
    end
    emails = emails.uniq
    case department
    when 'Operations'
      role = ['operations','dispatch','stitching']
    when 'Support'
      role = ['support']
    when 'Marketing'
      role = ['sales','marketing','senior_marketing']
    when 'Accounts'
      role = ['accounts','accounts_admin']
    else
      role = nil;
    end
    if role.present?
      emails_by_role = Account.joins(:role).where(email: emails, role: {name: role}).pluck(:email)
      email_by_departments = {}
      emails.each do |email|
        email_by_departments[email.split('@')[0]] = department if emails_by_role.include?(email)
      end
      get_new_note(email_by_departments)
    else
      get_new_note( ['System'])
    end
  end

  def calculate_advance_days(line_items, delivery_time, add_on_time)
    date_advance_hash ={warehouse_received: delivery_time/2 , qc_done: delivery_time/6, stitching_done: add_on_time, ready_to_dispatch: delivery_time/6}
    days_advanced = date_advance_hash[:warehouse_received]
    warehouse_receive_date = self.confirmed_at.advance(days: days_advanced)
    line_items_hash = order_status(line_items)
    increase_by_days = 0
    all_line_items = self.line_items
    if line_items_hash[:item_receive_count] == line_items.length
      add_days = (all_line_items.collect(&:received_on).compact.max.to_date - warehouse_receive_date.to_date).to_i
      increase_by_days = add_days if add_days > 0
    end

    days_advanced +=  date_advance_hash[:qc_done]

    if all_line_items.collect(&:qc_status).compact.include?(false)
      qc_failed_dates = self.designer_issues.select{|i| i.issue_type == 'QC Failed'}.collect(&:created_at).map(&:to_date)
      increase_by_days += get_qc_fail_days(qc_failed_dates) if qc_failed_dates.present?
    elsif line_items_hash[:qc_pass_count] == line_items.length
      increase_by_days += get_qc_done_days(days_advanced, increase_by_days, all_line_items)
    end

    if line_items_hash[:stitch_items] != 0 && line_items_hash[:stitch_items] == line_items_hash[:stitching_done_count]
      days_advanced += date_advance_hash[:stitching_done]
      stitching_done_date = self.confirmed_at.advance(days: days_advanced + increase_by_days)
      received_stitching_on = (all_line_items.collect(&:stitching_done_on).compact.max)
      add_days = (received_stitching_on.to_date - stitching_done_date.to_date).to_i if received_stitching_on.present?
      if add_days.present? && add_days > 0
        delivery_date_adv = self.confirmed_at.advance(days: delivery_time + add_on_time)
        if received_stitching_on > delivery_date_adv
          increase_by_days = (received_stitching_on.to_date - delivery_date_adv.to_date).to_i + 7
        end
      end
    end

    days_advanced += date_advance_hash[:ready_to_dispatch]
    if self.ready_for_dispatch_at.present?
      dispatch_approx_date = self.confirmed_at.advance(days: days_advanced + increase_by_days)
      add_days = (self.ready_for_dispatch_at.to_date - dispatch_approx_date.to_date).to_i
      increase_by_days += add_days if add_days.present? && add_days > 0
    end
    increase_by_days
  end

  def resend_verification_token_last_time_not_confirmed
    if self.pending?
      self.get_cod_token_increment_verification_count
      # self.sidekiq_delay.send_zipdial_verify_sms
      SidekiqDelayGenericJob.perform_async(self.class.to_s, self.id, "send_zipdial_verify_sms")
      # self.sidekiq_delay(Time.now.advance(:minutes => 90)).clear_zipdial_details
      SidekiqDelayGenericJob.perform_in(Time.now.advance(:minutes => 90) ,self.class.to_s, self.id, "clear_zipdial_details")
      # self.sidekiq_delay( Time.now.advance(:minutes => 90)).cancel_unless_confirmed
      SidekiqDelayGenericJob.perform_in(Time.now.advance(:minutes => 90) ,self.class.to_s, self.id, "cancel_unless_confirmed")
    end
  end

  def clear_zipdial_details
    if self.state == 'pending'
      self.zipdial_transaction_token = nil
      self.zipdial_verify_image = nil
      self.zipdial_number = nil
      self.save!
    end
  end

  def send_zipdial_verify_sms
    if self.pending? && Rails.env.production?
      phone = Order.get_mobile_num(self.phone)
      if phone && phone != 0 && phone.length == 12 && self.zipdial_number.present?
        template = "Hi, We have received your COD order of amount Rs. #{self.total}. Please give us a missed call on #{self.zipdial_number} within the next 1 hour to confirm your order. Thanks, Mirraw.com"
        SmsNotification::NotificationService.notify_later(phone, template)
      end
    end
  end

  def zipdial?
    self.zipdial_transaction_token.present? && self.zipdial_verify_image.present? ? true : false
  end

  # Similar order - 1 or more common designs in comparision to current order
  #
  # Checks for similar order that has been confirmed and was created BETWEEN week ago and time.now
  # is not in cancel or cancel_complete state
  #
  # Return true if similar_order_condition present else false

  def check_similar_confirmed_order_week?(time_period=7)
    w_confirmed = 'orders.confirmed_at IS NOT NULL'
    w_state_not = 'orders.state NOT IN (?)', ['cancel', 'cancel_complete']
    w_date      = 'orders.created_at BETWEEN ? and ?', time_period.days.ago, Time.now
    current_order_design_ids = self.line_items.order(:design_id).pluck(:design_id)
    other_orders_similar_design_ids = LineItem.
      joins(:designer_order => :order).
      where('orders.id <> ?', self.id).
      where('orders.user_id = ?', self.user_id).
      where(:design_id => current_order_design_ids).
      where(w_confirmed).
      where(w_state_not).
      where(w_date).
      pluck(:design_id)
    other_orders_similar_design_ids.blank? ? false : true
  end

  def predict_good_orders?
    # Don't let the method name fool you. Nobody can forsee the future except
    # Dr Strange. So until we get the Time Stone, we're just checking if there
    # are any `order_states` orders within the lifetime of
    # `self` (mainly 'cancel').

    # NOTE: The keyword "good" in the method name is a term used to call the
    # `order_states` group below.

    order_states = ['new', 'confirmed', 'pending', 'sane']
    products = Hash.new
    products[:cancel_order] = line_items.collect { |line_item| [line_item.design_id, line_item.quantity] }.to_h
    products[:good_orders] = LineItem.joins(designer_order: :order)
      .where(design_id: design_ids)
      .where(orders: {email: email, state: order_states, updated_at: created_at..updated_at})
      .select('orders.id as order_id, design_id, quantity')
      .group_by(&:order_id)
      .transform_values do |line_items|
        line_items.collect do |line_item|
          [line_item.design_id, line_item.quantity]
        end.to_h
      end

    products[:good_orders].any? { |order_id, line_items| line_items == products[:cancel_order] }
  end

  # Checks for preivous confirmed orders for user related to current order
  #
  # Return true if confirmed_orders > 0 else false

  def previous_user_confirmed_order?
    if self.user.present?
      cod_orders_confirmed_count = self.user.orders.where('confirmed_at IS NOT NULL').count
      cod_orders_confirmed_count > 0 ? true : false
    else
      false
    end
  end

  # Method relies on check_similar_confirmed_order_week? and previous_user_confirmed_order?
  #
  # if check_similar_confirmed_order_week? true
  # => add duplicate tag to current order
  # elsif previous_user_confirmed_order
  # => mark order as sane
  #
  # Return current_order

  def attempt_auto_confirm
    if check_similar_confirmed_order_week?
      self.tag_list.add('duplicate')
      self.add_notes_without_callback("Added tag duplicate", 'other')
      self.confirmed_order!
    # elsif self.previous_user_confirmed_order? || true
    elsif self.previous_user_confirmed_order?
      # Confirm all orders that are not duplicate
      # self.good_data
      self.confirmed_order!
    end
    if valid?
      self.save!
    else
      self.add_notes_without_callback(self.errors.full_messages.join(','),'cod_validity_issue')
    end
  end

  def update_paypal_rate
    currency_record = CurrencyConvert.find_by_country_code(self.country_code)
    rate = currency_record.rate
    if should_convert_to_usd?
      us_currency = CurrencyConvert.find_by_country_code('US')
      paypal_rate = (rate * us_currency.exchange_rate) / currency_record.market_rate.to_f
    else
      paypal_rate = currency_record.try(:paypal_rate)
      paypal_rate = if paypal_rate.blank?
                    convert = "#{currency_record.iso_code}_USD"
                    current_paypal_rate = JSON.parse(HTTParty.get("https://free.currconv.com/api/v7/convert?q=#{convert}&compact=ultra&apiKey=07ff7062a2ff71958f36").response.body)
                    (rate / current_paypal_rate[convert].to_f)
                  else
                    (rate / paypal_rate).to_f
                  end
    end
    self.order_notification['paypal_rate']   = paypal_rate.round(5)
    com,rate,currency_code = get_commercial_values
    self.order_notification['exchange_rate'] = ((cc = CurrencyConvert.where{(symbol == currency_code) | (iso_code == currency_code)}.first).exchange_rate.presence || cc.market_rate).round(5)
    self.skip_before_filter = true
    self.save!
  end

  def self.update_paypal_rate_mobile(id)
    order = Order.find_by_id(id)
    order.update_paypal_rate
  end
  #This method will check cod order shipping details and try to find out any anomalies to reduce RTO cases.
  # 2,500 per-day-limit of API request to Google Geocoder
  def cod_order_scan_for_issues
    unless ['sane','dispatched'].include?(state)
      cod_order_issues,location,location_type,shortened_address = '','','',street.dup
      issue_count = 0
      direct_sane = street.length >= 30
      designer_orders_count =  self.designer_orders.try(:length).to_i
      if designer_orders_count > COD_AUTO_MARK_SANE_MAX_AMNT
        cod_order_issues += "Order has more than #{COD_AUTO_MARK_SANE_MAX_AMNT} designer orders"
        issue_count += 1
      end
      unless direct_sane
        geocode_address = search_address_using_geocoder("#{street},#{city},#{pincode}")
        3.times do
          if geocode_address.present?
            location_type = geocode_address.first.precision
            direct_sane = true if  location_type == 'ROOFTOP'
            unless geocode_address.first.postal_code == pincode || direct_sane
              cod_order_issues += "Pincode in address may be wrong,"
              issue_count += 1
            end
            location  = geocode_address.first.coordinates.join(",")
            break
          else
            seperator = shortened_address.include?(',') ? ',' : ' '
            address =  shortened_address.split(seperator)
            if address.present?
              shortened_address = address[1..-1].join(',').squish
              geocode_address = search_address_using_geocoder("#{shortened_address},#{city},#{pincode}")
            else
              break
            end
          end
        end
        unless direct_sane
          if geocode_address.nil? || location_type != 'ROOFTOP'
            cod_order_issues += "Location of address not found,"
            issue_count += 1
          end
          if street.length < 12
            cod_order_issues += "Street address is too short,"
            issue_count += 1
          end
          if Order.get_mobile_num(phone).to_i == 0
            cod_order_issues += "Phone number doesn't seem valid,"
            issue_count += 1
          end
        end
      end
      has_duplicate = !check_similar_confirmed_order_week?(5)
      assessment = case issue_count
        when 0 then "GOOD"
        when 1..2 then "OK"
        when 3..4 then "BAD"
      end
      if (self.international? ? SANE_INT_COD_ORDERS : COD_AUTO_MARK_SANE) && self.can_good_data? && has_duplicate
        self.good_data ? add_tags_skip_callback('auto_mark_sane') : self.add_notes_without_callback(self.errors.full_messages.join(', '), 'State Transition Error')
      end

      unless has_duplicate && (direct_sane || assessment == "GOOD") && designer_orders_count <= COD_AUTO_MARK_SANE_MAX_AMNT
        if cod_order_issues.include?("Pincode in address may be wrong") || cod_order_issues.include?("Location of address not found") || cod_order_issues.include?("Street address is too short")
          add_tags_skip_callback('Invalid Address')
          send_cod_address_confirmation_sms
        end
        self.create_order_issue(coordinates: location,issues: cod_order_issues,assessment: assessment)
      end
    end
  end

  # Check whether all items are received or not. if received then updates items_received_status and items_received_on
  # input params - none
  #
  # Returns object
  def check_items
    w_lineitems_received = 'line_items.received = ? AND ((qc_status = ? OR qc_status IS NULL) OR (qc_status = ? AND issue_status = ?))' , 'Y',true, false, 'N'
    w_lineitems_canceled = 'status IN (?)', ['cancel','buyer_return']
    w_designer_order_state = 'designer_orders.state IN (?)', ['pending', 'pickedup', 'dispatched', 'completed']
    items_in_canceled_des_ord = 0
    designer_order = DesignerOrder.preload(:line_items).select('id,state').where('state IN (?) AND order_id = ?',['canceled','vendor_canceled','buyer_returned'],self.id).where(created_at: 4.months.ago..DateTime.now)
    line_items = LineItem.select('line_items.id,status,received,qc_status,designer_order_id').joins(:designer_order => :order).where(w_designer_order_state).where('designer_orders.order_id = ?', self.id).where(created_at: 6.months.ago..DateTime.now)
    line_items_received = line_items.dispatchable.where(w_lineitems_received).to_a.count
    line_items_canceled = line_items.where(w_lineitems_canceled).to_a.count
    designer_order.each {|des| items_in_canceled_des_ord += des.line_items_count}
    count_of_line_items_in_order = self.total_line_items - (line_items_canceled + items_in_canceled_des_ord)
    if line_items.to_a.count > 0 && line_items_received > 0 && line_items_received == count_of_line_items_in_order
      # self.sidekiq_delay_until(15.minutes.from_now, queue: 'high').check_order_valid_for_rfd(true) if self.sane? && !self.items_received_status
      SidekiqDelayGenericJob.perform_in(15.minutes.from_now ,self.class.to_s, self.id, "check_order_valid_for_rfd", true) if self.sane? && !self.items_received_status
      Order.where(:id => self.id).update_all(:items_received_status => true, :items_received_on => (self.items_received_status ? (self.items_received_on.presence || DateTime.now) : DateTime.now))
      unless self.items_received_on.present?
        OrderMailer.sidekiq_delay.all_line_items_notification(self.id,'air')
        # self.sidekiq_delay.send_line_items_notification('air')
        SidekiqDelayGenericJob.perform_async(self.class.to_s, self.id, "send_line_items_notification", 'air') 
      end
    else
      self.update_column(:items_received_status, false)
    end
  end

  handle_asynchronously :check_items, priority: -8, queue: 'high_priority'

  # input params - none
  #
  # send request to ccavenue to mail payment link to customer. updates order based on ccavenue respone

  def send_cbp_link
    items = [{'name' => self.number, 'unitCost' => self.total, 'quantity' => 1, 'description' => 'Order total amount to be paid'}]

    # CCAvenue requires this object as json as we are using json as request type
    request_items = {
      'customerName' => self.billing_name, 'customerEmailId' => self.billing_email, 'customerEmailSubject' => "Your order number #{self.number}",
      'validfor' => 7, 'currency' => 'INR', 'amount' => self.total, 'items' => items, 'invoiceType' => 'INVOICE',
      'merchantReferenceNo' => self.number}.to_xml(:root => 'GenerateInvoiceQuery', :skip_types => true).gsub('items', 'itemList')

    request = {'command' => 'generateInvoice'}

    if Rails.env.development?
      request['accessCode'] = CCAVENUE_DEV_API_ACCESS_CODE
    elsif Rails.env.production?
      request['accessCode'] = Mirraw::Application.config.ccavenue_mpcg_access_code
    end

    if Rails.env.development?
      request['encXML'] = Order.ccavenue_mpcg_encrypt(request_items, CCAVENUE_DEV_API_ENC_KEY)
    elsif Rails.env.production?
      request['encXML'] = Order.ccavenue_mpcg_encrypt(request_items, Mirraw::Application.config.ccavenue_mpcg_enc_key)
    end

    # CCAvenue API URL
    url = 'https://login.ccavenue.com/api/servlet/DoWebTrans'

    response = HTTParty.post(url, :body => request, :headers => {'Referer' => 'www.mirraw.com'})

    enc_response, status = response.split('&')
    status = status.split('status=')[1].to_i
    enc_response = enc_response.split('encResXML=')[1]

    # CCAvenue API has two status message
    # This one - the first one tell us whether api request was successful
    # Response structure status=0||1&enc_response=something
    if status == 1
      # if api request is unsucessful enc_response is plain text other wise its encrypted text
      # In unsuccessful request enc_response contains error message
      self.ccavenue_payment_link_status = 'failed'
      self.ccavenue_api_error = enc_response
    else
      # Special chars causes problem with decrypt
      if Rails.env.development?
        decrypt_content = Order.ccavenue_mpcg_decrypt(enc_response, CCAVENUE_DEV_API_ENC_KEY)
      elsif Rails.env.production?
        decrypt_content = Order.ccavenue_mpcg_decrypt(enc_response, Mirraw::Application.config.ccavenue_mpcg_enc_key)
      end
      decrypt_content = Hash.from_xml(decrypt_content)

      if decrypt_content['GenerateInvoiceResult'].present?
        response_details = decrypt_content['GenerateInvoiceResult']
        if response_details['errorObject']['error'] == 'false' && response_details['errorObject']['status'] == '0'
          self.ccavenue_payment_link_status = 'passed'
          self.ccavenue_invoice_id = response_details['invoiceId']
          self.ccavenue_payment_link = response_details['tinyUrl']
          self.ccavenue_payment_link_qr_code = response_details['qrCode']
          self.ccavenue_api_error = nil
        else
          self.ccavenue_payment_link_status = 'failed'
          self.ccavenue_api_error = response_details['errorObject']['error']
        end
      end
    end
    self.save
  end

  handle_asynchronously :send_cbp_link

  # DO NOT CHANGE THIS
  # CCAvenue decryption & encryption required method
  def self.ccavenue_mpcg_init_vector
    init_vector = (0..15).to_a.pack("C*")
  end

  # DO NOT CHANGE THIS
  # CCAvenue request encryption method
  # input params - text, key (encryption key)
  #
  # Returns encrypted text
  def self.ccavenue_mpcg_encrypt(plain_text, key)
    secret_key =  [Digest::MD5.hexdigest(key)].pack("H*")
    cipher = OpenSSL::Cipher::Cipher.new('aes-128-cbc')
    cipher.encrypt
    cipher.key = secret_key
    cipher.iv  = self.ccavenue_mpcg_init_vector
    encrypted_text = cipher.update(plain_text) + cipher.final
    (encrypted_text.unpack("H*")).first
  end

  # DO NOT CHANGE THIS
  # CCAvenue decryption method
  # input params - text (encrypted text), key (encryption key)
  #
  # Returns decrypted text
  def self.ccavenue_mpcg_decrypt(cipher_text,key)
    secret_key =  [Digest::MD5.hexdigest(key)].pack("H*")
    encrypted_text = [cipher_text].pack("H*")
    decipher = OpenSSL::Cipher::Cipher.new('aes-128-cbc')
    decipher.decrypt
    decipher.key = secret_key
    decipher.iv  = self.ccavenue_mpcg_init_vector
    decrypted_text = (decipher.update(encrypted_text) + decipher.final).gsub(/\0+$/, '')
    return decrypted_text
  end

  # removes cart references from line_item and cart
  #
  # Returns nil
  def destroy_cart_references
    self.line_items.update_all(:cart_id => nil)
    if self.cart_id.present?
      self.cart.update_attribute(:used, true)
      self.cart_id = nil
    end
  end

  def calculate_shipping_for_pending_and_sane
    if self[:notes].present? && self[:notes].include?('system_shipping_cost')
      shipping_cost = self[:notes].split(/system_shipping_cost/)[1][/\d+/].to_i
      self.update_column(:shipping, shipping_cost)
      self.shipping = shipping_cost
    end
  end

  def get_total_discount
    (discount.to_i + additional_discount.to_i + wallet_discount(currency_rate)).to_f
  end

  def get_order_invoice_details(commercial, shipper_id, selected_ids)
    order_shipments = self.shipments.where(designer_order_id: nil).preload(:line_items)
    shipping_amt = (self.bucket_line_items.sum(:quantity).to_i.nonzero? || (commercial && order_shipments.to_a.select{|s| s.line_items.present?}.count == 0)) ? self.shipping_charges : 0
    order_total_price = self.line_items.to_a.sum{|li| ((li.snapshot_price + li.line_item_addons.to_a.sum(&:snapshot_price)) * li.quantity)}.to_f + shipping_amt + self.total_tax
    item_ids = self.line_items.collect(&:id)
    self.line_items.where.not(replacement_item_id: nil).preload(replaced_product: :line_item_addons).each do |li|
      order_total_price -= ((li.snapshot_price + li.line_item_addons.to_a.sum(&:snapshot_price)) * li.quantity).to_f if selected_ids.exclude?(li.replacement_item_id)
      order_total_price += ((li.replaced_product.snapshot_price + li.replaced_product.line_item_addons.to_a.sum(&:snapshot_price)) * li.replaced_product.quantity).to_f if item_ids.exclude?(li.replacement_item_id)
    end
    if (count = order_shipments.to_a.count{|s| s.shipper_id == shipper_id}) > 0
      reference = "#{self.number}_#{count}"
    else
      reference = self.number
    end
    return [reference, shipping_amt, order_total_price, self.total_tax]
  end

  def shipping_charges
    (self.get_gift_wrap_price(1) + self.shipping.to_f).round(2)
  end

  def free_shipping_on_country?(country)
    promotion_pipelines = PromotionPipeLine.where{(start_date.lt Time.zone.now) & (end_date.gt Time.zone.now)}
    shipping_country_code = Country.find_by_namei_cached(self.country).try(:[], :iso3166_alpha2)
    if self.actual_country_code == 'IN'
      false
    elsif (country_code = Promotions.free_shipping_on_country(shipping_country_code)).present?
      country_code.split(',').include?(shipping_country_code)
    elsif promotion_pipelines.where('name like ?', '%free_shipping%').pluck(:country_code).include?(shipping_country_code)
      true
    else
      false
    end
  end

  def payu_billing_page_url_non_seamless(response_url, payu_money = false,additional_payment_id = nil)
    payu = Mirraw::Application.config.payu
    params = self.payu_billing_page_non_seamless_params(payu[:merchant_key], payu[:salt], response_url, payu_money,additional_payment_id)
    response = HTTParty.post(payu[:billing_page_non_seamless_request_url], :body => params)
    if response.code == 200
      if payu_money
        url = Rails.env.production? ? payu[:payu_money_production_url] : payu[:payu_money_test_url]
        {url: url, params: params}
      else
        "#{payu[:billing_page_non_seamless_transaction_url]}?mihpayid=#{response.body.strip}"
      end
    end
  end

  def payu_billing_page_non_seamless_params(key, salt, response_url, payu_money,additional_payment_id = nil)
    non_word_excluding_space_regex = Regexp.new(/[^\w\s]/)
    productinfo = 'Ethnic Wear'
    udf1, udf2, udf3, udf4, udf5 = '','','','',''
    if additional_payment_id
      amount = AdditionalPayment.find_by_id(additional_payment_id).total
      txnid = "#{number}_addon_#{additional_payment_id}"
    else
      txnid = self.number
      amount = self.total
    end
    #temporary fix
    if (billing_international? || actual_country_code != 'IN') && currency_rate.present? && currency_rate_market_value.present?
      amount = ((amount / currency_rate) * currency_rate_market_value)
    end
    firstname = self.billing_name.gsub(non_word_excluding_space_regex,'')[0..59]
    email = self.billing_email
    phone = self.phone
    # surl - success url, furl - failure url, curl - cancel url
    curl = surl = furl = response_url
    shipping_address = multi_line_address(100,self.street.gsub(non_word_excluding_space_regex,''))
    billing_address = multi_line_address(100,self.billing_street.gsub(non_word_excluding_space_regex,''))
    hashsum = self.payu_billing_page_checksum(key, txnid, amount, productinfo, firstname, email, udf1, udf2, udf3, udf4, udf5, salt)
    params = {:key => key, :txnid => txnid, :amount => amount, :surl => surl, :furl => furl, :curl => curl, :hash => hashsum,
      :address1 => billing_address[0], :address2 => billing_address[1], :shipping_address1 => shipping_address[0],
      :shipping_address2 => shipping_address[1], :productinfo => productinfo, :firstname => firstname
    }
    params[:email] = email
    params[:phone] = phone.gsub(/\D/,'')[0..49]
    params[:shipping_firstname] = self.name.gsub(non_word_excluding_space_regex,'')[0..59]
    params[:shipping_city] = self.city.gsub(non_word_excluding_space_regex,'')[0..49]
    params[:shipping_state] = self.state.gsub(non_word_excluding_space_regex,'')[0..49]
    params[:shipping_country] = self.country.gsub(non_word_excluding_space_regex,'')[0..49]
    params[:shipping_zipcode] = self.pincode.gsub(non_word_excluding_space_regex,'')[0..19]
    params[:shipping_phone] = self.phone.gsub(/\D/,'')[0..49]
    params[:city] = self.billing_city.gsub(non_word_excluding_space_regex,'')[0..49]
    params[:state] = self.billing_state.gsub(non_word_excluding_space_regex,'')[0..49]
    params[:country] = self.billing_country.gsub(non_word_excluding_space_regex,'')[0..49]
    params[:zipcode] = self.billing_pincode.gsub(non_word_excluding_space_regex,'')[0..49]
    if payu_money
      params[:pg] = 'Wallet'
      params[:bankcode]  = 'payuw'
    end
    params
  end

  def payu_billing_page_checksum(key, txnid, amount, productinfo, firstname, email, udf1, udf2, udf3, udf4, udf5, salt)
    Digest::SHA512.hexdigest "#{key}|#{txnid}|#{amount}|#{productinfo}|#{firstname}|#{email}|#{udf1}|#{udf2}|#{udf3}|#{udf4}|#{udf5}||||||#{salt}"
  end

  def self.payu_billing_page_response_checksum(key, txnid, amount, productinfo, firstname, email, udf1, udf2, udf3, udf4, udf5, salt, status)
    Digest::SHA512.hexdigest "#{salt}|#{status}||||||#{udf5}|#{udf4}|#{udf3}|#{udf2}|#{udf1}|#{email}|#{firstname}|#{productinfo}|#{amount}|#{txnid}|#{key}"
  end

  def has_addon_type_value_group?(addon_type_value_group_name, avoid_fnp=nil, note_check=false)
    w_fnp_condition, w_note_check = '', ''
    w_fnp_condition = 'addon_type_values.name NOT IN (?)',["Fall and Pico","Petticoat Stitching"] if avoid_fnp.present?
    w_note_check = ['line_item_addons.notes is null or trim(line_item_addons.notes) = ?', ''] if note_check
    self.line_items.joins(:line_item_addons => [:addon_type_value =>:addon_type_value_group]).where(addon_type_value_groups: {name: addon_type_value_group_name}).where(w_fnp_condition).where(w_note_check).exists?
  end

  # input params - id - integer
  #
  # conditioanlly creates delayed jobs for sending notifications
  #
  # Return Integer
  def self.processing_notifications(order_ids)
    count = 0
    unless order_ids.nil?
      Order.where(id: order_ids).find_each do |order|
        if order.send_order_processing_notification?
          OrderMailer.sidekiq_delay.order_processing_notification(order.id)
          count += 1
        end
      end
    end
    count
  end

  # input params - none
  #
  # Get expected days for delivery of order
  #
  # Returns Integer
  def delivery_days
    addon_prod_values = self.addon_delivery_time
    addon_prod_days = addon_prod_values.max_by(&:first)
    delivery_days = self.shipping_time
    if express_delivery.present?
      delivery_days = (delivery_days + (express_delivery == 0 ? addon_prod_values.max_by(&:last).last : (addon_prod_days.try(:first).to_f))).round
    else
      delivery_days = (delivery_days + addon_prod_days.try(:first).to_f).round
      delivery_days += 1 if (self.confirmed_at || Time.current).advance(days: delivery_days).sunday? if actual_country_code == 'IN' && country.downcase == 'india'
      delivery_days
    end
  end

  # input params - none
  #
  # Checks whether to send order delayed mail or not
  #
  # Returns Boolean
  def send_order_processing_notification?
    return false unless self.email_logs.where(:email_type => 'order_processing_notification').count < 2
    return false unless (self.international? && self.dispatched?) || self.domestic_dispatched?
    return false unless (Date.today - self.confirmed_at.to_date).to_i == self.days_for_next_mail('order_processing_notification')
    return true
  end

  # input params - none
  #
  # Check whether designer order has been processed
  #
  # Returns Boolean
  def domestic_dispatched?
    self.designer_orders.where(:state => ['pending', 'new', 'critical']).count == 0 ? false : true
  end

  # input params - email type
  #
  # Provides day for next mail to sent
  #
  # Returns Integer
  def days_for_next_mail(email_type)
    days = 0
    case email_type
    when 'order_processing_notification'
      first, second = self.when_mail_customer_order_processing_days
      emails_sent = self.email_logs.where(:email_type => email_type).count
      days = emails_sent == 0 ? first : second
    end
    days
  end

  # input params - none
  #
  # Check whether order has stitching addons
  #
  # Returns Boolean
  def stitching_addon? (avoid_fnp = nil, note_check= false)
    stitching_constant = SystemConstant.get('STITCHING_ADDON_GROUPS')
    if stitching_constant.present?
      stitching_addon_group_names = stitching_constant.split(',')
      return self.has_addon_type_value_group?(stitching_addon_group_names, avoid_fnp, note_check)
    end
    false
  end

  def stitching_form_required?
    line_items.any?(&:send_stitching_form?)
  end

  def salwar_measurements(item, design_type, approve_ids)
    addon_notes = item.line_item_addons.first.notes.split(',')
    std_length = (s_n1 = addon_notes.each_index.find{|i| addon_notes[i].downcase.include?('kameez length') || addon_notes[i].downcase.include?('kurti length')}).present? ? addon_notes[s_n1].scan(/\d+/).first : nil
    std_bottom_length = (s_n2 = addon_notes.each_index.find{|i| addon_notes[i].downcase.include?('bottom length')}).present? ? addon_notes[s_n2].scan(/\d+/).first : nil
    std_size = (s_n3 = addon_notes.each_index.find{|i| addon_notes[i].downcase.include?('standard stitching size')}).present? ? addon_notes[s_n3].scan(/\d+/).first : nil
    user_height = (s_n4 = addon_notes.each_index.find{|i| addon_notes[i].downcase.include?('your height')}).present? ? addon_notes[s_n4].scan(/\d+/).join('.') : nil
    std_size_measure = SIZE_CHART_TABLE["SalwarKameez-size-#{std_size}"]
    is_anarkali = (ANARKALI_CATEGORY_FOR_STANDARD_ADDON & item.design.try(:categories).to_a.map(&:id)).present?
    mes_approve_hash = ((std_size.to_i > 40 || (is_anarkali && std_size.to_i > 38)) ? {} : (approve_ids << item.id; {state: 'approved', approved_at: Time.current, measurement_flow: "Approved #{Time.current.strftime('(%d-%m-%Y)')}", user_review: {'system_approved' => 'true'}, bottom_length: 'As Per Image'}))
    std_salwar_measure = []
    designable_type = ['Kurti', 'Islamic'].include?(design_type) ? design_type.try(:downcase) : 'kameez_with_bottom_as_per_image'
    item.quantity.times do
      measurement_obj = StitchingMeasurement.new(design_id:item.design_id,line_item_id: item.id,order_id: self.id,
        chest_size:"#{std_size_measure.try(:[],3)} Ready",waist_size:"#{std_size_measure.try(:[],5)} Ready", hip_size:"#{std_size_measure.try(:[],6)} Ready",shoulder_size:"#{std_size_measure.try(:[],7)} Ready",size_around_arm_hole:"#{std_size_measure.try(:[],8)} Ready",sleeves_around:"#{std_size_measure.try(:[],9)} Ready",
        length:(std_length.presence || 'As Per Image'), bottom_length:(std_bottom_length.presence || '40 As per Image'),code:std_size_measure.try(:[],10),sleeves_length:'As per fabric or given',front_neck:'As per fabric or given',back_neck:'As per fabric or given',
        product_designable_type:designable_type, measurement_info_done_by:'System',stylist_id:self.stylist_id, height: user_height, measurement_group: 'user')
      measurement_obj.assign_attributes(mes_approve_hash)
      std_salwar_measure << measurement_obj
    end
    return std_salwar_measure
  end

  def saree_measurements(item, approve_ids)
    addon_notes = item.line_item_addons.pluck(:notes).join(',').split(',')
    std_blouse_length = (s_n1 = addon_notes.each_index.find{|i| addon_notes[i].downcase.include?('blouse length')}).present? ? addon_notes[s_n1].scan(/\d+/).first : nil
    std_sleeve_length = (s_n2 = addon_notes.each_index.find{|i| addon_notes[i].downcase.include?('blouse sleeve')}).present? ? addon_notes[s_n2].sub(/.*?:/, '') : nil
    std_size = (s_n3 = addon_notes.each_index.find{|i| addon_notes[i].downcase.include?('standard stitching size')}).present? ? addon_notes[s_n3].scan(/\d+/).first : nil
    user_height = (s_n4 = addon_notes.each_index.find{|i| addon_notes[i].downcase.include?('your height')}).present? ? addon_notes[s_n4].scan(/\d+/).join('.') : nil
    std_size_measure = SIZE_CHART_TABLE["Saree-size-#{std_size}"]
    mes_approve_hash = (std_size.to_i > 40 ? {} : approve_ids << item.id;{state: 'approved', approved_at: Time.current, measurement_flow: "Approved #{Time.current.strftime('(%d-%m-%Y)')}", user_review: {'system_approved' => 'true'}, front_neck: 'As Per Image', back_neck: 'As Per Image'})
    std_blouse_measure = []
    item.quantity.times do
      measurement_obj = StitchingMeasurement.new(design_id:item.design_id,line_item_id: item.id,order_id: self.id,chest_size:std_size_measure.try(:[],0), length:(std_blouse_length.presence || std_size_measure.try(:[],3)), code:std_size_measure.try(:[],10), sleeves_length:'As per fabric or given', front_neck: std_size_measure.try(:[],4), back_neck:std_size_measure.try(:[],5), under_bust: std_size_measure.try(:[],7), size_around_arm_hole: std_size_measure.try(:[],8), shoulder_size: std_size_measure.try(:[],9), product_designable_type:'blouse', measurement_info_done_by:'System',stylist_id:self.stylist_id, height: user_height, measurement_group: 'user')
      measurement_obj.assign_attributes(mes_approve_hash)
      std_blouse_measure << measurement_obj
    end
    return std_blouse_measure
  end

  def lehenga_measurements(item, approve_ids)
    addon_notes = item.line_item_addons.first.notes.split(',')
    std_lehenga_length = (s_n2 = addon_notes.each_index.find{|i| addon_notes[i].downcase.include?('lehenga length')}).present? ? addon_notes[s_n2].scan(/\d+/).first : nil
    std_size = (s_n3 = addon_notes.each_index.find{|i| addon_notes[i].downcase.include?('standard stitching size')}).present? ? addon_notes[s_n3].scan(/\d+/).first : nil
    user_height = (s_n4 = addon_notes.each_index.find{|i| addon_notes[i].downcase.include?('your height')}).present? ? addon_notes[s_n4].scan(/\d+/).join('.') : nil
    mes_approve_hash = (std_size.to_i > 40 ? {} : (approve_ids << item.id; {state: 'approved', approved_at: Time.current, measurement_flow: "Approved #{Time.current.strftime('(%d-%m-%Y)')}", user_review: {'system_approved' => 'true'}}))
    std_size_measure = SIZE_CHART_TABLE["Lehenga-size-#{std_size}"]
    std_saree_size_measure = SIZE_CHART_TABLE["Saree-size-#{std_size}"]
    std_lehenga_measure = []
    item.quantity.times do
      measurement_obj = StitchingMeasurement.new(design_id:item.design_id,line_item_id: item.id,order_id: self.id,front_neck: 'As Per Image', back_neck: 'As Per Image', length: std_saree_size_measure.try(:[],3),
        chest_size:std_size_measure.try(:[],1), waist_size:std_size_measure.try(:[],3), hip_size:std_size_measure.try(:[],4),bottom_length: (std_lehenga_length.presence || 'Max Possible As per fabric'), sleeves_length:'As per fabric or given',under_bust: std_saree_size_measure.try(:[],7), size_around_arm_hole: std_saree_size_measure.try(:[],8), shoulder_size: std_saree_size_measure.try(:[],9), code:std_size_measure.try(:[],5),product_designable_type:'lehenga_choli',measurement_info_done_by:'System',stylist_id:self.stylist_id, height: user_height, measurement_group: 'user')
      measurement_obj.assign_attributes(mes_approve_hash)
      std_lehenga_measure << measurement_obj
    end
    return std_lehenga_measure
  end

  def standard_addon?
    count, standard_addons, paid_stitching = 0, 0, true
    std_measurements, std_line_item_ids, auto_approve_item_ids = [], [], []
    all_line_items = self.line_items.to_a
    stitch_items_count = all_line_items.count{|item| (item.status.blank? || item.status == 'buyer_return') && item.stitching_required == 'Y'}
    all_line_items.each do |item|
      if Design::STITCHING_ALLOWED_DESIGNABLE_TYPES.include?(item.design.designable_type) && item.has_addon_type_value_group?('standard_stitching') && item.line_item_addons.pluck(:notes).join().downcase.include?('standard stitching size')
        if item.stitching_measurements.blank?
          if self.stylist_id.blank? && self.sane?
            stylist_group = (self.has_addon_type_value_group?(['custom_stitching']) ? 'custom' : 'standard')
            assign_it_to_stylist(stitch_items_count, item.design.designable_type.downcase, stylist_group)
          end
          case (des_type = item.design.designable_type)
          when 'SalwarKameez', 'Kurti', 'Islamic'
            ActiveRecord::Associations::Preloader.new.preload(all_line_items, [design: :categories]) if item.association_cache.keys.exclude?(:design)
            std_measurements.push(*salwar_measurements(item, des_type, auto_approve_item_ids))
          when 'Lehenga'
            std_measurements.push(*lehenga_measurements(item, auto_approve_item_ids))
          when 'Saree'
            std_measurements.push(*saree_measurements(item, auto_approve_item_ids))
          end
        end
        standard_addons += 1
        std_line_item_ids << item.id
      elsif item.paid_addons?
        paid_stitching = false
      end
      count += 1
    end
    auto_approve_item_ids.uniq!
    LineItem.bulk_store_item_details(item_ids: auto_approve_item_ids, detail_name: 'std_auto_approve', detail_value: 'true') if auto_approve_item_ids.present?
    StitchingMeasurement.import std_measurements if std_measurements.present?
    std_line_item_ids.each do |item|
      StitchingMeasurement.sidekiq_delay.generate_pdf(item)
    end
    StitchingMeasurement.create_standard_stylist_measurements(std_line_item_ids)
    auto_approve_item_ids.uniq!
    LineItem.auto_do_fabric_operation_in_bulk(auto_approve_item_ids, nil, 'System') if auto_approve_item_ids.present?
    send_stitching_info_mail_to_user if std_measurements.present?
    standard_addons != 0 && stitch_items_count == standard_addons && paid_stitching
  end

  def send_stitching_info_mail_to_user
    stitching_count, measurements_count = get_stitching_counts
    if stitching_count > 0 && stitching_count == measurements_count
      if self.notes.exclude?('Stitching Info Mail sent')
        self.add_notes_without_callback("Stitching Info Mail sent",'stitching')
        OrderMailer.sidekiq_delay.send_stitching_info_label(self.id)
      end
    end
    if other_details['stitching_order'] == 'true' && (stitching_count == 0 || stitching_count == measurements_count)
      self.remove_tags_skip_callback('addon')
      self.add_tags_skip_callback('stitching')
      self.add_notes_without_callback('Order Tag moved from Addon to Stitching','stitching') if self.notes.exclude?('Order Tag moved from Addon to Stitching')
    end
  end

  def self.send_stitching_info_mail_to_user_from_mobile(order_id)
    order = Order.find_by_id(order_id)
    order.send_stitching_info_mail_to_user if order.present?
  end

  def addon_delivery_time
    size_chart_id = SizeChart::UNSTITCHED_SIZE_CHART.try(:id) || 0
    if country == 'India'
      DesignerOrder.unscoped.joins(line_items: [design: :designer]).
      joins(<<-JOIN).
        LEFT OUTER JOIN line_item_addons lia ON lia.line_item_id = line_items.id
        LEFT OUTER JOIN addon_type_values atv ON atv.id = lia.addon_type_value_id
        LEFT OUTER JOIN addon_type_value_groups atvg ON atv.addon_type_value_group_id = atvg.id
      JOIN
      where(order_id: id).group('line_items.id').
      pluck(<<-SELECT)
        MAX((CASE
           WHEN lia.size_chart_id <> #{size_chart_id}
                AND atvg.name = 'standard_stitching'
                AND line_items.available_in_warehouse = TRUE THEN 0
           ELSE COALESCE(atv.prod_time,0)
           END) + CASE
           WHEN COALESCE(designs.eta,0) > 0 THEN designs.eta
           WHEN COALESCE(designers.eta,0) > 0 THEN designers.eta
           ELSE #{Designer::SLA_CONFIG[:mirraw_designer_default_eta]}
           END + CASE
           WHEN designer_orders.created_at >= designers.vacation_start_date and designer_orders.created_at <= designers.vacation_end_date THEN COALESCE(EXTRACT(EPOCH FROM(designers.vacation_end_date - designers.vacation_start_date)/(3600*24)),0)
           ELSE 0
           END),
        MAX((CASE
           WHEN lia.size_chart_id <> #{size_chart_id}
                AND atvg.name = 'standard_stitching'
                AND line_items.available_in_warehouse = TRUE THEN 0
           ELSE COALESCE(atv.prod_time,0)
           END))
      SELECT
    else
      DesignerOrder.unscoped.joins(line_items: [design: :designer]).
      joins(<<-JOIN).
        LEFT OUTER JOIN line_item_addons lia ON lia.line_item_id = line_items.id
        LEFT OUTER JOIN addon_type_values atv ON atv.id = lia.addon_type_value_id
        LEFT OUTER JOIN addon_type_value_groups atvg ON atv.addon_type_value_group_id = atvg.id
        LEFT OUTER JOIN categories_designs cat_d on cat_d.design_id = designs.id
        LEFT OUTER JOIN categories cat on cat_d.category_id = cat.id
      JOIN
      where(order_id: id).group('line_items.id').
      pluck(<<-SELECT)
        MAX((CASE
           WHEN lia.size_chart_id <> #{size_chart_id}
                AND atvg.name = 'standard_stitching'
                AND line_items.available_in_warehouse = TRUE THEN 0
           ELSE COALESCE(atv.prod_time,0)
           END) + CASE
           WHEN COALESCE(designs.eta,0) > 0 THEN designs.eta
           WHEN COALESCE(designers.eta,0) > 0 THEN designers.eta
           ELSE #{Designer::SLA_CONFIG[:mirraw_designer_default_eta]}
           END + CASE
           WHEN COALESCE(designers.ship_time,0) <> 0 THEN designers.ship_time
           ELSE #{Designer::SLA_CONFIG[:vendor_default_dispatch_days]}
           END + CASE
           WHEN designer_orders.created_at >= designers.vacation_start_date and designer_orders.created_at <= designers.vacation_end_date THEN COALESCE(EXTRACT(EPOCH FROM(designers.vacation_end_date - designers.vacation_start_date)/(3600*24)),0)
           ELSE 0
           END) + MAX(COALESCE(cat.eta,0)),
        MAX((CASE
           WHEN lia.size_chart_id <> #{size_chart_id}
                AND atvg.name = 'standard_stitching'
                AND line_items.available_in_warehouse = TRUE THEN 0
           ELSE COALESCE(atv.prod_time,0)
           END))
      SELECT
    end
  end
  # input params - none
  #
  # Provides days on which customer should receive order processing mails
  #
  # Returns Array
  def when_mail_customer_order_processing_days
    first = self.international? ? 5 : 3
    second = self.international? ? 10 : 5
    return first, second
  end

  ## Check COD is available or not according to cart_id and pincode
  def cod_available?
    Courier.check_cod(cart_id, (ENABLE_COD_COUNTRIES.include?(country.try(:downcase)) && international? ? SHIPPING_PINCODE : pincode))
  end

  def can_return?
    line_items = LineItem.joins(:designer_order).sane_items.where(:designer_orders => {:order_id => self.id, state: ['dispatched','completed','buyer_returned']})
    line_item_not_stiching = []
    line_items.each do |item|
      unless item.has_addon_type_value_group?('custom_stitching') || item.has_addon_type_value_group?('standard_stitching')
        line_item_not_stiching << item
      end
    end
    return_designer_orders_count = ReturnDesignerOrder.where(:return_id => self.return_ids, :state => 'new').count
    (line_item_not_stiching.count > 0 || return_designer_orders_count > 0) ? true : false
  end

  # input params - none
  #
  # Send mailers according to condtions
  #
  # Returns Nil / Delayed::Job Object
  def check_mailers_send
    self.send_stitching_measurement_mail if self.can_mail_stitching?
  end

  handle_asynchronously :check_mailers_send

  # input params - none
  #
  # Check whether mail can be sent for stitching
  #
  # Returns Boolean
  def can_mail_stitching?
    if SystemConstant.get('STITCHING_FOLLOWUP_MAIL') == '1' && !self.standard_addon? && self.stitching_addon?('true', true) && self.email_logs.where(:email_type => 'stitching_followup').count == 0
      return true
    end
    false
  end

  # input params - none
  #
  # Send mail for stitching measurements
  #
  # Returns Delayed::Job Object
  def send_stitching_measurement_mail(current_account = nil)
    OrderMailer.sidekiq_delay.stitching_measurement(self.id)
    send_confirmation_sms_international('stitching_form')
    AppEvent::OrderEvent.new(self.id, "Stitching Form Sent").trigger_clevertap_event_deliver_later
    self.add_notes_without_callback('Stitching Forms Sent', 'stitching', current_account)
  end

  # input params - none
  #
  # Adds notes to order
  def add_notes(note, to_save, current_account = nil)
    first = current_account.nil? ? 'System' : current_account.email.split('@')[0]
    note_update(first,note)
    self.save if to_save
  end

  def add_notes_without_callback(note, note_type, current_account = nil)
    type = ['admin', 'accounts_admin', 'super_admin', nil, ''].include?(note_type) ? 'other' : note_type
    event_attrs = {note_type: type, notes: note, done_by: (current_account.try(:name) || 'System'), event_timestamp: Time.current, account_id: current_account.try(:id)}
    self.new_record? ? events.build(event_attrs) : events.create(event_attrs)
  end

  def note_update(user,note)
    note_content = "#{Date.today.strftime("%m/%d")} : #{user} : #{note}"
    self.notes = if self[:notes].blank?
      ''
    else
      self[:notes] + ' ... '
    end
    self.notes = self[:notes] + note_content
  end

  def shipment_reference
    "#{self.number} #{Date.today}"
  end

  def stripe?
    payment_gateway.to_s.downcase == 'stripe'
  end

  def get_ad_code
    stripe? ? STRIPE_AD_CODE : MIRRAW_AD_CODE
  end
 
  def expected_delivery_date
    return delivery_nps_info.promised_delivery_date.to_date if confirmed_at.present? && delivery_nps_info.present?
  end

  def self.multi_line_address(line_length, split_var)
    multi_line_address_block = []
    i = 0
    split_var.split(/[ ,]/).each do |s|
      if multi_line_address_block[i].blank?
        multi_line_address_block[i] = s
      elsif (multi_line_address_block[i].length + s.length < line_length)
        multi_line_address_block[i] += ' ' + s
      else
        i += 1
        multi_line_address_block[i] = s
      end
    end
    multi_line_address_block
  end

  def self.mail_order_details_to_buyer(id)
    order = Order.find(id)
    SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "mail_order_details_to_buyer", {"#{order.class}": order.id})
    #OrderMailer.sidekiq_delay.mail_order_details_to_buyer(order)
  end

  def self.convey_status_update_to_designer(designer_order_id)
    designer_order = DesignerOrder.where(id: designer_order_id).first
    OrderMailer.convey_status_update_to_designer(designer_order.order, designer_order).deliver
  end


  def self.cancel_order_mobile(id)
    order = Order.find_by_id(id)
    order.cancel_order
  end

  def self.cancel_unless_confirmed_mobile(id)
    order = Order.find(id)
    order.cancel_unless_confirmed
  end

  def self.paypal_pending_mobile(id)
    order = Order.find(id)
    order.paypal_pending! unless order.pending?
  end

  def self.cancel_orders(ids)
    order = Order.where(id: ids)
    order.map(&:cancel!) if order
  end

  def cancel_order_increase_quantity
    self.increment_item_quantity = true
    self.cancel!
  end

 # update payment status of order according to payment_status
  def self.process_payu_order!(id, amount)
    order = Order.find(id)
    case order.payu_status
    when 'success'
      order.set_successful_payment_status!(amount)
    when 'failure', 'aborted', 'invalid'
      order.cancel_order_increase_quantity if order.can_cancel?
    end
  end

  def self.process_juspay_order!(jp_order_status)
    order = Order.find_by_number(jp_order_status['order_id'])
    if order.present? && jp_order_status['status'].present?
      order.juspay_order_status = jp_order_status['status']
      order.juspay_txn_id = jp_order_status['txn_id']
      order.add_to_payment_gateway_details(jp_order_status.to_json)
      case jp_order_status['status']
      when 'CHARGED'
        order.set_successful_payment_status!(jp_order_status['amount'])
      when 'AUTHENTICATION_FAILED', 'AUTHORIZATION_FAILED', 'JUSPAY_DECLINED'
        order.cancel_order_increase_quantity if order.can_cancel?
      end
    end
  end

  def self.process_stripe_order!(id, amount, status)
    order = Order.find(id)
    if status == 'succeeded'
      order.set_successful_payment_status!(amount)
    else
      order.cancel_order_increase_quantity if order.can_cancel?
    end
  end

  def self.process_paytm_order!(id, amount)
    if (order = Order.find_by_id(id)).try(:paytm?)
      if order.paytm_txn_status == 'TXN_SUCCESS'
        if (status_response = order.check_paytm_status) == true
          order.set_successful_payment_status!(amount)
        else
          order.add_notes_without_callback("Failed in paytm status check #{(status_response)}", 'payment')
          order.require_confirmation!
        end
      elsif order.can_cancel?
        order.cancel_order_increase_quantity
      end
    end
  end
  # sets the success status of order along with processing required
  def set_successful_payment_status!(amount)
    return if self.state == 'sane'
    if (amount.to_i == self.total) || (amount.to_i >= (self.total - 10))
      if self.can_good_data? # essential order condition
        self.notes = "SUCCESS"
        self.good_data!
        SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "mail_order_details_to_buyer", {"#{self.class}": self.id})
        # OrderMailer.sidekiq_delay.mail_order_details_to_buyer(self)
      end
    else
      self.tag_list.add('payment_issue')
      self.add_notes_without_callback("#{self.payment_gateway} Payment difference #{amount.to_i}", 'payment')
    end
    self.save
  end

  def self.post_new_order(id)
    order = Order.find(id)
    if order.total <= 0
      # Ignore the name
      # paypal pending is an event that moves order to pending state
      order.paypal_pending unless order.state == 'pending'
      # order.sidekiq_delay(queue: 'high').good_data  
      # don't know why they are passing only good_data insted of good_data!  
      SidekiqDelayGenericJob.set({queue: 'high'}).perform_async(order.class.to_s, order.id, "good_data") 
    elsif order.bank_deposit?
      order.require_followup
      post_confirmation_postpaid(order)
    elsif order.cod? || order.cbd?
      order.paypal_pending 
      post_confirmation_postpaid(order)
    elsif order.paypal? || order.international? && order.online_payment?
      # order.sidekiq_delay_until(15.minutes.from_now).cancel_unless_confirmed
      SidekiqDelayGenericJob.perform_in(15.minutes.from_now, order.class.to_s, order.id, "cancel_unless_confirmed") 
    end
    order.blacklist_user_order
    order.save
  end

  def self.mobile_good_data(id)
    order = Order.find_by_id(id)
    order.good_data! if order.can_good_data?
  end

  def self.post_confirmation_postpaid(order)
    SidekiqDelayGenericJob.set(queue: 'high').perform_async("OrderMailer", nil, "mail_order_details_to_buyer", {"#{order.class}": order.id})
    #OrderMailer.sidekiq_delay(queue: 'high')
    #           .mail_order_details_to_buyer(order)
    
    if order.cbd?
      post_confirmation_postpaid_cbd(order)
    elsif order.cod?
      post_confirmation_postpaid_cod(order)
    end
  end

  def self.post_confirmation_postpaid_cbd(order)
    if order.cbd?
      SidekiqDelayGenericJob.set(queue: 'high').perform_async("OrdersController", nil, "gharpay_create_order", {"#{order.class}": order.id})  if Rails.env.production?
      #OrdersController.sidekiq_delay(queue: 'high')
      #                .gharpay_create_order(order) if Rails.env.production?
      order.designer_orders.each do |designer_order|
        SidekiqDelayGenericJob.set(queue: 'high').perform_async("OrderMailer", nil, "dispatch_order_to_designer", {"#{order.class}": order.id}, {"#{designer_order.class}": designer_order.id})
        #OrderMailer.sidekiq_delay(queue: 'high').dispatch_order_to_designer(order, designer_order)
      end
    end
  end

  def self.paypal_update_order(id)
    order = Order.find_by_id(id)
    #capture this
    paypal_payment = MirrawPaypal.paypal_rest_api_payment(order.paypal_txn_id)
    if paypal_payment.error.present?
      order.paypal_error = paypal_payment.error
      ExceptionNotify.sidekiq_delay.notify_exceptions('Paypal Order sdk error','Payment failed', { error: paypal_payment.inspect, order_number: order.number })
    end
    if paypal_payment.present?
      case paypal_payment.state
      when 'approved'
        sale = paypal_payment.transactions.first.related_resources.first.sale
        sale_state = sale.state
        order.transaction_fee = sale.transaction_fee.value.to_i
        order.transaction_fee_currency_code = sale.transaction_fee.currency
        amount = sale.amount.total.to_f
        amount_currency_code = sale.amount.currency
        if sale_state == 'completed' && (amount - order.amount_sent_payment_gateway).round(2).abs < 2 #paypal PPCALMISMATCH error resolved
          amount = (amount * order.paid_currency_rate ).round(2)
          order.notes = 'SUCCESS'
          order.payment_gateway = PAYPAL
          order.paypal_payer_id = paypal_payment.payer.payer_info.payer_id
          order.paypal_txn_id = sale.id
          order.payu_error = 'PPNOTXNID' unless sale.id.present?
          order.cart.update_attributes(used: true) if order.cart.present?
          order.set_successful_payment_status!(amount)
        else
          if sale_state == 'completed'
            # order.sidekiq_delay.update_payment_gateway_error_code(error: 'PPCALMISMATCH')
            SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "update_payment_gateway_error_code", error: 'PPCALMISMATCH', sidekiq_request_params: true) 
            order.cancel_order_increase_quantity if order.can_cancel?
          elsif sale_state == 'pending'
            order.paypal_pending! unless order.pending?
            # order.sidekiq_delay.update_payment_gateway_error_code(error: 'PPPENDING')
            SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "update_payment_gateway_error_code", error: 'PPPENDING', sidekiq_request_params: true) 
          else
            order.cancel_order_increase_quantity if order.can_cancel?
            # order.sidekiq_delay.update_payment_gateway_error_code(error: 'PPINCOMPLETE')
            SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "update_payment_gateway_error_code", error: 'PPINCOMPLETE', sidekiq_request_params: true) 
          end
        end
      when 'failed'
        order.cancel_order_increase_quantity if order.can_cancel?
        # order.sidekiq_delay.update_payment_gateway_error_code(error: 'PPFAILED')
        SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "update_payment_gateway_error_code", error: 'PPFAILED', sidekiq_request_params: true) 
      else
        order.cancel_order_increase_quantity if order.can_cancel?
        # order.sidekiq_delay
        #      .update_payment_gateway_error_code(
        #        error: "PPUNKNOWN-#{paypal_payment.state}"
        #      )
        SidekiqDelayGenericJob.perform_async(order.class.to_s,
                                             order.id, 
                                             "update_payment_gateway_error_code", 
                                             error: "PPUNKNOWN-#{paypal_payment.state}", 
                                             sidekiq_request_params: true
                                            ) 
      end
    end
  end

  def in_eu_zone?
    EU_COUNTRIES.include?(country.downcase)
  end

  def mark_addon_payment_complete(additional_payment_id)
    line_item_addons_id = []
    self.line_items.each do |li|
      li.line_item_addons.each do |lia|
        line_item_addons_id << lia.id if lia.status == 'unpaid' && lia.additional_payment_id == additional_payment_id.to_i
      end
    end
    additional_payment = AdditionalPayment.where(id: additional_payment_id).first
    LineItemAddon.where(id: line_item_addons_id).update_all(status: 'paid',snapshot_payable_to: 'mirraw')
    if %w(sane ready_for_dispatch dispatched).include?(self.state)
      payment_state = 'paid'
      self.paid_amount += additional_payment.total
    else
      payment_state = 'added_to_order'
    end
    additional_payment.update_column(:payment_state, payment_state)
    if self.stitching_addon?(true, true)
      OrderMailer.sidekiq_delay.stitching_measurement(self.id)
      send_confirmation_sms_international('stitching_form')
    end
    self.save
  end

  def check_not_picked_up
    designer_orders.all?{|designer_order| %w(new pending).include?(designer_order.state)}
  end

  #capturing razorpay payment
  def self.process_razorpayment(order_id, razorpay_id)
    if (order = Order.where(id: order_id).first).present?
      order.update_column(:razorpay_id,razorpay_id)
      response = RazorpayGateway.payment(razorpay_id, order.total)
      case response
      when 'captured'
        order.save!
      when 'payment_failed'
        order.increment_item_quantity = true
        order.bad_data!
      else
        payment_response = JSON.parse(response)
        if payment_response['error_type'].present?
          order.paypal_error = payment_response
        else
          order.transaction_fee = payment_response["fee"]
          order.payment_gateway_details  = payment_response.slice('bank', 'method', 'tax').merge(razorpay_order_id: payment_response['order_id']).to_json
          order.paypal_error = payment_response.slice('error_description', 'error_code').merge(error_type: 'razorpay').to_json if payment_response['error_code'].present?
          if order.can_good_data?
            order.notes = "SUCCESS"
            order.good_data!
          end
          order.payment_gateway = 'razorpay'
          SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "mail_order_details_to_buyer", {"#{order.class}": order.id})
          #OrderMailer.sidekiq_delay.mail_order_details_to_buyer(order)
        end
        order.save!
      end
    order.update_column(:razorpay_status,RazorpayGateway.payment(razorpay_id))
    end
  end

  def paid_via_wallet
    self.good_data! if self.can_good_data?
  end

  def self.post_confirmation_postpaid_cod(order)
    # order.delay(queue: 'high_priority', priority: -8).attempt_auto_confirm
    SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async( order.class.to_s, order.id, "attempt_auto_confirm")
    # order.delay(queue: 'high_priority', priority: 8).cod_order_scan_for_issues
    SidekiqDelayGenericJob.perform_async( order.class.to_s, order.id, "cod_order_scan_for_issues")
  end

  def international_cod_price(price)
    unless self.currency_rate == 0.0
      ((price/self.currency_rate)*self.currency_rate_market_value).round(2)
    end
  end

  def notes
    self.events.present? ? (self[:notes].to_s + '...' + self.events.collect{|ev| "#{ev.event_timestamp.strftime('%m/%d')}:#{ev.done_by}:#{ev.notes}"}.join('... ')) : self[:notes].to_s
  end

  def self.check_status_line_items(order, type)
    all_line_items = order.line_items.where('line_items.status is null and designer_orders.state not in (?)',['canceled','vendor_canceled']).preload(:design)
    line_items_status = order.order_status(all_line_items)
    case type
    when 'stitching_sent'
      send_mail = line_items_status[:stitch_items] == line_items_status[:stitching_sent_count]
    when 'stitching_done'
      send_mail = line_items_status[:stitch_items] == line_items_status[:stitching_done_count]
    when 'qc_done'
      send_mail = line_items_status[:qc_fail_count] == 0 && line_items_status[:qc_pass_count] == all_line_items.length
    else
      send_mail = false
    end
    if send_mail
      OrderMailer.sidekiq_delay.all_line_items_notification(order.id, type)
      # order.sidekiq_delay.send_line_items_notification(type)
      SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "send_line_items_notification", type)
    end
  end

  def send_line_items_notification(type, design_image_url= nil)
    app_versions = ALLOWED_APP_VERSIONS[40..-1] | ALLOWED_IOS_APP_VERSIONS[7..-1]
    if user.present? && (account = user.account).present? && (api_data = account.api_data).present? && app_versions.include?(api_data.app_version)
      message = [] << order_micro_state_msg(account, type, design_image_url)
      campaign_key = 'Order_Micro_State'
      update_customer_notification(type)
      if FCM_NOTIFICATION
        fcm_token = account.try(:fcm_registration_token)
        FirebaseNotification.fcm_push_notification(message[0]['alert']['title'], message[0]['alert']['body'], message[0]['android']['extra']['PushImageHandler'], fcm_token, api_data.app_source) if fcm_token && api_data.app_source.exclude?('Designer')
      else
        LocalyticsNotification.customer_notification(message.compact, campaign_key, api_data.app_source) if api_data.app_source.include?('Android') && LOCALYTICS_NOTIFICATION == 'true'
        ClevertapNotification.modify_and_push_notification(message.compact, campaign_key, api_data.app_source.downcase) if app_source.include?('iOS') || CLEVERTAP_NOTIFICATION == 'true'
      end
    end
  end

  def send_stitching_notification(count=1)
    items = self.line_items.not_canceled_items.any?{|l| l.stitching_required == 'Y' && l.status.blank? && l.stitching_measurements.size < l.quantity}
    if items && count <= STITCHING_NOTIFICATION_DELAY['notification_count']
      self.send_line_items_notification('stitching_form')
      time = DateTime.current + (STITCHING_NOTIFICATION_DELAY['delay_time'] * count).hours
      # self.sidekiq_delay_until(time).send_stitching_notification(count+1)
      SidekiqDelayGenericJob.perform_in(time ,self.class.to_s, self.id, "send_stitching_notification", count+1)
    end
  end

  def order_micro_state_msg(account, type, design_image_url)
    context = {msg: LOCALYTICS_MESSAGES["#{type}"], image_url: design_image_url || notification_image_url("#{type}_notify.png")}
    {
      target: account.id.to_s,
      alert: context[:msg],
      android: { extra: extra_params(context).as_json }.as_json
    }.as_json
  end

  def extra_params(context)
    {
      type: 'OrderDetail',
      bundleFrom: "clevertap",
      ignore_threshold: true,
      notificationMainTitle: context[:msg]['title'],
      PushImageHandler: context[:image_url],
      orderId: id,
      orderNumber: number
    }
  end

  def update_customer_notification(type)
    if (notification = user.notification).present? &&  (notify = notification.body).present? && (order_status_notification = (notify.select{ |n| n['id'] == id}).first).present?
      order_status_notification['title'], order_status_notification['msg'] =  CustomerPanel.get_notification_status(type, number)
      order_status_notification['update_key'] += 1
      order_status_notification['active'] = true
      notification.save!
    end
  end

  def self.find_and_delete_previous_related_orders(order_id)
    states = 'new', 'cancel'
    if (current_order = Order.find_by_id(order_id)).present? &&
      (orders = Order.joins(:cart).where('orders.created_at > ?', 1.week.ago).where("orders.id !=? AND cart_id=? AND carts.user_id=?", current_order.id, current_order.cart_id, current_order.user_id).where(state: states)).present?
      orders.each do |order|
        if order.line_items.empty?
          order.destroy
        else
          order.add_notes_without_callback("New order #{current_order.number} created old user id #{order.user_id} removed", 'payment')
          order.update_column(user_id: nil)
        end
      end
    end
  end

  def self.compute_order_complete(order_hash,design_quantity)
    if order_hash.keys.length != 1
      return :error, "Multiple Orders Found #{order_hash.keys.join(', ')}"
    else
      order = Order.find_by_number(order_hash.keys[0])
      quantity_not_match, not_received_design = [], []
      not_found_design = []
      design_ids, variant_names = [], {}
      unless order.present?
        return :error, 'Order Missing'
      end
      if order.state != 'ready_for_dispatch' && order.state != 'partial_dispatch' 
        return :error, 'Order Not In READY FOR DISPATCH state or either not in PARTIAL DISPATCH state'
      end

      error_tag_names = if order.state == 'ready_for_dispatch'
                          order.tags.select { |t| Order::ORDER_CHECK_BLOCK_TAG_TYPES.include?(t.tag_type) }.map(&:name)
                        elsif order.state == 'partial_dispatch'
                          order.tags.select { |t| ORDER_PARTIAL_DISPATCH_BLOCK_TAG_NAME_PREFIXES.any? { |prefix| t.name.downcase.start_with?(prefix) } }.map(&:name)
                        end
      return :error, "Order contains tag #{error_tag_names.join(', ')}" if error_tag_names.present?

      if order.state == 'ready_for_dispatch' && order.designer_orders.where('state IN (?)',%w(new pending replacement_pending)).exists?
        return :error, 'Some Designer Orders are in new, pending or replacement_pending state'
      end
      sane_items = order.line_items.preload(variant: :option_type_values).joins(:designer_order)
      if order.state == 'ready_for_dispatch'
        sane_items = sane_items.dispatchable.where(designer_orders: { state: %w[dispatched completed critical] })  
      elsif order.state == 'partial_dispatch'
        sane_items = sane_items.joins(:order, :designer_order).where(orders: { state: 'partial_dispatch' }, shipment_id: nil, status: nil).where.not(designer_orders: { state: 'canceled' }).prepared_items
      end
      scanned_design_ids = design_quantity.keys.to_set
      if (buckets = sane_items.select{|item| scanned_design_ids.include?((item.variant ? [item.design_id, item.variant_id].join('-') : item.design_id.to_s))}.collect(&:shipment_bucket_id).compact.uniq).count > 1
        msg = 'Items of different bucket scanned.<br>'
        sane_items.select{|item| scanned_design_ids.include?((item.variant ? [item.design_id, item.variant_id].join('-') : item.design_id.to_s))}.group_by(&:designer_order_id).each_with_index{|(k,v),i| msg += "Bucket #{i+1} : #{v.collect(&:design_id)}<br>"}
        return :error, msg
      end
      if buckets.first.present?
        cargo_bucket_id = buckets.first
        sane_items = sane_items.where(shipment_bucket_id: cargo_bucket_id)
      end
      sane_items.each do |item|
        design_ids << (key = (item.variant ? [item.design_id, item.variant_id].join('-') : item.design_id.to_s))
        variant_names[key] = (item.variant ? [item.design_id, item.variant.option_type_values[0].try(:p_name)].join('-') : item.design_id.to_s)
      end
      design_ids = design_ids.to_set
      if scanned_design_ids != design_ids
        message = "Order Consists Of Missing Designs: #{variant_names.values_at(*(design_ids - scanned_design_ids).to_a).join(' ,')} <br>"
        message += "Order Consists Of Extra Designs: #{variant_names.values_at(*(scanned_design_ids - design_ids).to_a).join(' ,')}"
        return :error, message
      end
      sane_items.each do |line_item|
        key = line_item.variant_id ? [line_item.design_id, line_item.variant_id].join('-') : line_item.design_id.to_s
        if design_data = design_quantity[key]
          design_quantity[key][:line_item_id] = line_item.id
          quantity_not_match << key if line_item.quantity != design_data[:quantity]
          not_received_design << key if (line_item.received_on.blank? || line_item.qc_done_on.blank?)
        else
          not_found_design << key
        end
      end
      if not_found_design.present? || quantity_not_match.present? || not_received_design.present?
        message = ""
        message += "Designs Not Found: #{variant_names.values_at(*not_found_design).join(', ')} <br>" if not_found_design.present?
        message += "Quantity Does Not Match: #{variant_names.values_at(*quantity_not_match).join(', ')} <br>" if quantity_not_match.present?
        message += "Not Marked Received Or QC Not Done Designs: #{variant_names.values_at(*not_received_design).join(', ')} <br>" if not_received_design.present?
        return :error,message
      else
        return :perfect, order, cargo_bucket_id
      end
    end
  end

  def mark_check_items(current_account, order_weight, volumetric_weight, packaging_weight, accurate_weight, design_weight, shipment_bucket)
    order_weight += 0.03
    self.actual_weight = order_weight.round(3)
    self.volumetric_weight = self.get_normalized_weight(volumetric_weight)
    weight = [volumetric_weight, order_weight].max
    normalized_weight = self.get_normalized_weight(weight)
    shipper_name = self.check_dhl_ecom_atlantic_serviceable(normalized_weight)
    if shipper_name.to_s.downcase == 'fedex'
      weight = [volumetric_weight, order_weight+=0.03].max
      shipper_name = self.get_best_shipper_name(self.get_normalized_weight(weight))
      self.actual_weight += 0.03
    end
    self.best_shipper = shipper_name
    self.other_details[:packaging_weight] = shipper_name.to_s.downcase == 'fedex' ? packaging_weight + 0.06 : packaging_weight + 0.03
    self.other_details[:measured_weight] = accurate_weight
    self.save!
    self.add_notes_without_callback('Checked From OrderCheckPage','dispatch') if self.notes.to_s.exclude?('Checked From OrderCheckPage')
    line_items = self.line_items.where(id: design_weight.values.collect{|i| i[:line_item_id]}.flatten).where('sent_to_invoice is null')
    if line_items.present?
      line_items.each do |line_item|
        key = line_item.variant_id ? [line_item.design_id, line_item.variant_id].join('-') : line_item.design_id.to_s
        if design_weight[key][:line_item_weight].present?
          line_item.update_column(:weight,design_weight[key][:line_item_weight])
          Design.where(id: line_item.design_id).where('actual_weight > ? or actual_weight is null',design_weight[key][:line_item_weight].to_i).update_all(actual_weight: design_weight[key][:line_item_weight].to_i / design_weight[key][:quantity])
        end
      end
      line_items.update_all(sent_to_invoice: "Y-#{Date.today.strftime('%d/%m/%y')}", check_items_done_by: current_account.id, check_items_done_at: DateTime.now)
      WarehouseBucket.bulk_update_warehouse_buckets(bucket_type: 'PackagingBucket', item_ids: line_items.map(&:id), item_type: 'LineItem', new_state: 'Order Checked')
      if shipment_bucket.present? && (bucket = ShipmentBucket.find_by_id(shipment_bucket)).present?
        bucket.sent_to_invoice = true
        bucket.sent_to_invoice_on = DateTime.now
        bucket.actual_weight = self.actual_weight
        bucket.save!
      end
      LineItem.bulk_add_into_scan('LineItem', line_items.collect(&:id), 'Sent For Invoicing', current_account.id)
      LineItem.update_rack_status(condition: {id: line_items.collect(&:id)}, status: 'rack_out')
    end
  end

  def to_param
    number
  end

  def self.decrease_design_quantity(id)
    order = Order.find_by_id(id)
    order.designer_orders.preload(line_items: [:design,:variant]).each do |designer_order|
      designer_order.change_lineitem_design_quantity
    end
    # This is comment out because we're using it in the sane orders.
    #order.reduce_quantity_from_warehouse unless order.bank_deposit?
  end

  def self.update_order_related_details(id)
    if (order = Order.find_by_id(id)).present?
      order.mark_order_as_stitching(false)
      order.update_paypal_rate if order.international?
    end
  end

  def self.send_feedback_mail(name, feedback)
    OrderMailer.sidekiq_delay.send_feedback_mail(name, feedback)
  end

  def cod_delivered?
    self.designer_orders.each do |designer_order|
      return false unless (shipment = designer_order.shipment).present? && (shipment.shipment_state == 'delivered')
    end
    return true
  end

  def wallet_discount(rate =1)
    rate ||= 1
    discount = self.referral_discount.to_f + self.refund_discount.to_f
    (discount*rate).round(2)
  end

  def get_default_best_shipper(weight)
    min_weight = DEFAULT_BEST_SHIPPER_COUNTRIES[self.country]['min_weight'].to_f
    if weight.to_f <= min_weight
      return DEFAULT_BEST_SHIPPER_COUNTRIES[self.country]['best_shipper_1'].to_s
    else
      return DEFAULT_BEST_SHIPPER_COUNTRIES[self.country]['best_shipper_2'].to_s
    end
  end

  def get_best_shipper_name(weight = nil)
    #best shipper fixed for internation cod countries.
    if self.cod? && self.international? && COD_COUNTRY_SHIPPER[self.country]
      best_shipper_name = COD_COUNTRY_SHIPPER[self.country]
      return best_shipper_name
    end
    shipping_country = self.country.presence || self.billing_country
    country_id = Country.find_by_namei_cached(shipping_country.strip.downcase).try(:[], :id)
    (weight = self.volumetric_weight.presence || self.actual_weight) unless weight.present?

    if ((((self.ready_for_dispatch_at.presence || Time.current) - self.confirmed_at)/ 1.day) <= ORDER_PROCESSING_DAYS) && COUNTRY_SPECIFIC_SHIPPER_LOGIC[self.country].present? && COUNTRY_SPECIFIC_SHIPPER_LOGIC[self.country]['max_weight'] >= weight
      return COUNTRY_SPECIFIC_SHIPPER_LOGIC[self.country]['shipper_name']
    end

    #specific shipper for specific countries mention in constant DEFAULT_BEST_SHIPPER_COUNTRIES
    if DEFAULT_BEST_SHIPPER_COUNTRIES[self.country].present? && (best_shipper = self.get_default_best_shipper(weight)).present?
       return best_shipper
    end
    if DISABLE_ADMIN_FUCTIONALITY['enable_pdd_check_in_best_shipper']
      shipper_data = ShipmentCharge.get_shippers_by_country(country_id)
      shipper_ids = shipper_data.map(&:first).uniq
      case
      when shipper_ids.size > 1
        many_shippers_country_wise(shipper_ids, country_id, weight)
      when shipper_ids.size == 1
        shipper_name = shipper_data.map(&:second).first
      end
    else
      ShipmentCharge.get_shipper_name(weight, country_id)
    end
  end

  def many_shippers_country_wise(shipper_ids, country_id, weight)
    pdd_not_breaching_shippers = ShipperCountryEstimation.get_shippers_not_breaching_pdd(shipper_ids, country_id, self.id).presence || []
    case
    when pdd_not_breaching_shippers.size > 1
      weight_wise_shipper = ShipmentCharge.get_shipper_name(weight, country_id)
      if weight_wise_shipper.present? && (pdd_not_breaching_shippers.include? weight_wise_shipper)
        weight_wise_shipper
      elsif
        weight_wise_shipper.present?
        pdd_not_breaching_shippers.first
      end
    when pdd_not_breaching_shippers.size == 1
      pdd_not_breaching_shippers.first
    when pdd_not_breaching_shippers.size < 1
      ShipmentCharge.get_shipper_name(weight, country_id)
    end
  end

  def get_paypal_rate
    country_code = CURRENCY_CONVERT_TO_USD[self.country_code] ? CURRENCY_CONVERT_TO_USD[self.country_code]["country_code"] : self.country_code
    currency_record = CurrencyConvert.currency_convert_memcached.find{|cc| cc.country_code == country_code}
    rate = currency_record.try(:rate)
    currency_rate = CURRENCY_CONVERT_TO_USD[self.country_code] ? rate : self.currency_rate
    paypal_rate = currency_record.try(:paypal_rate)
    paypal_rate.nil? ? currency_rate : (rate / paypal_rate)
  end

  def order_status(all_line_items)
    status = []
    status_hash = {item_receive_count: 0, qc_pass_count: 0, qc_fail_count: 0, stitching_sent_count: 0, stitching_done_count: 0, stitch_items: 0, line_items_count: all_line_items.length}

    all_line_items.each do |item|
      item_design = item.design
      status_hash[:item_receive_count] += 1 if item.received == 'Y'
      status_hash[:qc_pass_count] += 1 if (item_design.skip_qc == false && item.qc_done == 'Y' && item.qc_status) || item_design.skip_qc == true
      status_hash[:qc_fail_count] += 1 if item_design.skip_qc == false && item.qc_done == 'Y' && item.qc_status == false
      status_hash[:stitch_items] += 1 if item.stitching_required == 'Y'
      status_hash[:stitching_done_count] += 1 if item.stitching_done == 'Y'
      status_hash[:stitching_sent_count] += 1 if item.stitching_sent == 'Y'
    end
    return status_hash
  end

  def should_convert_to_usd?(atlantic: false)
    code = [paid_currency_code, currency_code]
    (atlantic && (ATLANTIC_SUPPORTED_CURRENCIES & code ).blank?) ||
    (CONVERT_TO_USD_CURRENCIES & code).present?
  end

  def get_commercial_values(invoice = false,shipper = nil)
    commercial_invoice_countries = CurrencyConvert.get_commercial_countries
    commercial = false
    country    = self.country.strip.downcase
    is_atlantic = shipper == 'atlantic'
    if (commercial_invoice_countries.keys.include? country) || invoice
      commercial = true
      if should_convert_to_usd?(atlantic: is_atlantic) && ((self.order_notification.present? && order_notification['paypal_rate'].to_f != 0) || self.paid_currency_rate.present?) && (shipper!='aramex' && self.pay_type!='Cash On Delivery' && currency_code !='AED')
        paypal_rate = self.order_notification['paypal_rate'] || self.paid_currency_rate.presence
        currency_code = 'USD'
      elsif self.pay_type == 'Bank Deposit'
        paypal_rate = self.currency_rate
        currency_code = self.currency_code
        if shipper == 'fedex' && Order::INDIAN_CUSTOM_ALLOWED_CURRENCIES.exclude?(self.currency_code)
          if self.order_notification.present? && self.order_notification['paypal_rate'].present?
            paypal_rate = self.order_notification['paypal_rate']
            currency_code = 'USD'
          elsif (rate = commercial_invoice_countries["#{country}"].to_f) > 0
            paypal_rate = self.currency_rate / rate
            currency_code = 'USD'
          end
        end
      else
        paypal_rate = self.paid_currency_rate
        currency_code = self.paid_currency_code
        if paypal_rate.blank? && (rate = commercial_invoice_countries["#{country}"].to_f) > 0 && self.pay_type != COD
          paypal_rate = self.currency_rate / rate
          currency_code = 'USD'
        end
        unless paypal_rate.present?
          paypal_rate = self.currency_rate
          currency_code = self.currency_code
        end
      end
      if paypal_rate == 1 && self.international? && (cc = CurrencyConvert.where(country: self.country.try(:strip)).first).present?
        paypal_rate = (self.order_notification.present? && order_notification['exchange_rate'].to_f != 0) ? order_notification['exchange_rate'] : (cc.exchange_rate.presence || cc.market_rate)
        currency_code = cc.symbol
      end
    else
      paypal_rate = 1
      currency_code = 'INR'
    end
    currency_code = 'INR' if (['rs.','rs'].include? currency_code.downcase)
    if self.best_shipper.downcase == 'atlantic'
      currency_code,paypal_rate = self.get_shipping_country_code
    end
    return [commercial,paypal_rate,currency_code]
  end

  def get_shipping_country_code
    cc = CurrencyConvert.find_by_namei(self.country)
    return cc.iso_code, cc.rate
  end 

  #Following task fetches orders whose all items are recieved and mark them as ready_for_dispatch
  def self.mark_orders_ready_for_dispatch(done_by_account=nil)
    now  = Time.zone.now
    order_ids, rfd_done_numbers = [], []
    designer_order_ids =[]
    Order.unscoped.where(state: 'sane',items_received_status: true,payment_state: 'completed',items_received_on: now.beginning_of_day-3.month..now).preload(:tags,:designs,:line_items,:rfd_designer_issues).find_in_batches(batch_size: 400) do |orders_grp|
      next if orders_grp.blank?
      orders_grp.each do |order|
        if (all_dos_ids = order.check_order_valid_for_rfd(false, done_by_account)).present?
          designer_order_ids += all_dos_ids
          order_ids << order.id
          rfd_done_numbers << order.number
        end
      end
    end
    Order.unscoped.where(id: order_ids).update_all(state: 'ready_for_dispatch',ready_for_dispatch_at: DateTime.now)
    Order.post_rfd_changes_in_bulk(order_ids)
    if rfd_done_numbers.present?
      file = CSV.generate do |csv|
        csv << ['Order Number']
        rfd_done_numbers.each{|num| csv << [num]}
      end
      filename = "Auto RFD Report #{Time.current.strftime('%d-%b-%Y %I:%M %p')}"
      emails = {'to_email'=> (done_by_account.try(:email).presence || '<EMAIL>'), 'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}
      OrderMailer.report_mailer(filename, 'Please Find Attachment.',emails,{"#{filename}.csv" => file}).deliver
    end
  end

  def check_order_valid_for_rfd(to_save = false, done_by_account=nil)
    dos_ids = []
    self.reload if to_save
    if (self.international? || self.country_code != 'IN') && self.sane? && self.items_received_status && self.payment_state == 'completed' && self.rfd_designer_issues.empty? && !self.tags.to_a.any?{|tag| Order::ORDER_RFD_BLOCK_TAG_TYPES.include?(tag.tag_type)}
      dos_ids = self.designer_orders.select { |d| d.state != 'canceled'}.map(&:id)
      line_items = self.line_items.select { |l| dos_ids.include? l.designer_order_id }.select { |l| l.status != 'cancel'}
      stitching_required_count,stitching_done_count, qc_incorrect = 0,0, false
      line_items.each do |li|
        break if (qc_incorrect = (li.qc_done_on.blank? || (!li.qc_status && (li.issue_status.blank? || li.issue_status == 'Y'))))
        stitching_required_count += 1 if li.stitching_required == 'Y'
        stitching_done_count += 1 if li.stitching_done == 'Y'
      end
      if (line_items.present? && !qc_incorrect &&
        stitching_required_count == stitching_done_count)
        if to_save
          self.skip_before_filter = true
          self.items_ready_dispatch
        end
        self.remove_tags_skip_callback('stitching', 'fnp', 'FNP', 'PCOT')
        self.add_notes_without_callback('ready_for_dispatch', 'state_change', done_by_account)
      else
        dos_ids = []
      end
    end
    dos_ids
  end

  def get_invoice_number(sane_invoice=false)
    year = Time.current.strftime("%y")
    if sane_invoice
      month = Time.current.strftime("%b")
      date = ''
      invoice = SystemConstant.where(name: 'PROFORMA_INVOICE_NUMBER').first
      inv = invoice.value.split('/')
      number = (month != inv[2] ? 1 : (inv[1].to_i + 1))
      if %w(Jan Feb Mar).include?(month)
        date = "MOSPL/#{number}/#{month}/#{year.to_i-1}#{year}"
      else
        date = "MOSPL/#{number}/#{month}/#{year}#{year.to_i+1}"
      end
      self.other_details["proforma_invoice_#{date}"] = nil
    elsif self.order_notification.present? && (key=self.order_notification.find{|k,v| k.to_s.include?('invoice') && v.blank? && k.to_s.split('_')[1].split('/')[1] == Time.current.strftime("%m")}.try(:[],0)).present?
      return key.to_s.split('_')[1]
    else
      month = Time.current.strftime("%m")
      date = ''
      invoice = SystemConstant.where(name: 'UNIQUE_INVOICE_NUMBER').first
      inv = invoice.value.split('/')
      if month != inv[1]
        number = 1
      else
        number = inv[0][3..inv[0].length-1].to_i + 1
      end
      if %w(01 02 03).include?(month)
        date = "MIR#{number}/#{month}/#{year.to_i-1}#{year}"
      else
        date = "MIR#{number}/#{month}/#{year}#{year.to_i+1}"
      end
      self.order_notification["invoice_#{date}"] = nil
      self.other_details['invoice_date'] = Time.current.to_date.to_s
    end
    invoice.value = date
    invoice.save!
    self.skip_before_filter = true
    self.save!(validate: false)
    date
  end

  def get_fedex_service_type
    FEDEX_INTERNATIONAL_CONNECT_PLUS_COUNTRY.include?(self.country_code) ? 'FEDEX_INTERNATIONAL_CONNECT_PLUS' : 'FEDEX_INTERNATIONAL_PRIORITY'
  end

  def generate_order_invoice(shipment_detail = nil,inv_number = nil,paypal_rate = nil)
    self.mark_order_as_stitching(false)
    commercial = false
    unless shipment_detail.present?
      sane_invoice = true
      commercial, paypal_rate, currency_code = international? ? self.get_commercial_values(true,'dhl') : [false,1,'INR']
      if self.other_details.present? && (key=self.other_details.keys.find{|k| k.to_s.include? 'proforma_invoice_'}).present?
        inv_number = key.to_s.split('_')[2]
      else
        inv_number = self.get_invoice_number(sane_invoice)
      end
      shipment_detail = {}
      shipment_detail['currency_code']= currency_code
    end

    selected_items,order_total_price = [],0
    self.designer_orders.preload(line_items: [:line_item_addons,[design: [:categories,property_values: :property]]]).each do |dos|
      line_items = dos.line_items.to_a
      dos_total  = (line_items.sum{|li| (li.snapshot_price * li.quantity)}.to_f)
      line_items.each do |line_item|
        addon_price = line_item.line_item_addons.to_a.sum(&:snapshot_price)
        order_total_price += ((line_item.snapshot_price + addon_price) * line_item.quantity).to_f
        next if line_item.status.present? || ['canceled','vendor_canceled','buyer_returned'].include?(dos.state)
        design = line_item.design
        selected_items << {item_id: line_item.id,
                  addon_price: addon_price,
                  designable_type: design.designable_type,
                  hsn_code: design.categories.hsn_code,
                  designer_discount: (line_item.snapshot_price * line_item.quantity * dos.discount.to_i/dos_total).round(2),
                  name: ((['saree','kurta','kurti','salwarkameez','lehenga','jewellery'].include? design.designable_type.try(:downcase)) ? design.invoice_category_name('dhl').titleize : design.categories.first.name),
                  quantity: line_item.quantity,
                  weight: get_line_item_wise_weight(line_item.quantity).to_f,
                  price: line_item.snapshot_price,
                  meis: line_item.elligible_for_meis?}
      end
    end
    selected_items_group        = selected_items.group_by{|item| item[:name]}
    market_rate = ((cc = CurrencyConvert.where{(symbol == currency_code) | (iso_code == currency_code)}.first).exchange_rate.presence || cc.market_rate)
    invoice_data                = {
                                    items_id: [],
                                    order_total_price: (order_total_price + (commercial ? self.shipping_charges : 0)),
                                    discount: self.get_total_discount,
                                    market_rate: market_rate,
                                    commercial: commercial,
                                    paypal_rate: paypal_rate,
                                    shipper_name: 'dhl'
                                  }
    items_price_total, shipping = 0, {}
    invoice_items               = []
    shipping_charges = commercial ? self.shipping_charges / selected_items.sum{|i| i[:quantity].to_i } : 0
    selected_items_group.each do |product_name, items|
      items_count, item_discounted_price, items_price, hsn_code = Shipment.calculate_invoice_items(items, invoice_items, invoice_data, product_name, shipping_charges)
      items_price_total  += items_price
    end
    shipment_detail['total_price']  = items_price_total.round(2)
    commercial_pdf = if international?
        ActionController::Base.new().render_to_string(
        :template => '/shipments/commercial_invoice',
        :layout   => false,
        :locals   => {:@order => self, :@items => invoice_items, :@shipment_detail => shipment_detail, :@date => inv_number, :@market_rate => market_rate, :@irn_number => nil, :@gst_barcode => nil}
      )
      else
        domestic_invoice_data = Shipment.calculate_domestic_invoice(self, invoice_items, true)
        ActionController::Base.new().render_to_string(
          :template => '/shipments/invoice_designer',
          :layout   => false,
          :locals   => {:@order => self, :@items => invoice_items, :@market_rate => 1, mirraw_domestic: true, invoice_number: inv_number, invoice_data: domestic_invoice_data, domestic_sor: false}
        )
      end
    commercial_invoice = WickedPdf.new.pdf_from_string(commercial_pdf)
    if sane_invoice
      filename = "Orders/ProformaCommercialInvoice/#{Time.now.strftime('%b/%d')}/#{inv_number}/#{shipment_detail['awb_number']}_invoice.pdf"
      AwsOperations.create_aws_file(filename,commercial_invoice,false)
      self.other_details["proforma_invoice_#{inv_number}"] = AwsOperations.get_aws_file_path(filename)
    else
      filename = "Orders/CommercialInvoice/#{Time.now.strftime('%b/%d')}/#{inv_number}/#{shipment_detail['awb_number']}_invoice.pdf"
      AwsOperations.create_aws_file(filename,commercial_invoice,false)
      self.order_notification["invoice_#{inv_number}"] = AwsOperations.get_aws_file_path(filename)
    end
    self.skip_before_filter = true
    self.save!(validate: false)
  end

  def commercial_allow_editing
    @commercial_invoice_countries = CurrencyConvert.get_commercial_countries
    if COMMERCIAL_FOR_AUTOMATION == 'true' && (@commercial_invoice_countries.keys.include? self.country.downcase)
      return true
    else
      return false
    end
  end

  def split_above_x_gbp
    currency_code = (self.paid_currency_code || self.currency_code)
    currency_rate = (self.paid_currency_rate || self.currency_rate)
    total_amt = self.total.nonzero? || (self.discount + self.shipping_charges + self.wallet_discount(self.currency_rate)).round(2)
    return true if self.international? && currency_code == 'GBP' && self.country_code == 'GB' && (ApplicationController.helpers.get_actual_price_in_currency(total_amt, currency_rate) > DHL_ECOM_GBP_LIMIT)
    return false
  end

  def self.post_rfd_changes_in_bulk(order_ids)
    Order.where(id: order_ids).preload(:shipment_buckets).find_each do |order|
      order.divide_order_in_shipment_buckets if order.is_cargo_shipment? && !order.shipment_buckets.present?
      OrderMailer.sidekiq_delay.all_line_items_notification(order.id,'rfd')
      # order.sidekiq_delay.send_line_items_notification('rfd')
      SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "send_line_items_notification", 'rfd')
    end
  end

  def remove_rack_quantity
    designer_orders.where.not(state: ['canceled','vendor_canceled']).preload(:line_items).each do |d_order|
      d_order.change_rack_quantity(d_order.line_items.to_a.sum(&:quantity)) unless d_order.line_items.any?{|item| item.rack_status == 'rack_reduced'}
    end
  end

  def get_domestic_sor_designer_orders        #### returns domestic designer_orders which has sor items
    return self.designer_orders.select {|designer_order| ['new', 'vendor_canceled', 'canceled'].exclude?(designer_order.state) && designer_order.line_items.any? {|line_item| line_item.available_in_warehouse && line_item.status.blank?}}
  end

  def get_shipper_tracking_link(shipper_name, tracking_number)
    track_flag = true
    shipment_state = self.shipments.where(shipments: {number: self.tracking_number}).pluck(:shipment_state).first
    if shipment_state == 'delivered'
      track_flag = false
    end
    case shipper_name.try(:downcase)
    when 'fedex'
      link  = "https://www.fedex.com/apps/fedextrack/?action=track&trackingnumber=#{tracking_number}&cntry_code=us"
    when 'dhl'
      link = "http://www.dhl.co.in/en/express/tracking.html?AWB=#{tracking_number}&brand=DHL"
    when 'aramex'
      link = "http://www.aramex.com/express/track_results_multiple.aspx?ShipmentNumber=#{tracking_number}&submit2=Track"
    when 'skynet'
      link = "http://www.skynetworldwide.net/ShipmentTrackSingle.aspx?textfield=#{tracking_number}&radiobutton=SB"
    when 'atlas'
      link = "http://atlasworldwideexpress.com/tracking.aspx?txtawbno=#{tracking_number}"
    when 'atlantic'
      link = "https://www.atlanticcourier.net/track.php?awbno=#{tracking_number}&search=A"
    when 'delhivery'
      link = "https://www.delhivery.com/track/package/#{tracking_number}"
    when 'dhl ecom'
      link = "https://ecommerceportal.dhl.com/track/?ref=#{tracking_number}"
    else
      link = ''
    end
    [link, track_flag]
  end

  def add_tags_skip_callback(*tag_names)
    tag_names.each do |tag_name|
      tag = ActsAsTaggableOn::Tag.where(name: tag_name).first_or_create
      unless tag_ids.include? tag.id
        tags << tag
      end
    end
  end

  def remove_tags_skip_callback(*tag_names)
    tag_names.each do |tag_name|
      tag = ActsAsTaggableOn::Tag.where(name: tag_name).first
      if tag.present? && self.tag_ids.include?(tag.id)
        ActsAsTaggableOn::Tagging.where(tag_id: tag.id,taggable_id: self.id, taggable_type: 'Order').delete_all
      end
    end
  end

  ## Paytm parameter genration
  def paytm_non_seamless_parameters
    paytm = Mirraw::Application.config.paytm
    values = Hash.new
    cust_id = (buyer_id || billing_phone).to_s.gsub(/[^\d]/, '').rjust(19, '0')
    parameter_list = {
      'CHANNEL_ID' => paytm[:channel_id],
      'CUST_ID' => cust_id,
      'EMAIL' => email,
      'INDUSTRY_TYPE_ID' => paytm[:industry_type_id],
      'MID' => paytm[:mid],
      'MSISDN' => billing_phone.gsub(/[^\d]/, ''),
      'ORDER_ID' => number,
      'REQUEST_TYPE' => 'DEFAULT',
      'TXN_AMOUNT' => total,
      'WEBSITE' => paytm[:website]
    }
    parameter_list['CALLBACK_URL'] = Rails.application.routes.url_helpers.order_paytm_response_url(host: MIRRAW_DOMAIN, protocol: 'https')
    parameter_list['CHECKSUMHASH'] = EncryptionNewPG.new_pg_checksum(parameter_list,paytm[:paytm_merchant_key]).tr("\n",'')
    values[:params] = parameter_list
    values[:payment_url] = paytm[:payment_url]
    values
  end

  def assign_paytm_attributes(paytmparams)
    self.payment_gateway = PAYTM
    self.paytm_txn_id = paytmparams.delete('TXNID')
    self.paytm_txn_status = paytmparams.delete('STATUS')
    self.payment_gateway_details = paytmparams.to_json
    case paytm_txn_status
    when 'TXN_SUCCESS'
      status = 'success'
    when 'TXN_FAILURE'
      status = 'failure'
    else
      status = 'aborted'
    end
  end

  def self.valid_paytm_response?(paytmparams)
    paytm = Mirraw::Application.config.paytm
    if (%w(MID TXNID ORDERID TXNAMOUNT STATUS RESPCODE RESPMSG CHECKSUMHASH) - paytmparams.keys).empty?
      checksum_hash = paytmparams.delete('CHECKSUMHASH').tr("\n",'')
      if EncryptionNewPG::new_pg_verify_checksum(paytmparams, checksum_hash, paytm[:paytm_merchant_key])
        return true
      end
    end
    false
  end

  def self.classify_customer_type(orders)
    #Get all company ids
    companies = {}
    ranks     = {}
    url = 'https://mirraw.freshdesk.com/api/v2/companies'
    headers = {'Authorization' => Base64.encode64(FRESHDESK_API_KEY),'Content-Type' => 'application/json'}
    response = HTTParty.get(url,{headers: headers})
    result = response.parsed_response

    cust_type_values = SystemConstant.get('FRESHDESK_CUSTOMER_TYPES')
    result.each do |company|
      company_name = company['name'].downcase
      if (cust_type_values.keys.include? company_name)
        companies[company['id']] = company_name
        ranks[company_name] = cust_type_values[company_name][1]
      end
    end

    url = 'https://mirraw.freshdesk.com/api/v2/contacts'
    orders.each do |order|
      get_url = url + "?email=#{order.email}"
      response = HTTParty.get(get_url,{headers: headers})
      new_company_id = order.get_customer_type(cust_type_values,companies)
      if (result = response.parsed_response).present? && result[0].present?
        present_customer_rank = ranks[companies[result[0]['company_id']]]
        if present_customer_rank.nil? || (present_customer_rank.present? && ranks[companies[new_company_id]] < present_customer_rank)
          update_url = url + "/#{result[0]['id']}"
          json_payload = {company_id: new_company_id,
                          address: "#{order.city} , #{order.buyer_state} , #{order.country}",
                          phone: result[0]['mobile'].presence || order.phone,
                          mobile: order.phone,
                          custom_fields: {order_source: order.app_source.presence || 'Desktop Duplicate'}
                        }.to_json
          options = {headers: headers, body: json_payload}
          response = HTTParty.put(update_url,options)
        end
      else
        json_payload = { address: "#{order.city} , #{order.buyer_state} , #{order.country}",
                         email: order.email,
                         company_id: new_company_id,
                         mobile: order.phone,
                         phone: order.phone,
                         name: order.name,
                         custom_fields: {order_source: order.app_source.presence || 'Desktop Duplicate'}
                        }.to_json
        options = {headers: headers, body: json_payload}
        response = HTTParty.post(url,options)
      end
    end
  end

  def get_customer_type(cust_type_values,companies)
    new_company_id = cust_type_values.key(cust_type_values.values.min)
    check_dom_int = self.country.strip.downcase == 'india' ? 'i_' : 'd_'
    cust_type_values.each do |key,value|
      condition_values = value[0].split('-').map(&:to_i)
      if (key.exclude? check_dom_int) && self.total >= condition_values[0] && self.total < condition_values[1]
        new_company_id = companies.key(key)
        break
      end
    end
    return new_company_id
  end

  def vacation_day_count
    list = [0]
    line_items.each do  |item|
      vendor = item.designer
      list << vendor.vacation_days_count if vendor.present? && vendor.vacation_mode_on?
    end
    list.max
  end


  def check_weight_and_dimension
    self.actual_weight.present? && self.volumetric_weight.present?
  end

  def self.find_child_duplicate_orders(order)
    notes_for_search = order.notes
    duplicate_order_array = []
    while (index_id = notes_for_search.try{|n| n.index('Duplicate Order:')}).present?
      index_of_from_duplicate = notes_for_search.try{|n| n.index('Duplicated from:')}
      duplicate_from_order_number = notes_for_search[index_of_from_duplicate+17..index_of_from_duplicate+26] if index_of_from_duplicate.present?
      duplicate_order_number = notes_for_search[index_id+17..index_id+26]
      duplicate_order_array << duplicate_order_number if duplicate_order_number != order.number && duplicate_from_order_number.to_s != duplicate_order_number
      notes_for_search = notes_for_search[index_id+26..-1]
    end
    duplicate_order_array.each do |order_number|
      order = Order.select('notes,number').where(number: order_number).first
      duplicate_order_array += find_child_duplicate_orders(order) if order.present?
    end
    duplicate_order_array.uniq
  end

  def find_parent_duplicate_order
    duplicate_order_array = []
    if self.duplicated_from_id.present? && (duplicate_order = self.parent_order).present?
      duplicate_order_array << duplicate_order.number
      duplicate_order_array += duplicate_order.find_parent_duplicate_order
    else
      notes_for_search = self.notes
      if (index_of_from_duplicate = notes_for_search.try{|n| n.index('Duplicated from:')}).present?
        duplicate_order_number = notes_for_search[index_of_from_duplicate+17..index_of_from_duplicate+26]
        duplicate_order_array << duplicate_order_number
      end
      duplicate_order_array.each do |order_number|
        order = Order.select('notes,number,duplicated_from_id,id').where(number: order_number).first
        duplicate_order_array += order.find_parent_duplicate_order if order.present?
      end
    end
    duplicate_order_array.uniq
  end

  def get_status_note(status_hash = {})
    status = ["Order Placed-#670e19-order_placed","Confirmed-#670e19-order_confirm"]
    if self.state == 'dispatched'
      note = "Your order has been handed over to Courier: #{self.courier_company} with tracking number as: #{self.tracking_number}"
      status += get_name_of_stage('dispatched',"#670e19")
      if (self.tracking_number.present? && (shipment_state = self.shipments.where(shipments: {number: self.tracking_number}).pluck(:shipment_state).first).present? && shipment_state == 'delivered')
        note = "Your order is delivered. Thank you for choosing Mirraw !"
        status += ["Delivered-#670e19-delivered"]
      end
    elsif (['ready_for_dispatch','partial_dispatch'].include? self.state)
      note = 'All items are ready for dispatch.'
      status += get_name_of_stage('ready_to_ship',"#670e19") + ['Dispatched-white-dispatched']
    elsif status_hash[:stitch_items] > 0 && status_hash[:stitching_done_count] == status_hash[:stitch_items]
      note = "Stitching of #{status_hash[:stitch_items]} #{'item'.pluralize(status_hash[:stitch_items])} is Completed."
      note += " Preparing for dispatch."
      status += get_name_of_stage('stitching_done',"#670e19") + get_name_of_stage('ready_to_ship','white')
    elsif status_hash[:qc_pass_count] == status_hash[:line_items_count] && self.items_received_status
      if status_hash[:stitch_items] > 0
        if status_hash[:stitching_sent_count] > 0
          note = "#{status_hash[:stitching_sent_count]} out of #{status_hash[:stitch_items]} #{'item'.pluralize(status_hash[:stitch_items])} #{'is'.pluralize(status_hash[:stitch_items])} sent to tailor for stitching."
          status += get_name_of_stage('out_for_stitching','#670e19') + get_name_of_stage('stitching_done',"white")
        else
          note = "All items passed quality check. Stitching will begin soon"
          status += get_name_of_stage('quality_check',"#670e19") + get_name_of_stage('out_for_stitching','white')
        end
      else
        note = "All items passed quality test. Preparing for dispatch."
        status += get_name_of_stage('quality_check',"#670e19") + get_name_of_stage('stitching_done','white')
      end
    elsif self.items_received_status
      note = "#{status_hash[:qc_pass_count]} out of #{status_hash[:line_items_count]} #{'item'.pluralize(status_hash[:line_items_count])} passed quality test."
      note += " #{status_hash[:qc_fail_count]} #{'item'.pluralize(status_hash[:qc_fail_count])} failed quality test. Arranging for replacement." if status_hash[:qc_fail_count] > 0
      status += ["In Warehouse-#670e19-in_warehouse"] + get_name_of_stage('quality_check','white')
    else
      note = "Order is confirmed. "
      if status_hash[:item_receive_count] > 0
        note += "#{status_hash[:item_receive_count]} out of #{status_hash[:line_items_count]} #{'item'.pluralize(status_hash[:line_items_count])} #{'is'.pluralize(status_hash[:line_items_count])} received in warehouse."
      else
        note += "No item received in warehouse yet."
      end
      note += " #{status_hash[:qc_pass_count]} out of #{status_hash[:line_items_count]} #{'item'.pluralize(status_hash[:line_items_count])} passed quality test." if status_hash[:qc_pass_count] > 0 && status_hash[:qc_pass_count] <= status_hash[:item_receive_count]
      status += get_name_of_stage('in_warehouse','white')
    end
    status -= ['Stitching Done-white-stitching_done',"Stitching Done-#670e19-stitching_done",'Out For Stitching-white-out_for_stitching',"Out For Stitching-#670e19-out_for_stitching"] if status_hash[:stitch_items] == 0
    [note,status]
  end

  def line_item_count(line_items)
    s_counts = 'count(received) as received_count, count(qc_done) as qc_done_count,count(stitching_done) as stitching_done_count,count(stitching_required) as stitching_required_count,count(line_items.id) as line_item_count'
    value = line_items.select(s_counts).group("designer_orders.id").where(designer_order_id: designer_order_ids).first
  end

  def update_design_score
    line_items.includes(design: :design_score).each do |line_item|
      (line_item.design.design_score || line_item.design.build_design_score).increment!(:incremental_order,line_item.quantity) if line_item.design.present?
    end
  end
  handle_asynchronously :update_design_score, priority: 3

  def get_payment_transaction_id
    payment_gateway = {"payu" => 'payu_mihpayid',"paypal" => 'paypal_txn_id',"razorpay" => 'razorpay_id','g2a' => 'g2a_txn_id'}
    id = case pay_type
          when PAY_WITH_AMAZON
            { amazon_order_id: self.amazon_order_id}
          when PAYTM
            { paytm_txn_id: self.paytm_txn_id}
          when PAYPAL
            { paypal_txn_id: self.paypal_txn_id}
          when PAYMENT_GATEWAY
            attribute = payment_gateway[self.payment_gateway.to_s.downcase]
            if attribute.present?
              { attribute => self.try(attribute) }
            elsif paypal_txn_id.present?
              { paypal_txn_id: paypal_txn_id }
            else
              {"None" => '-'}
            end
          when BANK_DEPOSIT
            { bank_deposit_txn_id: bank_deposit_txn_id }
          else
            {"None" => '-'}
          end
  end

  def check_paytm_status
    paytm = Mirraw::Application.config.paytm
    parameter_list = {
      'MID' => paytm[:mid],
      'ORDERID' => number
    }.to_json
    query_s = "?JsonData=#{parameter_list}"
    begin
      res = JSON.parse(HTTParty.get(paytm[:transaction_status_url] + query_s).body)
    rescue Exception => e
      ExceptionNotify.sidekiq_delay.notify_exceptions('Paytm Status', e.message, parameter_list)
      return "not able to confirm status"
    end
    if res["ErrorCode"].present? && PAYTM_STATUS_CHECK_ERROR_REPORT
      ExceptionNotify.sidekiq_delay.notify_exceptions('Status check response reports error', 'Paytm Status Check Error', {
        params: parameter_list,
        response: res,
        order_number: number
      }
    )
    end
    error_details = []
    error_details << 'Txn_id' unless res['TXNID'] == paytm_txn_id
    error_details << 'Txn_status' unless res['STATUS'] == 'TXN_SUCCESS'
    error_details << 'Txn total' unless res['TXNAMOUNT'].to_i == total
    error_details.empty? || error_details.join(', ') + " did not match"
  end

  def send_feedback_notification
    app_versions = ALLOWED_APP_VERSIONS | ALLOWED_IOS_APP_VERSIONS[5..-1]
    if user.present? && (account = user.account).present? && (api_data = account.api_data).present? && app_versions.include?(api_data.app_version)
      message = [] << nps_feedback_message(account, api_data)
      campaign_key = 'NPS_FeedBack'
      if FCM_NOTIFICATION
        fcm_token = account.try(:fcm_registration_token)
        FirebaseNotification.fcm_push_notification(message[0]['alert']['title'], message[0]['alert']['body'], message[0]['android']['extra']['PushImageHandler'], fcm_token, api_data.app_source) if fcm_token && api_data.app_source.exclude?('Designer')
      else
        LocalyticsNotification.customer_notification(message.compact, campaign_key, api_data.app_source) if api_data.app_source.include?('Android') && LOCALYTICS_NOTIFICATION == 'true'
        ClevertapNotification.modify_and_push_notification(message.compact, campaign_key, api_data.app_source.downcase) if app_source.include?('iOS') || CLEVERTAP_NOTIFICATION == 'true'
      end
    end
  end

  def nps_feedback_message(account, api_data)
    {
      target: account.id.to_s,
      alert: LOCALYTICS_MESSAGES['nps_feedback'],
      android: { extra: extra_payload(api_data).stringify_keys }.stringify_keys
    }.stringify_keys
  end

  def extra_payload(api_data)
    app_specific_payload(Survey.generate_token(email,number), api_data.app_version, api_data.app_source).merge(
      bundleFrom: 'localytics',
      PushImageHandler: SystemConstant.get('FEEDBACK_NOTIFICATION_IMAGE_URL'),
      ignore_threshold: true,
      notificationMainTitle: get_user_message(LOCALYTICS_MESSAGES['nps_feedback']['title'])
    )
  end

  def app_specific_payload(token, app_version, app_source)
    if app_source.include?('iOS')#ALLOWED_APP_VERSIONS[38..-1].include?(app_version) || app_source.include?('iOS')
      {
        type: 'Web',
        link: "#{Rails.application.routes.url_helpers.new_survey_url(host: 'm.mirraw.com')}?order=#{Base64.urlsafe_encode64({order_number: number, email: email}.to_json)}&token=#{token}"
      }
    else
      {
        type: 'Web',
        webLink: "#{Rails.application.routes.url_helpers.new_survey_url(host: 'm.mirraw.com')}?order=#{Base64.urlsafe_encode64({order_number: number, email: email}.to_json)}&token=#{token}"
      }
    end
  end

  def notification_image_url(img)
    "#{ActionController::Base.helpers.image_path(img)}"
  end

  def all_shipments_delivered?
     (states = shipments.pluck(:shipment_state).uniq).count == 1 && states.first == 'delivered'
  end

  def get_user_message(title)
    "Hi #{user.try(:short_name)}, #{title}"
  end

  def validate_buy1_get1
    b1g1_quantity = 0
    self.designer_orders.collect{|dso| b1g1_quantity += dso.line_items.to_a.sum{|item| (item.buy_get_free == 1) ? item.quantity : 0} }
    return (b1g1_quantity > 1)
  end

  def self.tag_measurements(order_id,review_id,design_id,tag_name)
    StitchingMeasurement.where('measurement_name IS NOT NULL AND NOT user_review ? :key',key:'measurement_tag').where('design_id = ? AND order_id = ?',design_id,order_id).update_all([%(user_review = COALESCE(user_review,'') || hstore(?,?) || hstore(?,?)),'measurement_tag',tag_name,'review_id',review_id.to_s])
  end

  def add_order_addons(addons = {})
    order_addons_hash = (addons.collect { |key, value| [key.to_sym, Kernel.const_get(key.upcase).to_f] if value }).compact.to_h
    build_order_addon(order_addons_hash) unless order_addons_hash.empty?
  end

  def get_gift_wrap_price(paypal_rate)
    self.order_addon.present? ? (self.order_addon.try(:gift_wrap_price).to_f/paypal_rate).round(2) : 0
  end

  def get_normalized_weight(weight)
    rounded_weight = weight.round(2)
    if confirmed_at && ((((ready_for_dispatch_at.presence || Time.current) - confirmed_at)/ 1.day) <= 4 || DHL_ECOM_PINCODE.include?(self.pincode)) && ['US','GB','AU'].include?(self.country_code) && rounded_weight <= 1.0
      if rounded_weight != 0.0
        (rounded_weight*20).ceil/20.0
      else
        0.05
      end
    else
      ((weight*2).ceil.to_f / 2)
    end
  end


  def gokwik_create_order_api(order)
    gokwik = GoKwik.new(order)
    gokwik.create_order
  end

  def update_cost_estimation
    #shipping cost Estimation
    weight = 0
    flag = true
    LineItem.joins(:designer_order, :design).select('line_items.quantity, line_items.id, designer_orders.state, designs.actual_weight').where('designer_orders.order_id = ?', id).dispatchable.group('line_items.id, designer_orders.id, designs.id').each do |line_item|
      weight += line_item.actual_weight.to_i * line_item.quantity if line_item.actual_weight && ['canceled','vendor_canceled'].exclude?(line_item.state)
      flag = false and break if line_item.actual_weight.blank?
    end
    shipping_country = country || billing_country
    shipping_cost = nil
    if flag
      country_id = Country.find_by_namei_cached(shipping_country.strip.downcase).try(:[], :id)
      shipping_cost = ShipmentCharge.select('price').where(weight: (weight/1000.0 * 2).ceil/2.0, country_id: country_id).order('price asc').first.try(:price).to_f
    end
    #stitching cost estimation
    stitching_cost = LineItem.joins(:designer_order,line_item_addons: :addon_type_value).select("sum(addon_type_values.cost * line_items.quantity) as addon_cost").where('lower(stitching_required) = ? and designer_orders.order_id = ?','y',id).where('designer_orders.state not in (?)',['vendor_canceled', 'canceled']).dispatchable[0].try(:addon_cost).to_i
    order_estimation = OrderEstimation.where(order_id: self.id).first_or_initialize
    order_estimation.shipping_cost  = shipping_cost
    order_estimation.stitching_cost = stitching_cost
    order_estimation.save
  end

  def self.get_order_estimation_report(start_date, end_date)
    flag = false
    file = CSV.generate do |csv|
      csv << 'Sane Order date, Invoice No, Order Number, Order State,TOTAL Amount,Paid Total Amount,Gateway,Country ship,Transaction Id,Shipping Charges RECD inr,Addon recd,Shipping payable, Shipping paid,Stitching Payable,Stitching paid,vendor Payout,Paid Payout,Net,Conversion Rate,Currency Payment,in foreign'.split(',')
      clause = 'orders.*, order_estimations.stitching_cost, order_estimations.paid_shipping_cost, order_estimations.paid_stitching_cost, order_estimations.paid_payout, order_estimations.paid_total_amount, order_estimations.shipping_cost, SUM(designer_orders.payout) as payout'
      Order.joins(:order_estimation,:designer_orders).select(clause).where('orders.created_at between ? and ?', start_date.to_date.beginning_of_day, end_date.to_date.end_of_day).where('orders.confirmed_at is not null').group('orders.id, order_estimations.id').find_each do |order|
        invoice_no          = order.order_notification.keys.join(',').match(/invoice_MIR[0-9a-zA-Z|\/|\-]*$/) if order.order_notification.keys.join(',').match(/invoice_MIR[0-9|\/]+/).present?
        invoice_no          = invoice_no.to_s.tr('invoice_','')
        foreign             = (order.paid_amount.to_f/order.currency_rate.to_f).round(2)
        total_paid_amount   = (foreign * order.currency_rate_market_value.to_f).round(2)
        pay_types           = {'bank_deposit_txn_id'=>'Bank Deposit', 'paypal_txn_id'=>'PayPal', 'payu_mihpayid'=>'PAYU', 'amazon_order_id'=>'Pay with Amazon', 'paytm_txn_id'=>'Paytm', 'razorpay_id'=>'Razorpay', 'g2a_txn_id'=>'G2A'}
        txn_id              = order.attributes.values_at(*pay_types.keys).compact
        gateway             = pay_types.values_at(*order.attributes.invert.values_at(*txn_id)).join(', ')
        payout              = order.payout
        shipping            = (order.shipping.to_i / order.currency_rate.to_f * order.currency_rate_market_value.to_f).round(2)
        addon_charges       = (order.mirraw_addon_charges.to_i / order.currency_rate.to_f * order.currency_rate_market_value.to_f).round(2)
        net                 = total_paid_amount.to_f - order.shipping_cost.to_f - order.stitching_cost.to_f - payout.to_f
        flag                = true unless flag
        csv << [order.confirmed_at, invoice_no, order.number, order.state, total_paid_amount, order.paid_total_amount, gateway, order.country, txn_id.join(', '), shipping, addon_charges, order.shipping_cost, order.paid_shipping_cost, order.stitching_cost, order.paid_stitching_cost, payout, order.paid_payout, net, order.currency_rate_market_value, order.currency_code, foreign]
      end
    end
    subject = 'Order Estimation Report'
    content = flag ? "Report Contains Orders from #{start_date} to #{end_date}" : 'No Orders found'
    email ={ 'to_email' =>'Mirraw.com <<EMAIL>>','from_email_with_name' => 'Mirraw.com <<EMAIL>>' }
    OrderMailer.report_mailer(subject,content,email,{'Order Estimation Report.csv' => file}).deliver
  end

  def error_capture_through_ec_token(token)
    begin
      log = {}
      response = client.details(token)
      log["status"] = response.checkout_status
      log["long_msg"] = response.payment_responses[0].long_message
      log["error_code"] = response.payment_responses[0].error_code
      self.update_column(:paypal_error,log.to_json)
    rescue Paypal::Exception::APIError => error
      log["status"] = error.response.ack
      log["long_msg"] = error.response.raw[:L_LONGMESSAGE0]
      log["error_code"] = error.response.raw[:L_ERRORCODE0]
      self.update_column(:paypal_error,log.to_json)
    end
  end

  def check_dhl_ecom_atlantic_serviceable(weight)
    shipping_country_code = Country.find_by_namei_cached(country).try(:[], :iso3166_alpha2)
    is_dhl_ecom_serviceable_country = ['US', 'AU', 'GB'].include?(shipping_country_code)
    if (best_shipper = STATE_WISE_COURIER_CHECK.find{|k,v| v[shipping_country_code].to_a.include?(state_code)}).present?
      shipper_name = best_shipper.first
    else
      shipper_name = self.get_best_shipper_name(weight)
    end
  end

  def self.update_cost_estimation_report(file, type, email)
    case type.to_sym
    when :stitching
      object, uniq_id, column, order_id = 'TailoringInfo', :id, :paid_stitching_cost, :order_id
    when :shipping
      object, uniq_id, column, order_id = 'Shipment', :number, :paid_shipping_cost, :order_id
    when :total_amount
      object, uniq_id, column, order_id = 'Order', :number, :paid_total_amount, :id
    when :payout
      object, uniq_id, column, order_id = 'DesignerOrder', :id, :paid_payout, :order_id
    end
    not_found = []
    CSV.new(open(AwsOperations.get_aws_file_path(file)), headers: true).each_slice(1000) do |rows|
      rows = rows.group_by{|row| row[0].to_s}
      order_ids = Kernel.const_get(object).unscoped.where(uniq_id => rows.keys).uniq.pluck(order_id, uniq_id)
      not_found += rows.keys - order_ids.collect{|i| i[1].to_s}
      order_ids = order_ids.group_by{|i| i[0]}
      import_data = []
      OrderEstimation.where(order_id: order_ids.keys).find_each do |oe|
        oe[column] += rows.values_at(*order_ids[oe.order_id].collect{|i| i[1].to_s}).flatten(1).collect{|i| i[1].to_i}.try(:sum)
        import_data << oe
      end
      OrderEstimation.import import_data, on_duplicate_key_update: { conflict_target: [:id], columns: [column]}
      puts import_data
    end
    attachment = {}
    if not_found.present?
      attachment[:file] = {'estimation_update_report.csv'=> not_found.join("\n")}
      attachment[:msg] = 'OrderEstimation update failed For following orders please find attachment of the same below'
    else
      attachment[:msg] = 'OrderEstimation update successfully done'
    end
    OrderMailer.report_mailer('OrderEstimation Update', attachment[:msg], {'to_email'=> email,'from_email_with_name'=> '<EMAIL>'}, attachment[:file] || {}).deliver
  end

  def get_user_total_paid_amount
    market_rate = CurrencyConvert.currencies_marketrate[currency_code] || currency_rate_market_value
    ((paid_amount.to_f / currency_rate.to_f) * market_rate.to_f).round(2)
  end

  def user_paid_amount
    paid_amount * currency_rate_market_value / currency_rate
  end

  # Actual in the sense that the refund money also belongs to the user.
  # And that's why refund plus total makes the actual money.
  #
  # FIXME: Make the +refund_discount+ method return 0.0 by default instead.
  def actual_total
    refund_discount.to_f * currency_rate + total
  end

  def get_stylist_group_name(type)
    type_to_serach = ['lehenga', 'saree'].include?(type) ? ['lehenga', 'saree'] : [type]
    is_order_of_mix_stitching = line_items.sane_items.joins(:design).where(stitching_required: 'Y').where('lower(designs.designable_type) NOT IN (?)', type_to_serach).exists?
    (is_order_of_mix_stitching || ['salwarkameez', 'lehenga', 'saree'].exclude?(type)) ? 'mix' :  type_to_serach.join('+')
  end

  def assign_stylist_as_per_group(designable_type, old_group_name)
    stylist_group_name = (ENABLE_CUSTOM_STANDARD_STYLISTS == 1 ? old_group_name : self.get_stylist_group_name(designable_type))
    Stylist.assign_to_stylist(self, stylist_group_name)
  end

  def send_order_sms(type,email=nil)
    if (phone = get_mobile_num_international) && (template = ORDER_MESSAGES[type])
      mirraw_number = ApplicationController.helpers.mirraw_contact_number(currency_code, actual_country_code)
      template = template % {
        mirraw_number: mirraw_number,
        order_number: number
      }
      add_notes_without_callback("#{type} - SMS sent",nil,email)
      if %w(production admin).include?(Rails.env)
        if !country_code.blank? && ALERT_SMS['countries'].include?(country_code.downcase)
          sms_api = ALERT_SMS['api_url'].sub('{phone}', phone).sub('{template}', template)
        else
          template_based = actual_country_code.try(:downcase) != 'in' ? 'INTERNATIONAL' : nil
          SmsNotification::NotificationService.notify_later(phone, template,'24x7', template_based)
          return true
        end
        (HTTParty.get(sms_api))
      end
    end
  end

  def cashback_available?
    other_details['loyalty_rewards_credit'].to_f > 0
  end

  def cashback_rewarded?
    wallet_transactions.any?(&:cashback_transaction?)
  end

  def share_and_earn_reward_available?
    other_details['share_and_earn_credit'].present? && other_details['share_and_earn_referrer'].present?
  end

  def share_and_earn_cashback_rewarded?
    wallet_transactions.any?(&:share_and_earn_transaction?)
  end

  def delayed_credits_rewarded?
    wallet_transactions.any?{|wt| wt.fund_type == 'late_delivery'}
  end

  def satisfies_cashback_conditions?
    ['cancel', 'cancel_complete'].exclude?(state)
  end

  def ga_data(other_details={})
    {
      id: number, affiliation: (utm_source.presence || 'Mirraw'),
      revenue: total, tax: 0, shipping: shipping, coupon: coupon.try(:code).to_s
    }.merge!(other_details)
  end

  def assign_warehouse_addresses_to_designer_orders
    self.designer_orders.each do |dos|
      dos.set_warehouse_address if dos.warehouse_address_id.blank?
    end
  end

  def assign_attempted_payment_gateway(smart_checkout_type=nil)
    self.attempted_payment_gateway = post_paid? ? nil : if self.paypal?
      smart_checkout_type == 'paypal_smart_pay' ? 'paypal_smartpay' : 'paypal'
    elsif (billing_international? || actual_country_code != 'IN') && online_payment?
      smart_checkout_type == 'paypal_inline_cc' ? 'paypal_smartpay' : 'paypal'
    elsif online_payment? && payu_money?
      'payu_money'
    elsif paytm? && !billing_international? && actual_country_code == 'IN'
      PAYTM
    elsif rand(ENV['REDIRECT_TO_RAZORPAY'].to_i)==1
      'razorpay'
    else
      'payu'
    end
  end

  def credit_cashback_and_notify_user_via_email
    # After 7 Days Don't Send Cashback if Buyer Returned Or RTO
    return if self.domestic? && self.designer_orders.any? { |designer_order| [:buyer_returned, :rto].include?(designer_order.state.to_sym) }
    if cashback_available? && !cashback_rewarded?
      create_user_if_not_present
      current_wallet = get_user_wallet
      if actual_country_code == current_wallet.currency_convert.country_code
        amount_to_credit = other_details['loyalty_rewards_credit'].to_f

        converted_amount_to_credit = if current_wallet.currency_convert.country_code != 'IN'
          (amount_to_credit / currency_rate.to_f).round(2)
        else
          amount_to_credit.round(2)
        end

        current_wallet.credit_loyalty_amount_and_notify(converted_amount_to_credit, user, number, app_source, id, 'referral_amount')
      end
    end
  end

  def get_user_wallet
    unless current_wallet = user.wallet
      currency_convert_id = CurrencyConvert.find_by_country_code(actual_country_code).id
      current_wallet = Wallet.create(currency_convert_id: currency_convert_id)
      user.update_attribute(:wallet_id, current_wallet.id)
    end
    current_wallet
  end

  def self.credit_reward_for_late_delivery(order_id)
    order = Order.find order_id
    if order.user.present? && !order.delayed_credits_rewarded?
      current_wallet = order.get_user_wallet
      if order.actual_country_code == current_wallet.currency_convert.country_code && (amount_to_credit = ((order.paid_amount.to_f/order.currency_rate.to_f) * LATE_DELIVERY_CREDIT/100.0).round(2)) > 0
        wt = current_wallet.wallet_transactions.create(fund_type: 'late_delivery', order_id: order.id, user_id: order.user.id)
        wt['referral_amount'] = amount_to_credit
        wt.save
        current_wallet['referral_amount'] += amount_to_credit
        if current_wallet.save
          wt.success
          # SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "mail_late_delivery_wallet_credit", {"#{self.class}": self.id}, amount_to_credit, current_wallet.currency_convert.try(:iso_code))
          # OrderMailer.sidekiq_delay.mail_late_delivery_wallet_credit(self, amount_to_credit, current_wallet.currency_convert.try(:iso_code))
          OrderMailer.sidekiq_delay.mail_late_delivery_wallet_credit(order.id, amount_to_credit, current_wallet.currency_convert.try(:iso_code))
        else
          wt.failure
        end
      end
    end
  end

  def share_and_earn_reward
    if share_and_earn_reward_available? && !share_and_earn_cashback_rewarded?
      referee = user
      referrer = User.find_by_id(other_details['share_and_earn_referrer'])
      referee_wallet = referee.wallet || referee.assign_wallet(actual_country_code || 'IN')
      referrer_wallet = referrer.wallet || referrer.assign_wallet(actual_country_code || 'IN')
      amount_to_credit = other_details['share_and_earn_credit'].to_f
      converted_amount_to_credit = if referee_wallet.currency_convert.country_code != 'IN'
          (amount_to_credit / currency_rate.to_f).round(2)
        else
          amount_to_credit.round(2)
        end
      if referee_wallet.currency_convert_id == referrer_wallet.currency_convert_id
        referee_wallet.credit_share_and_earn_reward_and_notify(converted_amount_to_credit, referee, 'referee', 'referral_amount', id)
        referrer_wallet.credit_share_and_earn_reward_and_notify(converted_amount_to_credit, referrer, 'referrer', 'referral_amount')
      end
    end
  end

  def assign_epst_lpst_process_dates
    to_be_import, order_level_process_details = [], {}
    operation_process = OperationProcess.get_process_data
    self.line_items.sane_items.preload(:designer_order).group_by{|item| [item.designer_order_id, item.stitching_required]}.each do |des_o_id_with_stitching, items|
      process_dates_hash  = {}
      process_types = des_o_id_with_stitching[1] == 'Y' ? ['order', 'product', 'stitching'] : ['order', 'product', 'non-stitching']
      designer_id = items.last.designer_order.designer_id
      sorted_processes = operation_process.select{|process| process_types.include?(process.process_type)}.sort_by(&:priority)
      store_product_level_process_dates(items, sorted_processes, process_dates_hash, designer_id, order_level_process_details, to_be_import, self.confirmed_at)
    end
    store_order_level_process_dates(order_level_process_details, self, to_be_import)
  end

  def check_if_all_items_dispatched?(move_to_dispatched = false)
    s_line_items        = 'COUNT(line_items.id) as count'
    w_shipment_not_null = 'line_items.shipment_id IS NOT NULL'
    items               = DesignerOrder.unscoped.select(s_line_items).joins(:line_items).where(:order_id => self.id).where('(line_items.status IS NULL OR line_items.status = ?) and designer_orders.state not in (?)', 'buyer_return', ['canceled', 'vendor_canceled'])
    items_dispatched    = items.where(w_shipment_not_null)
    all_dispatched = items[0][:count].to_i == items_dispatched[0][:count].to_i
    if move_to_dispatched && all_dispatched && self.can_package_shipped?
      self.package_shipped!
    elsif !move_to_dispatched
      all_dispatched
    end
  end

  def complete_the_look_suggestions
    Design.joins(%{
      INNER JOIN design_addons ON designs.id = design_addons.addon_product_id
    }).where({
      'design_addons.design_id' => line_items.map(&:design_id)
    }).where.not({
      id: line_items.map(&:design_id)
    }).where(%{
      quantity > 0 AND state = 'in_stock'
    }).order({
      international_grade: :desc, discount_price: :asc
    }).limit(3)
  end

  def assign_it_to_stylist(stitching_count, design_type, group_name)
    if (stylist_ids = STYLIST_ASSIGN_MAPPING.to_h[stitching_count.to_s]).present?
      Stylist.assign_to_stylist(self, '', stylist_ids)
    else
      assign_stylist_as_per_group(design_type, group_name)
    end
  end

  def add_to_payment_gateway_details(value, update_now: false)
    new_details = [payment_gateway_details, value].compact.join("\n")
    if update_now
      update_column(:payment_gateway_details, new_details)
    else
      self.payment_gateway_details = new_details
    end
  end

  def get_credit_note_number
    year = Time.current.strftime("%y")
    month = Time.current.strftime("%m")
    date = ''
    invoice = SystemConstant.where(name: 'UNIQUE_CREDIT_NOTE_NUMBER').first
    inv = invoice.value.split('/')
    if month != inv[1]
      number = 1
    else
      number = inv[0][3..inv[0].length-1].to_i + 1
    end
    if %w(01 02 03).include?(month)
      date = "CRN#{number}/#{month}/#{year.to_i-1}#{year}"
    else
      date = "CRN#{number}/#{month}/#{year}#{year.to_i+1}"
    end
    invoice.value = date
    invoice.save!
    self.skip_before_filter = true
    self.save!(validate: false)
    date
  end

  def self.mail_ipending_report(order_ids, email)
    file = CSV.generate do |csv|
      csv << ['Order Number', 'All Items Received On', 'Notes', 'Promised Date', 'Promised Day Remaining', 'Product ID', 'Team', 'Last Process', 'Receive Status', 'QC Status', 'Stitching Status', 'Confirmed', 'Name', 'Total', 'Tags']
      Order.preload(:latest_event, :tags, :delivery_nps_info, line_items: [:designer, :stitching_measurements, :ipending_related_scans, tailoring_info: [:tailoring_inscan_bags]]).where(id: order_ids).find_each(batch_size: 100) do |order|
        promised_date = order.delivery_nps_info.try(:promised_delivery_date)
        latest_event = order.latest_event
        latest_note = latest_event ? "#{latest_event.event_timestamp.strftime('%m/%d')}:#{latest_event.done_by}:#{latest_event.notes}" : ''
        order_level_data = [order.number, order.items_received_on.try(:strftime, '%d/%m/%Y %H:%M'), latest_note, promised_date.try(:to_date), (promised_date ? ((promised_date - Time.now)/1.day).round(1) : '')]
        order.line_items.each do |item|
          next if item.status.present? || ['canceled', 'vendor_canceled'].include?(item.designer_order.state)
          tag_names = order.try(:tags).to_a.map(&:name).reject{|tag| tag.include?('convert-mkt')}
          process_name, team_name = (tag_names.include?('addon') ? ['Addon Tag Present', 'Support'] : item.get_item_status)
          stitch_status = (item.stitching_required != 'Y' ? 'No Stitching' : (item.stitching_done_on.present? ? 'Stitching Done' : 'Stitching Pending'))
          csv << (order_level_data + [item.design_id, team_name, process_name, (item.received_on.present? ? 'Yes' : 'No'), (item.qc_done_on.present? ? 'QC Done' : 'QC Pending'), stitch_status, order.confirmed_at.try(:strftime, '%d/%m/%Y %H:%M'), order.name, order.total, tag_names.join(',')])
        end
      end
    end
    emails = {'to_email'=> email, 'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}
    OrderMailer.report_mailer("IPENDING REPORT #{DateTime.now.strftime('%d-%m-%Y %I:%M %p')}", 'Please Find Attachment.', emails, {"IPENDING REPORT #{DateTime.now.strftime('%d-%m-%Y %I:%M %p')}.csv"=> file}).deliver
  end

  def get_courier_automation_related_details(response_shipment, courier, account_id, automate_attempt=false, length=0, breadth=0, height=0)
    status = check_for_automation_approval(response_shipment, courier)
    if status[:error]
      return status
    else
      return {error: false, shipment_data: get_shipment_details(courier, account_id, automate_attempt, length, breadth, height)}
    end
  end

  def check_for_automation_approval(response_shipment, courier)
    status = {error: false, error_message: nil}
    if (record = tickets.find{|ticket| SHIPMENT_BLOCK_TICKET_ISSUES.include?(ticket.issue.try(:downcase)) && ['close', 'reject'].exclude?(ticket.state)}).present?
      status[:error_message] = "Order Contains '#{record.issue}' ticket in open state. Please Close them to create shipment."
    elsif check_for_po_box_presence
      status[:error_message] = "User's address still contains a PO Box Number.To avoid further ambiguities, the shipment cannot be created till he/she sends the correct address."
    elsif !(courier == 'delhivery') && international? && !check_weight_and_dimension
      status[:error_message] = "Please fill the weight and volume dimensions of order"
    # elsif response_shipment.blank? && ((actual_weight.to_f * 2).ceil / 2.0) < volumetric_weight.to_f && Shipment.check_for_volumetric_lockout
    #   status[:error_message] = 'Todays threshold of over volume shipment exceeded.Please update the volumetric weight'
    end
    status[:error] = true if status[:error_message].present?
    return status
  end

  def get_shipment_details(courier_name, account_id, automate_attempt,length=0, breadth=0, height=0, cargo_bucket: false)
    index ,content_des, item_name = 0, nil, nil
    all_shipment_item_details = HashWithIndifferentAccess.new({'item' => {}})
    designer_orders.each do |designer_order|
      dos_line_items = designer_order.line_items.to_a
      dos_total      = designer_order.get_item_total
      cargo_designer_wise = dos_line_items.collect(&:design).flatten.compact.collect(&:designable_type).include?('Jewellery')
      dos_line_items.each do |line_item|
        if (cargo_bucket && line_item.shipment_id.nil? && line_item.received == 'Y' && %w(dispatched completed critical).include?(designer_order.state) && line_item.status.blank?) || line_item.check_if_dispatchable
          design = line_item.design
          orignal_item = line_item.get_replacement_product
          item_name = ((is_valid_type = (['saree','kurta','kurti','salwarkameez','lehenga','jewellery','other','islamic'].include? design.designable_type.try(:downcase))) && INVOICE_TITLE_CHANGE.include?(courier_name) ? design.invoice_category_name(courier_name).titleize : design.categories.first.name)
          if content_des.blank? && design.designable_type.try(:downcase) != 'jewellery' && is_valid_type
            content_des = item_name
          end
          item_price = orignal_item.snapshot_price
          item_addon_price = orignal_item.line_item_addons.to_a.sum(&:snapshot_price)
          if courier_name == 'delhivery' && currency_rate_market_value.present?
            item_price = international_cod_price(orignal_item.snapshot_price)
            item_addon_price = international_cod_price(orignal_item.line_item_addons.to_a.sum(&:snapshot_price))
          end
          item_data = {
            item_id: line_item.id,
            designable_type: design.designable_type,
            cargo_designer_wise: cargo_designer_wise,
            hsn_code: design.categories.hsn_code,
            design_link_text: (design.title + '- #' + design.id.to_s),
            design_id: design.id,
            designer_id: design.designer_id,
            image_link: design.master_image.photo(:small),
            addon_price: item_addon_price,
            designer_discount: (orignal_item.snapshot_price * orignal_item.quantity * orignal_item.designer_order.discount.to_i/dos_total).round(2),
            name: item_name,
            quantity: orignal_item.quantity,
            weight: get_line_item_wise_weight(orignal_item.quantity).to_f,
            price: item_price,
            item_snapshot: orignal_item.snapshot_price,
            sku_id: (design.design_code.present? && design.design_code.length < 15) ? design.design_code : design.id.to_s,
            meis: line_item.elligible_for_meis?(courier_name),
            shipment_bucket_id: line_item.shipment_bucket_id
          }
          item_data[:selected] = 'selected' if automate_attempt || cargo_bucket
          all_shipment_item_details['item'][index.to_s] = HashWithIndifferentAccess.new(item_data)
          index += 1
        end
      end
    end
    all_shipment_item_details[:total_weight] = (actual_weight.to_f > volumetric_weight.to_f ? actual_weight : volumetric_weight).to_f.round(2).to_s
    all_shipment_item_details[:number] = number
    all_shipment_item_details[:packer_id] = account_id
    all_shipment_item_details[:ship_contents] = (content_des.presence || item_name)
    package_type = get_courier_package_type(courier_name, actual_weight)
    all_shipment_item_details[:packaging_type] = package_type if !package_type.nil?
    if (courier_name == 'dhl' || courier_name == 'ups')
      if length <= 0
        all_shipment_item_details[:shipment_depth] = '1'
      else
        all_shipment_item_details[:shipment_depth] = length.to_s
      end
      if breadth <= 0
        all_shipment_item_details[:shipment_width] = '1'
      else
        all_shipment_item_details[:shipment_width] = breadth.to_s
      end
      if height <= 0
        all_shipment_item_details[:shipment_height] = '1'
      else
        all_shipment_item_details[:shipment_height] = height.to_s
      end
    else
      all_shipment_item_details[:shipment_depth] = all_shipment_item_details[:shipment_width] = all_shipment_item_details[:shipment_height] = '0'
    end
    all_shipment_item_details[:use_cbs] = 'on' if automate_attempt
    all_shipment_item_details
  end

  def get_line_item_wise_weight(quantity = 1)
    single_item_weight = get_order_weight_per_line_item
    return (single_item_weight * quantity).to_f
  end

  def get_order_weight_per_line_item
    order_weight = (actual_weight.to_f > volumetric_weight.to_f ? actual_weight : volumetric_weight).to_f.round(2)
    line_items_count = 0
    line_items.preload(:designer_order).select{|li| line_items_count+=li.quantity if li.check_if_dispatchable || li.is_buyer_returned?}
    if line_items_count.nonzero?
      return (order_weight/line_items_count).to_f
    else
      return (order_weight/line_items.count).to_f
    end
  end

  def divide_order_in_shipment_buckets
    best_shipper_for_country = {'ARAMEX'=>{cities: ['Sri Lanka', 'Bangladesh', 'Saudi Arabia', 'UAE', 'Bahrain', 'Kuwait', 'Qatar', 'Hong Kong', 'Oman', 'Northern Mariana Islands'], limit: 25000}, 'ATLANTIC'=> {cities: ['United Kingdom', 'Canada', 'South Africa', 'Mauritius', 'Germany', 'Singapore', 'France', 'Malaysia', 'Netherlands', 'Italy', 'Denmark', 'Ireland', 'Sweden', 'Belgium', 'Spain', 'Portugal', 'Finland', 'Zambia', 'Kenya', 'Bulgaria'], limit: 50000} }.find{|courier, countries| countries[:cities].include?(self.country)}
    if best_shipper_for_country.present?
      shipment_limit = best_shipper_for_country[1][:limit]
      best_shipper_for_country = best_shipper_for_country[0].downcase
    else
      shipment_limit = CSB_LIMITS['DHL']
      best_shipper_for_country = 'dhl'
    end
    ActiveRecord::Associations::Preloader.new.preload(self, line_items: [:designer_order ,:addon_type_values, replaced_product: [:line_item_addons, :designer_order],  design: [:designer,:categories,:images,property_values: :property]]);nil
    shipment_data = self.get_shipment_details(best_shipper_for_country, nil, false, cargo_bucket: true)
    order_shipment = OrderShipmentCreate.new(self, best_shipper_for_country, shipment_data)
    order_shipment.set_selected_items
    order_shipment.set_order_invoice_details
    invoice_items = Hash.new { |hash, key| hash[key] = {items: [], total_price: 0} }
    shipment_data["item"].each do |index, item|
      Shipment.calculate_invoice_items([item], invoice_items, order_shipment.invoice_data, item[:name], order_shipment.shipping_charges, cargo_shipment: true)
    end
    invoice_items = invoice_items.sort_by{|k, i| -i[:total_price].to_f}.to_h
    if invoice_items.present? && invoice_items.first[1][:total_price] <= shipment_limit && invoice_items.values.last[:total_price] > 0 && (buckets = order_shipment.find_number_of_buckets(invoice_items, shipment_limit)).length > 1
      buckets.each do |bucket|
        if LineItem.where(id: bucket[:items]).where(shipment_bucket_id: nil).exists?
          LineItem.where(id: bucket[:items]).where(shipment_bucket_id: nil).update_all(shipment_bucket_id: ShipmentBucket.create(order_id: self.id).id)
        end
      end
    end
    unless is_pbr_order?
      process_date_rfd = ((process_date = self.rfd_process_date) ? process_date.lpst_date.to_date : nil)
      CreateSearchEntityJob.perform_async(self.class.to_s, self.id, 'RfdSearcherEntity', process_date_rfd)
    end
  end

  def is_pbr_order?
    line_items.any?{|l| l.item_details['fake_rack'] == 'PBR'}
  end

  def cargo_shipment_item(items,market_rate,selected_items,shipper_csb_limit)
    shipment_count = SystemConstant.get('DHL_SHIPMENT_COUNT')
    return if shipment_count['monthly_count'].to_i <= shipment_count['cargo_limit'].to_i
    items = items.group_by{|i| i [:name]}
    items = selected_items.values.flatten.map do |item|
      item_name = item[:name].gsub('-', ' ').camelize
      group_count = selected_items[item[:name]].count
      group_total = items[item_name][0][:total_price]
      {
        name: item_name,
        total: group_total/group_count
      }
    end
    total_item = items.length
    total_item.downto(1).each do |index|
      items.combination(index).find do |item|
        if (item.sum{|i| i[:total]} * market_rate) <= shipper_csb_limit
          @use_cbs = true
          return (unless total_item == item.length
            item.group_by{|i| i[:name]}.map do |k,v|
              v.count > 1 ? "#{k} x #{v.count}" : k
            end
          end)
        end
      end
    end
    @use_cbs=false;nil
  end

  def create_international_shipment(length = 0,breadth = 0,height = 0,account_id)
    if (best_shipper.try(:downcase) == 'dhl' || best_shipper.try(:downcase) == 'ups')
      order_shipment_data = get_courier_automation_related_details(nil, best_shipper.try(:downcase), account_id, true, length, breadth, height)
    else
      order_shipment_data = get_courier_automation_related_details(nil, best_shipper.try(:downcase), account_id, true)
    end
    if !order_shipment_data[:error]
      shipment_data = order_shipment_data[:shipment_data]
      if shipment_data[:item].present? && shipment_data[:total_weight].present?
        best_shipper_name = self.best_shipper
        if self.cod? && self.international? && COD_COUNTRY_SHIPPER[self.country]
          best_shipper_name = COD_COUNTRY_SHIPPER[self.country]
        end
        order_shipment_create = OrderShipmentCreate.new(self, best_shipper_name, shipment_data)
        automation_status = order_shipment_create.create_order_automation_shipment
        if automation_status.present? && automation_status[:error]
          self.add_tags_skip_callback('shipment_error')
          self.other_details["#{best_shipper.try(:downcase)}_status"] = automation_status[:message]
          self.skip_before_filter = true
          self.save(validate: false)
        end
      end
    end
  end

  def cancellable?
    cod? && confirmed_at? && %w(sane confirmed).include?(state) && check_not_picked_up
  end

  def returnable?
    ['cancel','cancel_complete','fraud','reject'].exclude? state
  end

  def save_order_cancel_reason(reason=nil, current_account=nil)
    self.cancel_reason = reason
    add_notes_without_callback(reason, 'order_cancel_reason', current_account)
    LineItem.where(id: line_items.select{|item| item.cancel_reason.blank? }.map(&:id)).update_all(cancel_reason: reason)

    if (DesignerOrder::CANCEL_ORDER_REASONS - DesignerOrder::NO_DESIGNER_EVENT_REASONS).include?(reason)
      SidekiqDelayGenericJob.perform_async("ScopeScoreEvent", nil, "apply_remove_cancel_event", {"#{self.class}": self.id})
      # ScopeScoreEvent.sidekiq_delay.apply_remove_cancel_event(self)
    end
  end

  def get_sane_line_items_count
    sane_items_count = 0
    designer_orders.to_a.each do |dso|
      if ['canceled', 'vendor_canceled'].exclude?(dso.state)
        sane_items_count += dso.line_items.to_a.count{|item| item.status.blank?}
      end
    end
    sane_items_count
  end

  def create_clickpost_tracking(shipment, invoice_items)
    if CLICKPOST_INTERNATIONAL_TRACK && self.international?
      SidekiqDelayClassSpecificGenericJob.set(queue: 'low').perform_async("ClickPostAutomation", "create_tracking_for_international", {}, {"#{shipment.class}": shipment.id}, invoice_items)
      #click_post = ClickPostAutomation.new(self)
      #click_post.sidekiq_delay(queue: 'low').create_tracking_for_international(shipment, invoice_items)
    end
  end

  def delete_system_order_keys
    self.other_details.delete('system_order_cancel')
    self.other_details.delete('valid_followup')
    self.skip_before_filter = true
    self.save
  end

  def can_good_data?
    if super
      return true if !MOVE_NE_ORDERS_TO_FOLLOWUP
      if is_red_zone_order? && !self.line_items.any?{|lt| (lt.categories.pluck(:name) & ESSENTIAL_CATEGORIES).present?}
        if self.can_move_non_essential_order?
          self.move_non_essential_order!
          self.add_notes_without_callback('Non Essential order in red zone', 'state_change')
        end
        return false
      end
      return true
    end
    return false
  end

  def is_red_zone_order?
    red_zone_pincodes = Pincode.get_red_zone_pincodes
    red_zone_pincodes.include? self.pincode.to_i
  end

  def document_upload_url(redirect_url=nil)
    cipher = OpenSSL::Cipher::AES.new(128, :CBC).encrypt
    cipher.key = Rails.application.config.url_secret_key
    iv = cipher.random_iv
    encrypted = cipher.update(self.number) + cipher.final
    url = "https://"+ ENV['MOBILE_DOMAIN_NEW'] + "/kyc_documents/new?order_number=#{(Base64.urlsafe_encode64(iv + encrypted))}"
    url += "&redirect_back_url=#{CGI.escape(redirect_url)}" if redirect_url.present?
    url
  end

  def national_id_required?
    country == 'South Africa' && user.present? && !user.kyc_documents.exists?(name: 'National ID')
  end

  def self.send_document_to_shipper(order_id,type)
    SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "send_kyc_document_to_shipper",{"#{self}": order_id},type)
  end

  private

  def get_courier_package_type(courier, weight)
    case courier
    when 'xpressbees_ups'
      'XPRESSBEES_UPS_PAK'
    when 'xindus'
      'XINDUS_PAK'
    when 'dhl'
      'DHL_PAK'
    when 'aramex'
      weight.to_i < 3.0 ? 'ARAMEX_PAK' : ''
    when 'delhivery'
      'DELHIVERY_PAK'
    when 'fedex'
      weight.to_f <= 2.5 ? 'FEDEX_PAK' : 'YOUR_PACKAGING'
    else
      nil
    end
  end

  def store_product_level_process_dates(items, sorted_processes, process_dates_hash, designer_id, order_level_process_details, to_be_import, confirmed_date)
    sorted_processes.each do |process|
      previous_epst_date = (process_dates_hash[:epst_date].presence || confirmed_date).to_date

      previous_lpst_date = (process_dates_hash[:lpst_date].presence || confirmed_date).to_date

      process_epst_days = (process.specification[designer_id].try(:[], 'epst_days').presence || process.epst_days).to_i

      process_lpst_days = (process.specification[designer_id].try(:[], 'lpst_days').presence || process.lpst_days).to_i
      process_dates_hash = {operation_process_id: process.id, epst_date: (previous_epst_date + process_epst_days.day), lpst_date: (previous_lpst_date + process_lpst_days.day)}

      items.each{|item| to_be_import << item.process_dates.new(process_dates_hash)}

      if process.process_type == 'order'
        ((order_level_process_details[process.id] ||= {})['epst_date'] ||= []) << process_dates_hash[:epst_date]
        ((order_level_process_details[process.id] ||= {})['lpst_date'] ||= []) << process_dates_hash[:lpst_date]
      end
    end
  end

  def store_order_level_process_dates(order_level_process_details, order, to_be_import)
    order_level_process_details.each do |process_id, data|
      to_be_import << order.process_dates.new({operation_process_id: process_id, epst_date: data['epst_date'].max, lpst_date: data['lpst_date'].max})
    end
    ProcessDate.import to_be_import, validate: false, on_duplicate_key_update: {conflict_target: [:id], columns: [:operation_process_id, :epst_date, :lpst_date]}
  end

  def update_coupon_used_on
    if coupon && coupon.limit == 1
      coupon.coupon_used_on = self
      coupon.save
    end
  end



  def client
    Paypal::Express::Request.new PAYPAL_CONFIG
  end

  def search_address_using_geocoder(address)
    Geocoder.search(address,params: {region: "in"})
  end

  # checks if still some payment status is in process after receiving the response from payment gateway
  def payment_status_under_process?
    case pay_type
    when PAYTM
      paytm_txn_status != nil && state == 'new'
    when PAYMENT_GATEWAY
      state == 'new' && self.attempted_payment_gateway == 'G2A' && g2a_txn_status == nil
    end
  end

  def get_name_of_stage(stage_name,color)
    order_of_stages = ['in_warehouse','quality_check','out_for_stitching','stitching_done','ready_to_ship','dispatched']
    if color == 'white'
      required_stages = order_of_stages.slice(order_of_stages.index(stage_name)..order_of_stages.length)
    else
      required_stages = order_of_stages.slice(0..order_of_stages.index(stage_name))
    end
    all_names = []
    required_stages.each do |name|
      all_names << "#{name.gsub('_',' ').titleize}-#{color}-#{name}"
    end
    all_names
  end

  def get_qc_fail_days(qc_failed_dates)
    if qc_failed_dates.length != 1
      qc_failed_dates.sort!
      qc_failed_dates.insert(0, qc_failed_dates.first - 3)
      days_overlap = qc_failed_dates.each_cons(2).map{|a,b| (b-a).abs.to_i}
      add_days = 0
      days_overlap.map{|i| add_days += i > 3 ? 3 : i}
      add_days
    else
      3
    end
  end

  def get_new_note(names)
    new_notes = ''
    notes_split = self.notes.split('...')
    notes_split.each do |note|
      name =  note.split(' : ')[1]
      new_notes << note + '...' if name.present? && names.include?(name)
    end
    new_notes
  end

  def get_qc_done_days(days_advanced, increase_by_days, line_items)
    qc_done_approximate_date =  self.confirmed_at.advance(days:  days_advanced  + increase_by_days)
    qc_done_by_date = line_items.collect(&:qc_done_on).compact.max
    if qc_done_by_date.present?
      add_days = (qc_done_by_date.to_date - qc_done_approximate_date.to_date).to_i
      add_days > 0 ? add_days : 0
    end
    0
  end

  #checks if cart was otp verified and adds error
  def otp_verified_cod_order?
    if !COD_OTP_DISABLED && !self.international?
      errors[:base] << 'OTP must be verified for COD orders'  if cod? && cart.try(:otp) != 'verified'
    end
  end

  def check_address_validation
    if self.new_record? && ['United States', 'Australia'].include?(country) && street.match(/P[\.\s]*O[\.\s]+Box/)
      errors[:base] << "Please enter a valid street address, we do not except P.O. Box addresses"
    end
  end

  def self.create_potential_order_csv(potential_orders)
    CSV.generate do |csv|
      csv << ['Number','No. Of Designer Orders', 'Items Count', 'Received Count' ,'Stitching Required Count', 'Stitching Done Count']
      potential_orders.each do |order|
        csv << order
      end
    end
  end

  def update_expected_dates
    ActiveRecord::Associations::Preloader.new.preload(self,line_items: :designer); nil
    delivery_time = delivery_days
    delivery_date = (self.confirmed_at || Time.current).advance(days: delivery_time)

    if (dns = self.delivery_nps_info).present?
      delivery_date = self.confirmed_at.advance(days: delivery_time)
      dns.update_attributes(revised_delivery_date: delivery_date, promised_delivery_date: delivery_date) if dns.promised_delivery_date.to_date != delivery_date.to_date
    elsif self.international? && self.delivery_nps_info.blank?
      DeliveryNpsInfo.create(promised_delivery_date: delivery_date,order_number: self.number,order_from: 'mirraw',order_to: 'user',element: self)
      update_designer_order_date('designer','mirraw')
    elsif (DeliveryNpsInfo.where(element_id: self.designer_orders.map(&:id)).where(element_type: 'DesignerOrder')).blank?
      DeliveryNpsInfo.create(promised_delivery_date: delivery_date,order_number: self.number,order_from: 'designer',order_to: 'user',element: self)
      update_designer_order_date('designer','user')
    end
  end

  def update_designer_order_date(from,to)
    all_entries = []
    ActiveRecord::Associations::Preloader.new.preload(self,designer_orders: [:designer, :designs]); nil
    self.designer_orders.each do |dos|
      designer_shipping_time = dos.get_vendor_delivery_days
      all_entries << DeliveryNpsInfo.new(promised_delivery_date: Time.current.advance(days: designer_shipping_time),order_number: self.number,order_from: from,order_to: to,element: dos)
    end
    DeliveryNpsInfo.import all_entries
  end

  ## Showing error message if cod is not available for COD option
  def can_do_cod?
    if self.cod? && self.international?
      return true
    elsif self.cod? && (self.new? || self.pending?)
      if (self.billing_pincode == self.pincode)
        errors.add(:order, 'Cash On delivery not available for ' +  self.pincode) unless self.cod_available?
      else
        errors.add(:order, 'Cash On delivery requires billing and shipping address to be same')
      end
    end
  end

  def get_stitching_counts
    line_items = self.line_items.sane_items.where(stitching_required: 'Y').preload(:designer_order,:design, line_item_addons: [addon_type_value: :addon_type_value_group])
    stitching_count = 0
    line_items.each do |item|
      stitching_count = stitching_count + item.quantity if ['canceled','vendor_canceled'].exclude?(item.designer_order.state) && ((item.line_item_addons.select{|ad| (['petticoat stitching','fall and pico'].exclude? ad.addon_type_value.name.try(:downcase)) && (["custom_stitching"].include?(ad.addon_type_value.try(:addon_type_value_group).try(:name)))}.count > 0))
    end
    measurements_count = self.line_items.sane_items.joins(:stitching_measurements).where('stitching_measurements.product_designable_type != ?','fnp').where('stitching_measurements.code is null').count
    return stitching_count,measurements_count
  end

  include Promotions::Order
  include TrikonApi
  include StreetAddressLineable
  include ScopeScoreEvent
  include Fullnameable.fullnameize(:billing_name, :name)
  include ForceDowncaseWriters.new(:email, :billing_email)

  include Wisper::Publisher
end

require 'line_item_calculations'
