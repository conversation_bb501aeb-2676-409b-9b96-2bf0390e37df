- item_total = @cart.items_total_without_addons(@conversion_rate)
- addon_charges = @cart.addons_total(@conversion_rate)
- grandtotal, taxes, shipping, wallet_discount, platform_fee = @cart.get_all_the_cart_details(@country_code, @actual_country , nil ,@conversion_rate, nil, session[:gift_wrapped], true)
- waved_off_platform_fee = @cart.get_platform_fee(@country_code, @conversion_rate)
.subscription-container{:data => {:subscription_plan_id => @subscription_plan.id}}
  .subscription-header
    %input{:type => "checkbox", :id => "subscription_enabled", :name => "enable_subscription", :checked => true}
    %label{:for => "subscription_enabled"}
    %h3 Mirraw Subscription
    %span.saved-label You Saved #{get_price_in_currency_with_symbol(waved_off_platform_fee, true)}
  .subscription-details
    %p
      Get platform fee waived off on this order and early access to exclusive previews.
      %br
      Discount applies to product value. Membership billed at checkout.
    .saved-amount #{get_price_in_currency_with_symbol(waved_off_platform_fee, true)}
  .breakdown-link
    %a.toggle-breakdown{:href => "javascript:void(0);"} View Breakdown
  .breakdown-details
    %table
      %tr
        %td Products
        %td.price #{get_price_in_currency_with_symbol(item_total, true)}
      %tr
        %td Discount applied due to Subscription
        %td.price.discount - #{get_price_in_currency_with_symbol(waved_off_platform_fee, true)}
      %tr
        %td Subscription Fee
        %td.price #{get_price_in_currency_with_symbol(@subscription_plan.price, true)}
      %tr
        %td Customisation
        %td.price #{get_price_in_currency_with_symbol(addon_charges, true)}
      %tr
        %td Shipping
        - shipping = 0
        %td.shipping_cost.price
          - if (shipping = @order_retry_shipping_cost).present?
            = shipping
          - elsif (shipping = @cart.shipping()) > 0
            #{get_price_in_currency_with_symbol(shipping)}
          - else
            FREE
      %tr
        %td Taxes
        %td.price #{get_price_in_currency_with_symbol(taxes, true)}
      %tr.final-payable
        %td Final Payable
        %td.price #{get_price_in_currency_with_symbol(grandtotal,true)}