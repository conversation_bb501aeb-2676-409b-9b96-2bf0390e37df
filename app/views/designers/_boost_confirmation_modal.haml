-# app/views/designers/_boost_modal.html.haml
- boost_config = @designer.designer_boost_config
.modal#boostModal
  .modal-content
    .modal-header
      %h2 Boost Confirmation
      %span.close &times;
    .modal-body
      %p.boost-info-header Please select your desired boost duration:

      %h2 Boost Start And End Date
      .boost-form-group#start_date_selection_group
        %label{for: "boost_start_date_input"} Start Date:
        %input#boost_start_date_input.boost-datepicker{type: "text", readonly: true, placeholder: "Select Start Date"}
        %span.error-message#boost_start_date_error_message

      .boost-form-group#end_date_selection_group
        %label End Date:
        .radio-group
          %label
            %input{type: "radio", name: "boost_end_date_type", value: "none", checked: true}
            None (Continuous Boost)
          %label
            %input{type: "radio", name: "boost_end_date_type", value: "fixed"}
            Select End Date
        %input#boost_end_date_input.boost-datepicker{type: "text", readonly: true, placeholder: "Select End Date", style: "display: none;"}
        %span.error-message#boost_end_date_error_message


      %p.boost-details
        By clicking the "Confirm and Boost" button, you acknowledge and agree to the following:
      %ul
        %li
          %strong Boost Fee:
          %span#boostFee A charge of Rs. #{boost_config.boost_fee} will be applied for each day the boost is active, reflected as a negative adjustment in your payout report.
        %li
          %strong Product Visibility:
          %span#productVisibility Your boosted product will be displayed on the first page of the relevant category page for the selected duration.
        %li
          %strong Boost Availability:
          %span#boostAvailability Limited slots are available per category per day, boosts follow a first-come, first-served basis. If slots are full for your selected dates, the boost cannot be scheduled. New boosts go live daily at 12:50 PM IST.
        %li
          %strong Boost Duration:
          %span#boostDuration Your boost will run from the selected start date and time (12:50 PM IST) until the selected end date (12:50 PM IST the following day for a 24-hour period), or continuously if no end date is specified.

    .modal-footer
      %button#confirmBoost.button.button-primary Confirm and Boost
      %button.button.button-secondary.close Cancel

= javascript_include_tag 'vendor_boost'