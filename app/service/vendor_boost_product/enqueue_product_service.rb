# app/services/vendor_boost_product/enqueue_product_service.rb
module VendorBoostProduct
  class EnqueueProductService
    # Modified initialize to accept boost_start_time and boost_end_time
    def initialize(design_id, boost_start_time, boost_end_time)
      @design = Design.find_by(id: design_id)
      return unless @design

      @designer = @design.designer
      @boost_config = @designer.designer_boost_config
      @category = @design.boostable_category # This will still find the base boostable category
      @boost_start_time = boost_start_time # Store for use in #call
      @boost_end_time = boost_end_time     # Store for use in #call (can be nil for continuous)
    end

    def call
      # Basic guard clause: ensure design and category are found.
      # The comprehensive eligibility check (including slot availability over date range)
      # is now primarily handled in the DesignersController via @design.eligible_for_boost_with_slot_check?.
      # We don't need to re-run the full `eligible_for_boost?` here.
      return false unless @design && @category && @boost_start_time.present?

      ActiveRecord::Base.transaction do
        boosted_design = BoostedDesign.create!(
          design: @design,
          designer: @designer,
          category_id: @category.id,
          boost_fee: @boost_config&.boost_fee || 0, # Use safe navigation & default 0 if config/fee is nil
          boost_start_time: @boost_start_time, # Set boost_start_time from constructor
          boost_end_time: @boost_end_time     # Set boost_end_time from constructor (nil if continuous)
        )
        # Assuming CreateAdjustmentService can handle the new boosted_design structure
        VendorBoostProduct::CreateAdjustmentService.new(boosted_design).call
      end
      true
    rescue ActiveRecord::RecordInvalid => e
      # Capture model validation errors
      message = "Product Boost Enqueue failed for design_id: #{@design.id} due to validation: #{e.message}"
      Rails.logger.error(message)
      # Optionally add errors to the design object if you want them propagated back to controller,
      # but in this flow, the controller already handles Design#errors.
      # @design.errors.add(:base, e.message) if @design
      ExceptionNotify.sidekiq_delay.notify_exceptions('BoostProductService failed', message, {})
      false
    rescue StandardError => e
      # This catch-all should ideally be more specific, but matches existing pattern
      # Removing destroy_all if `currently_boosted?` as that's not the goal on general error
      Rails.logger.error("Product Boost Enqueue failed: #{e.message}")
      ExceptionNotify.sidekiq_delay.notify_exceptions('BoostProductService failed', "#{e.message}", {})
      false
    end
  end
end