// app/assets/javascripts/vendor_boost.js
// IMPORTANT: This code relies on jQuery, Moment.js, and the specific daterangepicker library
// you provided, being loaded BEFORE this script.

var MR = MR || {};

MR = (function (window, document, Mirraw) {
  Mirraw.boostModal = {
    _startDatePickerInstance: null, // Holds the daterangepicker instance for start date
    _endDatePickerInstance: null,   // Holds the daterangepicker instance for end date
    _selectedDesignId: null,
    _selectedDesignerId: null,
    _minSelectableDate: null,       // Stores the min_selectable_date from backend (moment object)
    _unavailableDates: {},          // Stores dates for which slots are full { 'YYYY-MM-DD': true }

    init: function () {
      this.bindEvents();
    },

    bindEvents: function () {
      $(document).on("click", ".boost-button", this.showModal.bind(this));
      $(document).on("click", ".close", this.hideModal.bind(this));
      $(document).on("click", "#confirmBoost", this.confirmBoost.bind(this));

      // Handle change for boost end date type (None vs. Select End Date)
      $(document).on("change", 'input[name="boost_end_date_type"]', this.handleEndDateTypeChange.bind(this));

      // Listen for changes on Start Date picker to update End Date picker's minDate
      $('#boost_start_date_input').on('apply.daterangepicker', (ev, picker) => {
        this.clearErrorMessages();
        const selectedStartDate = picker.startDate;
        // Update the input field
        $('#boost_start_date_input').val(selectedStartDate.format('YYYY-MM-DD'));

        // Update minDate for End Date picker
        if (this._endDatePickerInstance) {
          // End date must be strictly after start date
          this._endDatePickerInstance.setOptions({ minDate: selectedStartDate.clone().add(1, 'day') });
          
          // If the current end date is before the new minDate, clear it
          if (this._endDatePickerInstance.startDate && this._endDatePickerInstance.startDate.isSameOrBefore(selectedStartDate, 'day')) {
             this._endDatePickerInstance.setStartDate(selectedStartDate.clone().add(1, 'day')); // Suggest next day
             this._endDatePickerInstance.setEndDate(selectedStartDate.clone().add(1, 'day'));
             $('#boost_end_date_input').val(this._endDatePickerInstance.startDate.format('YYYY-MM-DD'));
          }
          this._endDatePickerInstance.updateCalendars();
        }
      });
      
      // Listen for changes on End Date picker
      $('#boost_end_date_input').on('apply.daterangepicker', (ev, picker) => {
        this.clearErrorMessages();
        const selectedEndDate = picker.endDate;
        $('#boost_end_date_input').val(selectedEndDate.format('YYYY-MM-DD'));
      });

      // When calendar is shown, ensure availability is fetched for the displayed month
      // This listener covers both start and end date pickers
      $('#boost_start_date_input, #boost_end_date_input').on('show.daterangepicker', (ev, picker) => {
        // Get the month(s) currently visible in the calendar view
        const currentMonthMoment = picker.startDate;
        this.fetchAvailabilityForMonth(currentMonthMoment.year(), currentMonthMoment.month() + 1); // Month is 0-indexed in moment.js

        // If the daterangepicker displays two months (e.g., in range mode, though we use singleDatePicker:true),
        // it's good practice to fetch for the second visible month too.
        // For singleDatePicker mode, usually only one calendar month is shown.
        // However, if the picker auto-advances, this ensures the new month's data is there.
        if (picker.calendar && picker.calendar.length > 1) { // Check if multiple calendars are rendered
            const nextMonthMoment = picker.endDate; // For dual calendar view, endDate might represent the start of the second calendar
            if (nextMonthMoment.month() !== currentMonthMoment.month() || nextMonthMoment.year() !== currentMonthMoment.year()) {
                this.fetchAvailabilityForMonth(nextMonthMoment.year(), nextMonthMoment.month() + 1);
            }
        }
      });
    },

    showModal: function (event) {
      this._selectedDesignId = $(event.currentTarget).data("design-id");
      this._selectedDesignerId = $(event.currentTarget).data("designer-id");

      this.clearErrorMessages(); // Clear any previous errors

      // Set default to "None" for end date (continuous boost)
      $('input[name="boost_end_date_type"][value="none"]').prop('checked', true).trigger('change');
      
      // Fetch default boost dates and availability from backend
      this.fetchAndSetDefaultBoostDates(this._selectedDesignId, this._selectedDesignerId);
      
      $("#boostModal").show();
    },

    hideModal: function () {
      $("#boostModal").hide();
      this.clearErrorMessages(); // Clear errors when modal is hidden
      
      // Destroy/clear picker instances to ensure clean state on next open
      if (this._startDatePickerInstance) {
          this._startDatePickerInstance.remove();
          this._startDatePickerInstance = null;
      }
      if (this._endDatePickerInstance) {
          this._endDatePickerInstance.remove();
          this._endDatePickerInstance = null;
      }
      // Clear input fields
      $('#boost_start_date_input').val('');
      $('#boost_end_date_input').val('');
      this._unavailableDates = {}; // Clear availability data
      this._minSelectableDate = null;
    },

    // This function initializes the daterangepicker instance(s)
    initializeDatePickers: function (isEndDateEnabled) {
      const commonPickerOptions = {
        singleDatePicker: true, // Always true for individual date selection
        showDropdowns: true,
        timePicker: false,
        locale: {
          format: "YYYY-MM-DD",
          applyLabel: "Apply",
          cancelLabel: "Cancel",
          // Set From/To labels to empty strings as requested
          fromLabel: "", 
          toLabel: "",
          customRangeLabel: "Custom Range",
          weekLabel: "W",
          daysOfWeek: moment.weekdaysMin(),
          monthNames: moment.monthsShort(),
          firstDay: moment.localeData()._week.dow
        },
        // The isInvalidDate callback to disable unavailable dates
        isInvalidDate: (date) => {
          const formattedDate = date.format('YYYY-MM-DD');
          // Disable dates before minSelectableDate or if they are explicitly unavailable
          return date.isBefore(this._minSelectableDate, 'day') || this._unavailableDates[formattedDate] === true;
        },
        autoUpdateInput: false
      };

      // Initialize Start Date Picker
      if (this._startDatePickerInstance) {
        this._startDatePickerInstance.remove(); // Remove existing instance before re-initializing
      }
      this._startDatePickerInstance = $('#boost_start_date_input').daterangepicker({
        ...commonPickerOptions,
        minDate: this._minSelectableDate || moment().startOf('day'),
      }).data('daterangepicker');

      // Initialize End Date Picker
      if (this._endDatePickerInstance) {
        this._endDatePickerInstance.remove(); // Remove existing instance before re-initializing
      }

      if (isEndDateEnabled) {
        $('#boost_end_date_input').show(); // Show the input field
        this._endDatePickerInstance = $('#boost_end_date_input').daterangepicker({
          ...commonPickerOptions,
          // Min date for end picker is the selected start date + 1 day
          minDate: this._startDatePickerInstance && this._startDatePickerInstance.startDate.isValid() ? 
                     this._startDatePickerInstance.startDate.clone().add(1, 'day') : 
                     (this._minSelectableDate ? this._minSelectableDate.clone().add(1, 'day') : moment().add(1, 'day').startOf('day')),
          // End date picker's isInvalidDate should also ensure it's not before selected start date
          isInvalidDate: (date) => {
            const baseInvalid = commonPickerOptions.isInvalidDate(date);
            const isBeforeStartDate = this._startDatePickerInstance && this._startDatePickerInstance.startDate && date.isSameOrBefore(this._startDatePickerInstance.startDate, 'day');
            return baseInvalid || isBeforeStartDate;
          }
        }).data('daterangepicker');
      } else {
        // If end date is not enabled (continuous mode), hide input and clear its value/instance
        $('#boost_end_date_input').hide().val('');
        this._endDatePickerInstance = null; // Ensure no instance is held
      }
    },

    handleEndDateTypeChange: function (event) {
      const isNoneSelected = (event.target.value === "none");
      this.initializeDatePickers(!isNoneSelected); // Pass true to enable end date picker if 'fixed'
      this.clearErrorMessages(); // Clear any existing errors

      if (isNoneSelected) {
        // If 'None' selected, clear end date and its display
        $('#boost_end_date_input').val('');
      } else {
        // If 'Select End Date' selected, ensure it's visible and prompt for selection
        // No specific action needed here beyond initialization, user will pick
        // If a start date is already selected, try to pre-set a minDate for the end date picker
        if (this._startDatePickerInstance && this._startDatePickerInstance.startDate.isValid()) {
            this._endDatePickerInstance.setStartDate(this._startDatePickerInstance.startDate.clone().add(1, 'day'));
            this._endDatePickerInstance.setEndDate(this._startDatePickerInstance.startDate.clone().add(1, 'day'));
            $('#boost_end_date_input').val(this._endDatePickerInstance.startDate.format('YYYY-MM-DD'));
        }
      }
    },

    fetchAvailabilityForMonth: function(year, month) {
      // Only fetch if design/designer IDs are set and if we haven't fetched for this month already
      if (!this._selectedDesignId || !this._selectedDesignerId) {
          console.warn("Design/Designer ID not set, cannot fetch availability.");
          return;
      }

      const monthKey = `${year}-${month}`;
      // Basic cache check (can be improved, e.g., storing a timestamp)
      // If we have data for this month (even if it's empty), assume fetched.
      // This prevents repeated calls for the same month.
      if (this._unavailableDates && Object.keys(this._unavailableDates).some(dateStr => dateStr.startsWith(`${year}-${String(month).padStart(2, '0')}`))) {
          return;
      }
      
      MR.boostModal.showLoader();
      $.ajax({
        url: "/designers/" + this._selectedDesignerId + "/designs/" + this._selectedDesignId + "/boost_availability_info",
        type: "GET",
        data: { month: month, year: year }, // Send month and year to backend
        dataType: "json",
        success: (response) => {
          MR.boostModal.hideLoader();
          if (response.success) {
            // Merge newly fetched unavailable dates into our main cache
            // Keys in response.unavailable_dates_map are 'YYYY-MM-DD'
            Object.assign(this._unavailableDates, response.unavailable_dates_map);

            // Re-render calendars to reflect new availability
            if (this._startDatePickerInstance) {
              this._startDatePickerInstance.updateCalendars();
            }
            if (this._endDatePickerInstance) {
              this._endDatePickerInstance.updateCalendars();
            }
          } else {
            alert("Error fetching monthly availability: " + (Array.isArray(response.errors) ? response.errors.join(", ") : response.errors));
          }
        },
        error: (xhr, status, error) => {
          MR.boostModal.hideLoader();
          const response = xhr.responseJSON;
          console.error("AJAX Error fetching monthly availability:", response || error);
        }
      });
    },

    fetchAndSetDefaultBoostDates: function (designId, designerId) {
      MR.boostModal.showLoader();
      $.ajax({
        url: "/designers/" + designerId + "/designs/" + designId + "/boost_availability_info",
        type: "GET",
        dataType: "json",
        success: (response) => {
          MR.boostModal.hideLoader();
          if (response.success) {
            this._minSelectableDate = response.min_selectable_date ? moment(response.min_selectable_date) : moment().startOf('day');
            
            // Store initial batch of unavailable dates
            this._unavailableDates = response.unavailable_dates_map || {};

            // Re-initialize pickers with updated min dates and isInvalidDate callback
            const isNoneSelected = $('input[name="boost_end_date_type"]:checked').val() === "none";
            this.initializeDatePickers(!isNoneSelected);

            if (response.default_start_date) {
              const defaultStartDateMoment = moment(response.default_start_date);
              if (this._startDatePickerInstance) {
                this._startDatePickerInstance.setStartDate(defaultStartDateMoment);
                this._startDatePickerInstance.setEndDate(defaultStartDateMoment); // For single selection
                $('#boost_start_date_input').val(defaultStartDateMoment.format('YYYY-MM-DD'));
              }
            } else if (response.errors && response.errors.length > 0) {
              alert("Info: " + response.errors.join(", ") + "\nPlease select a suitable date manually.");
            } else {
              alert("Info: Could not determine an optimal default start date. Please select manually.");
            }
            
            if (response.boost_fee !== undefined) {
              $('#boostFee').text(`A charge of Rs. ${response.boost_fee} will be applied for each day the boost is active, reflected as a negative adjustment in your payout report.`);
            }
          } else {
            alert("Error: " + (Array.isArray(response.errors) ? response.errors.join(", ") : response.errors));
            this.hideModal();
          }
        },
        error: (xhr, status, error) => {
          MR.boostModal.hideLoader();
          this.hideModal();
          const response = xhr.responseJSON;
          alert("Error fetching boost availability: " + (response && response.errors ? (Array.isArray(response.errors) ? response.errors.join(", ") : response.errors) : error));
        }
      });
    },

    confirmBoost: function () {
      this.clearErrorMessages();

      const startDate = this._startDatePickerInstance ? this._startDatePickerInstance.startDate : null;
      const isContinuous = $('input[name="boost_end_date_type"]:checked').val() === "none";
      let endDate = null;

      if (!startDate || !startDate.isValid()) {
        this.displayErrorMessage('boost_start_date', 'Please select a valid start date.');
        return;
      }

      // Check if start date itself is unavailable
      if (this._unavailableDates[startDate.format('YYYY-MM-DD')]) {
          this.displayErrorMessage('boost_start_date', 'The selected start date is not available.');
          return;
      }

      if (!isContinuous) {
          endDate = this._endDatePickerInstance ? this._endDatePickerInstance.endDate : null;
          if (!endDate || !endDate.isValid()) {
              this.displayErrorMessage('boost_end_date', 'Please select a valid end date.');
              return;
          }
          if (endDate.isSameOrBefore(startDate, 'day')) {
              this.displayErrorMessage('boost_end_date', 'End date must be after start date.');
              return;
          }
          // Check if any date in the range (inclusive) is unavailable
          let currentDate = startDate.clone();
          while (currentDate.isSameOrBefore(endDate, 'day')) {
              if (this._unavailableDates[currentDate.format('YYYY-MM-DD')]) {
                  this.displayErrorMessage('boost_end_date', `Date ${currentDate.format('YYYY-MM-DD')} in the selected range is not available.`);
                  return;
              }
              currentDate.add(1, 'day');
          }
      }

      MR.boostModal.showLoader();
      
      $.ajax({
        url: "/designers/" + this._selectedDesignerId + "/boost_product",
        type: "POST",
        data: {
          design_id: this._selectedDesignId,
          boost_start_time: startDate.format('YYYY-MM-DD'),
          boost_end_time: isContinuous ? null : endDate.format('YYYY-MM-DD')
        },
        dataType: "json",
        success: (response) => {
          MR.boostModal.hideLoader();
          this.hideModal();
          if (response.success) {
            alert(response.message);
            location.reload();
          } else {
            alert("Error: " + (Array.isArray(response.errors) ? response.errors.join(", ") : response.errors));
          }
        },
        error: (xhr, status, error) => {
          MR.boostModal.hideLoader();
          const response = xhr.responseJSON;
          let errorMessage = "An unexpected error occurred: " + error;
          if (response && response.errors) {
            if (Array.isArray(response.errors)) {
              errorMessage = "Error: " + response.errors.join(", ");
            } else if (typeof response.errors === "object") {
              const messages = [];
              for (const key in response.errors) {
                if (Array.isArray(response.errors[key])) {
                  messages.push(...response.errors[key]);
                } else {
                  messages.push(response.errors[key]);
                }
              }
              errorMessage = "Error: " + messages.join(", ");
            } else {
              errorMessage = "Error: " + response.errors;
            }
          }
          alert(errorMessage);
        }        
      });
    },

    showLoader: function () {
      $("#pageLoader").fadeIn();
    },

    hideLoader: function () {
      $("#pageLoader").hide();
    },

    displayErrorMessage: function(field, message) {
      // Use specific error message IDs for start and end date inputs
      $(`#${field}_error_message`).text(message).show();
    },

    clearErrorMessages: function() {
      $('#boost_start_date_error_message').text("").hide();
      $('#boost_end_date_error_message').text("").hide();
    }
  };

  $(document).ready(function () {
    MR.boostModal.init();
  });

  return Mirraw;
})(this, this.document, MR);