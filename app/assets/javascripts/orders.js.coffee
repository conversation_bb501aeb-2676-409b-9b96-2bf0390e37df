//= require fuse.min
//= require jquery.validate.min.js
//= require seamless_account.js
# Place all the behaviors and hooks related to the matching controller here.
# All this logic will automatically be available in application.js.
# You can use CoffeeScript in this file: http://jashkenas.github.com/coffee-script/

currency_symbol = $("#grand_total").text().split(" ")[0]

# jQuery plugin to prevent double submission of forms
jQuery.fn.preventDoubleSubmission = ->
  $(this).on "submit", (e) ->
    $form = $(this)
    if $form.attr("submitted")
      e.preventDefault()
    else
      # Mark it so that the next submit can be ignored
      if $form.valid()
        $form.attr "submitted", true 
    return

  # Keep chainability
  this

countryCode = undefined
zipRegex = undefined
stateListFuse = undefined

$ ->
  $("form").preventDoubleSubmission()

$ ->
  if !$('#phone-code1').length  > 0
    $('#order_billing_phone').attr('style', 'width: 60% !important');
  if $("#order_billing_country").val().length > 0
    appendPhoneCode("order_billing_country", 'phone-code1')
  if $("#order_country").val().length > 0
    appendPhoneCode("order_country", 'phone-code2')

$ ->
  $("#new_order").validate
    rules:
      "order[billing_email]":
        required:
          depends: ->
            $(this).val $.trim($(this).val())
            true
        email: true
      "order[billing_first_name]":
        required: true
        minlength: 2
      "order[billing_last_name]":
        required: true
        minlength: 2
      "order[billing_phone]":
        required: true
        digits: true
        minlength: () ->
          getPhoneNoMinDigitCount("billing_phone_number")
        maxlength: () ->
          getPhoneNoMaxDigitCount("billing_phone_number")
      "order[billing_country]":
        required: true
      "order[billing_pincode]":
        required: true
        minlength: () ->
          if $("#order_billing_country").val() == 'United States'
            5
        maxlength: () ->
          if $("#order_billing_country").val() == 'United States'
            5

      "order[billing_street_line_1]":
        required: true
      "order[billing_street_line_2]":
        required: true
      "order[billing_street]":
        required: true
      "order[billing_city]":
        required: true
      "order[billing_state]":
        required: true
      'password':
        required: true
        minlength: 6
      "order[first_name]":
        required: '#ship_to_same_address:unchecked'
      "order[last_name]":
        required: '#ship_to_same_address:unchecked'
      "order[phone]":
        required: '#ship_to_same_address:unchecked'
        digits: true
        minlength: () ->
          getPhoneNoMinDigitCount("shipping_phone_number")
        maxlength: () ->
          getPhoneNoMaxDigitCount("shipping_phone_number")
      "order[country]":
        required: '#ship_to_same_address:unchecked'
      "order[pincode]":
        required: '#ship_to_same_address:unchecked'
        minlength: () ->
          if $("#order_country").val() == 'United States'
            5
        maxlength: () ->
          if $("#order_country").val() == 'United States'
            5
      "order[street_line_1]":
        required: '#ship_to_same_address:unchecked'
      "order[street_line_2]":
        required: '#ship_to_same_address:unchecked'
      "order[street]":
        required: '#ship_to_same_address:unchecked'
      "order[city]":
        required: '#ship_to_same_address:unchecked'
      "order[buyer_state]":
        required: '#ship_to_same_address:unchecked'
    messages:
      'password':
        required: 'Please provide a password.'
        minlength: 'Password length should be greater than 6.'
    submitHandler: (form) ->
      if($('#codModal').length) && $('#order_pay_type_cash_on_delivery').is(":checked")
        if ($('#cod-confirm-wo-otp').length)
          $('#cod-confirmation-message').css('color', 'white')
          $('#codModal').modal()
          $('#cod-confirm-wo-otp').off('click').on 'click', ->
            $('#codModal').modal('hide')
            form.submit()
        else
          handleCodWithOtp(form)
      else
        form.submit()
        $('#green_button_box').find('input[type=submit]').attr('disabled', true).css({'opacity' : '0.5', 'background-image' : 'none'}).val('Please Wait ...')

handleCodWithOtp = (form) ->
  $('#otp-error-msg').hide()
  $('#cod-otp').val('')
  phoneNo = $('#order_billing_phone').val()
  $.ajax(generateOtp(phoneNo, false))
  $('#codModal').modal()
  $('#otp-form').off('ajax:success').on 'ajax:success', (evt,data) ->
    if(data['verified'] == true)
      $('#codModal').modal('hide')
      $('#green_button_box').find('input[type=submit]').attr('disabled', true).css('opacity', 0.5)
      form.submit()
    else
      $('#otp-error-msg').show()

getPhoneNoMinDigitCount = (phone_number) ->
  country = set_country(phone_number)
  switch country
    when 'United States' then digitMinCount = 10
    when 'India' then digitMinCount = 10
    when 'UAE' then digitMinCount = 9
    else digitMinCount = 8
  digitMinCount

getPhoneNoMaxDigitCount = (phone_number) ->
  country = set_country(phone_number)
  switch country
    when 'United States' then digitMaxCount = 10
    when 'India' then setMaxCount(phone_number)
    when 'UAE' then digitMaxCount = 9
    else digitMaxCount = setMaxCount(phone_number)
  digitMaxCount

setMaxCount = (phone_number) ->
  country = set_country(phone_number)
  if country != "India"
    digitMaxCount = 20
  else
    if gon.max_phone_length
      digitMaxCount = gon.max_phone_length
    else
      digitMaxCount = 10
  digitMaxCount

set_country = (phone_number) ->
  country = $("#order_country").val()
  if phone_number == "billing_phone_number"
    country = $("#order_billing_country").val()
  country

generateOtp = (phoneNo, resend) ->
  type: 'POST'
  data:
    phone:phoneNo
    resend:resend
  url: '/carts/generate_otp'

$ ->
  $('#cod-otp').on 'keydown', ->
    $('#otp-error-msg').hide()
  $('#resend-otp').on 'click', ->
    $(this).attr('disabled', true).css('opacity', 0.5)
    setTimeout ->
      $('#resend-otp').attr('disabled', false).css('opacity', 1)
    ,10000
    $.ajax(generateOtp($('#order_billing_phone').val(), true))

shippingCountry = () ->
  country = (if ($("#order_country").is(":visible")) then $("#order_country").val() else $("#order_billing_country").val())
  $("#no_india_shipping").removeClass().text ""
  if country is 'India' && $('#order_shipping_restrict').val() is 'india'
    $('#order_pay_type_cash_on_delivery').attr('disabled', true)
    $('#cod_status_always').removeClass().text ""
    $('#order_pay_type_cash_before_delivery').attr('disabled', true)
    $('#cod1_status_always').removeClass().text ""
    $('input[name="commit"]').attr 'disabled','true'
    $("#no_india_shipping").removeClass().addClass("alert alert-danger").text "Order cannot be shipped to India."
  else if gon.rakhi_pre_order isnt true
    $('input[name="commit"]').removeAttr 'disabled'

displayCustomsChargeMessage = (country) ->
  customsCharge = gon.custom_duty_country['country'][country.toLowerCase()]
  if (typeof customsCharge != 'undefined')
    if (gon.total < customsCharge)
      $('#customs_charge_message').html(gon.custom_duty_country['message']['no_duty'])
    else
      $('#customs_charge_message').html(gon.custom_duty_country['message']['duty'])
  else
    $('#customs_charge_message').html('')

appendPhoneCode = (selectName, inputName) ->
  country = $('#'+selectName).val()
  $.ajax
    type: 'GET'
    data:
      country: country
    url: '/country/get_state_dial_code'
    datatype: 'JSON'
    success: (data, status, jqhxr) ->
      $('#' +inputName).val('+' + data.dial_code)
      if country == "India"
        $('#' +inputName).attr('readonly', true)
      else
        $('#' +inputName).attr('readonly', false)


rakhiPreOrder = () ->
  country = (if ($("#order_country").is(":visible")) then $("#order_country").val() else $("#order_billing_country").val())

  if typeof gon isnt "undefined" && gon.rakhi_pre_order
    $("#rakhi_pre_order_message").removeClass().text ""

    if gon.rakhi_all_schedule
      $("#rakhi_pre_order_message").removeClass().addClass("alert alert-success").text "Your order will be delivered #{gon.rakhi_pre_order_date}."
      $(".delivery_time").hide()
      return false

    else if gon.rakhi_with_other_designs
      if country == 'India'
        $("#rakhi_pre_order_message").removeClass().addClass("alert alert-info").text "Your order has multiple items, only Rakhi will be delivered #{gon.rakhi_pre_order_date}."
        $(".delivery_time").text (Math.round(gon.indian_shipping_time) + ' days')

      else
        $("#rakhi_pre_order_message").removeClass().addClass("alert alert-success").text "Your order will be delivered #{gon.rakhi_pre_order_date}."
        $(".delivery_time").hide()
      return false

timer = null

updateTotalNShipping = (country_field) ->
  gon.wallet_discounts = gon.wallet_discounts || 0
  gon.remaining_wallet_credit = gon.remaining_wallet_credit || 0
  shipToSameAddress = $('#ship_to_same_address')
  cart_id = $('.cart_mini_info #cart_final_id').val()
  total = parseFloat(gon.total)
  total += parseFloat(gon.gift_wrap_price)
  country = $(country_field).val()
  updateExpressShipping(country)
  express_checked = $('#delivery_type_express').is(':checked')
  wallet_discounts = parseFloat(gon.wallet_discounts)
  if country.toLowerCase() == 'india'
    city = (if shipToSameAddress.is(':checked') then $('#order_billing_city').val() else $('#order_city').val())
    drop_pincode = (if shipToSameAddress.is(':checked') then $('#order_billing_pincode').val() else $('#order_pincode').val())
    delivery_days = updateDeliveryTime(country, city, false, drop_pincode)
    shipping_time = delivery_days + ' days'
  else
    shipping_time = Math.round(gon.shipping_time + gon.country_wise_shipping_time[country.toLowerCase()]) + ' days'
  $("#order_wallet_discount").html wallet_discounts.toFixed(2)
  $('#wallet_location').attr('value', country)
  if express_checked
    $('#wallet_payment_option').hide();
    $('#wallet_cant_be_used_for_express_delievery').show();
  else
    $('#wallet_payment_option').show();
    $('#wallet_cant_be_used_for_express_delievery').hide();
  if $('return_checkbox').innerHTML == 'Cancel' && (typeof gon.express_delivery_time != 'undefined')
    document.getElementById('express_shipping').style.display = 'none'
    $('#express_delievery_cannot_be_applied_for_wallet').show();
  if $('#order_pay_type_cash_on_delivery').is(':checked')
    $('.shipping-show').hide()
    shipToSameAddress.prop('checked', true)
    shipToSameAddress.attr('disabled', true)
  if $('#free_shipping_country').val().length > 0 && $('#free_shipping_country').val().indexOf($(country_field).val()) isnt -1
    if typeof gon isnt "undefined"
      # $(".shipping_label").text("Shipping To " + $(country_field).val())
      if gon.international_shipping_cost > 0
        $(".shipping_cost").text gon.international_shipping_cost_currency
        total += gon.international_shipping_cost
        $(".delivery_time").text shipping_time
        $(".cod").hide()
      if express_checked
        updateShippingAndDeliveryTime()
        total += gon.express_delivery_charge
      else
        $(".delivery_time").text shipping_time
    $('.promotion_text').show()
  else if country != 'India'
    if typeof gon isnt "undefined"
      if express_checked
        updateShippingAndDeliveryTime()
        total += gon.express_delivery_charge
      else
        $(".delivery_time").text (shipping_time)
  else
    $('.promotion_text').hide()
    # $(".shipping_label").text("Shipping To " + $(country_field).val
    if country == 'India'
      if gon.domestic_shipping_cost > 0
        total += gon.domestic_shipping_cost
        $(".shipping_cost").html currency_symbol + " " + gon.domestic_shipping_cost
      else
        $(".shipping_cost").text('FREE')
    $("#grand_total").html currency_symbol + " " + total
    $('#amount_to_be_paid').html total
    if $('#order_pay_type_cash_on_delivery').is(':checked')
      shipToSameAddress.prop('checked', true)
      shipToSameAddress.attr('disabled', true)
      if $("#order_billing_pincode").val().length
        $('#wallet_payment_option').hide();
        $('#wallet_cant_be_used_for_cod').show();
        wallet_discounts = parseFloat(gon.wallet_discounts)
        total = total + wallet_discounts
        $("#grand_total").html currency_symbol + " " + total.toFixed(2)
        $('#amount_to_be_paid').html total.toFixed(2)
        $("#order_wallet_discount").html 0.toFixed(2)

    else
      $(".cod").hide()
      shipToSameAddress.attr('disabled', false)
    $(".delivery_time").text (shipping_time)

  if gon.rts_delivery_time != undefined && country != 'India'
    $(".delivery_time").text (gon.rts_delivery_time.concat(' days'))
    $('#delivery_message_regular').text('Get your order delivered within ' + gon.rts_delivery_time + ' days')

  subscription_applied = $('#subscription_enabled').is(':checked')
  $.ajax
    type: 'GET'
    data:
      cart_id: cart_id
      country: country
      subscription_applied: subscription_applied
    url: '/api/check_free_shipping'
    datatype: 'JSON'
    success: (data, status, jqhxr) ->
      $('#express_delivery_price').text data.express_shipping_with_currency
      if country isnt 'India'
        if data.shipping_text != false && data.available #&& ($('#free_shipping_country').val().length == 0 || $('#free_shipping_country').val().indexOf($(country_field).val()) isnt -1)
          $(".shipping_cost").text 'FREE'
          $('#regular_shipping_cost').text 'FREE'
          $('.promotion_text').show()
          $('.free_shipment_text').html(data.shipping_text)
          
          value = parseFloat(data.grandtotal)
          # value += parseFloat(gon.gift_wrap_price)
          if express_checked
            value += data.express_shipping_charge
            $('.shipping_cost').text data.express_shipping_with_currency
          $("#grand_total").html currency_symbol + " " + value.toFixed(2)
          $('#amount_to_be_paid').html value.toFixed(2)
        else if  data.shipping_text != false && data.available == false #&& ($('#free_shipping_country').val().length == 0 || $('#free_shipping_country').val().indexOf($(country_field).val()) isnt -1)
          $('.promotion_text').show()
          $('.free_shipment_text').html(data.shipping_text)
          $(".shipping_cost").text data.shipping_with_currency
          $('#regular_shipping_cost').text data.shipping_with_currency
          value = parseFloat(data.grand_total)
          if express_checked
            value += data.express_shipping_charge
            $('.shipping_cost').text data.express_shipping_with_currency
          total_value = value - parseFloat(gon.remaining_wallet_credit)
          if total_value < 0
            wallet_discounts = parseFloat(gon.wallet_discounts) + parseFloat(data.shipping)
            $("#grand_total").html currency_symbol + " " + parseFloat(gon.gift_wrap_price).toFixed(2)
            $('#amount_to_be_paid').html parseFloat(gon.gift_wrap_price).toFixed(2)
            $("#order_wallet_discount").html wallet_discounts.toFixed(2)
          else
            wallet_discounts = parseFloat(gon.wallet_discounts) + parseFloat(gon.remaining_wallet_credit)
            $("#grand_total").html currency_symbol + " " + (total_value + parseFloat(gon.gift_wrap_price)).toFixed(2)
            $('#amount_to_be_paid').html (total_value + parseFloat(gon.gift_wrap_price)).toFixed(2)
            $("#order_wallet_discount").html wallet_discounts.toFixed(2)
        else
          $('.shipping_cost').text data.shipping_with_currency
          $('#regular_shipping_cost').text data.shipping_with_currency
          value = parseFloat(data.grand_total)
          if express_checked
            value += data.express_shipping_charge
            $('.shipping_cost').text data.express_shipping_with_currency
          total_value = value - parseFloat(gon.remaining_wallet_credit)
          if total_value < 0
            wallet_discounts = parseFloat(gon.wallet_discounts) + parseFloat(data.shipping)
            $("#grand_total").html currency_symbol + " " + parseFloat(gon.gift_wrap_price).toFixed(2)
            $('#amount_to_be_paid').html parseFloat(gon.gift_wrap_price).toFixed(2)
            $("#order_wallet_discount").html wallet_discounts.toFixed(2)
          else
            wallet_discounts = parseFloat(gon.wallet_discounts) + parseFloat(gon.remaining_wallet_credit)
            $("#grand_total").html currency_symbol + " " + (total_value + parseFloat(gon.gift_wrap_price)).toFixed(2)
            $('#amount_to_be_paid').html (total_value + parseFloat(gon.gift_wrap_price)).toFixed(2)
            $("#order_wallet_discount").html wallet_discounts.toFixed(2)
          $('.promotion_text').hide()
      else
        if express_checked
          value = parseFloat(gon.total) + data.express_shipping_charge
          $('.shipping_cost').text data.express_shipping_with_currency
          value += parseFloat(gon.gift_wrap_price)
          $("#grand_total").html currency_symbol + " " + value.toFixed(2)
          $('#amount_to_be_paid').html value.toFixed(2)    
      updateCartDetails(data)
      updatePaypal()
      if gon.paylater_message
        updatepaylaterMessage()        
  shippingCountry()

updateCartDetails = (data) ->
  $("#grand_total").html currency_symbol + " " + parseFloat(data.grandtotal).toFixed(2)
  $('#amount_to_be_paid').html parseFloat(data.grandtotal).toFixed(2)
  $('.platform_fee').text currency_symbol + " " + parseFloat(data.platform_fee).toFixed(2)
  $('.total_tax').text currency_symbol + " " + parseFloat(data.taxes).toFixed(2) 
  $("#order_wallet_discount").html parseFloat(data.wallet_discount).toFixed(2)
  if $('#order_pay_type_cash_on_delivery').is(':checked')
    total = data.grandtotal + data.wallet_discount
    $("#grand_total").html currency_symbol + " " + parseFloat(total).toFixed(2)
    $("#order_wallet_discount").html 0.toFixed(2)
    cart_id = $('.cart_mini_info #cart_final_id').val()
    $.get '/api/get_cod_charge', {cart_id: cart_id, pincode: $("#order_billing_pincode").val()}, 
      (response) ->
        if response.cod_charge > 0
          $(".cod").show()
          $('.shipping-show').hide()
          total += parseFloat(response.cod_charge)
          $("#grand_total").html currency_symbol + " " + total.toFixed(2)
          $('#amount_to_be_paid').html total.toFixed(2)
    
updateTaxValue = () ->
  if gon.tax_rate
    grand_total = parseFloat($('#amount_to_be_paid').text())
    tax_amount = grand_total * parseFloat(gon.tax_rate)
    $('#order_tax_amount').html (tax_amount).toFixed(2) 
    $('#amount_to_be_paid').html (grand_total + tax_amount).toFixed(2)
    $("#grand_total").html  currency_symbol + " " + (grand_total + tax_amount).toFixed(2)
  shippingCountry()

updatePaypal = () ->
  grand_total = parseFloat($('#amount_to_be_paid').text())
  wallet_discount = parseFloat($('#order_wallet_discount').text())
  if wallet_discount > 0 && grand_total <= 0
    $('#paypal-button-container').css('display', 'none')
    $('#pp-message').css('display', 'none')
    $('#green_button_box').css('display', '')
    $('#order_pay_type_mirraw_wallet').prop("checked", true)

updatepaylaterMessage = () ->
    amount = $('#amount_to_be_paid').text()
    element = $("#pp-message")
    element.attr("data-pp-amount", amount)



internationalMinVal = () ->
  country = (if ($("#order_country").is(":visible")) then $("#order_country").val() else $("#order_billing_country").val())
  cart_id = $('.cart_mini_info #cart_final_id').val()
  url = "/api/international_min_cart_value"
  data = {cart_id: cart_id}
  $("#international_min_val").removeClass().text ""
  $('input[name="commit"]').removeAttr 'disabled'
  if country isnt 'India'
    $.post url, data, (response) ->
      if response.error isnt false
        $("#international_min_val").removeClass().addClass("alert alert-danger").text "Minimum Order Value For International Orders is "+response.min_val+". Your current Items Total is "+$('#items_total_price').data('discounted-item-total')
        $('input[name="commit"]').attr 'disabled','true'
        $('#paypal-button').hide()
        $('#paypal-card-button').hide()
        $('#green_button_box').show()

rakhiPreOrder()
shippingCountry()
internationalMinVal()

updateForm = () ->
  if $('#ship_to_same_address').is(":checked")
    $('.cod_error').show()
    $('.shipping-show').hide()
    $('.designer_unshippable').css('border','solid 2px #03A9F4')
    updateTotalNShipping($('#order_billing_country'))
  else
    $('.cod_error').hide()
    $('.shipping-show').show()
    $('.designer_unshippable').css('border','')
    updateTotalNShipping($('#order_country'))


updateDeliveryTime = (country, city, show_delivery=false, drop_pincode='') ->
  if country == 'India'
    if city.length > 0 && (currency_symbol.toLowerCase() == 'inr' || currency_symbol.toLowerCase() == 'rs')
      if gon.can_use_cp_pdd && drop_pincode.length == 6 && /^[1-9]\d{5}$/.test(drop_pincode)
        fetchPddFromClickPost(gon.pickup_pincodes, drop_pincode)
        if gon.cp_eta > 0
          return (gon.max_vendor_eta + gon.cp_eta)
        else
          city_eta = fetchPddFromCities(city)
          if show_delivery
            $('.delivery_time').text(city_eta + ' days')
          else
            return (city_eta)
      else if gon.can_exclude_designers
        city_eta = fetchPddFromCities(city)
        if show_delivery
          $('.delivery_time').text(city_eta + ' days')
        else
          return (city_eta)
      else
        return gon.indian_shipping_time
    else
      return gon.indian_shipping_time

fetchPddFromCities = (city, show_delivery) ->
  all_cities = gon.vendor_pickup_locations.slice()
  if !all_cities.includes(city.toLowerCase())
    all_cities.push(city.toLowerCase())
  if gon.lane_functionality && gon.vendor_pickup_locations
    fetchPddFromLane(city, gon.vendor_pickup_locations)
  city_pdd = (if gon.metro_cities.filter((x)-> all_cities.includes(x)).length == all_cities.length then gon.city_based_ship_time['metro'] else gon.city_based_ship_time['non_metro'])
  return (gon.max_vendor_eta + city_pdd)

fetchPddFromClickPost = (p_pincodes ,drop_pincode) ->
  $.ajax '/api/fetch_pdd_from_cp',
  data :
    p_pincodes: p_pincodes
    d_pincodes: drop_pincode
  success: (data) ->
    gon.cp_eta = data.eta

fetchPddFromLane = (to_location, from_location) ->
  url = '/api/fetch_pdd_from_lane'
  data = {to_location: to_location, from_location: from_location}
  $.ajax url,
  data : data
  success: (response) ->
    if response.eta > 0
      $('.delivery_time').text((gon.max_vendor_eta + response.eta)  + ' days')

fetchPayTypeCookie = () ->
  if Cookies.get 'prefered_pay_type'
    selected_payment_option = Cookies.get 'prefered_pay_type'
    $('#'+selected_payment_option).prop 'checked', 'checked'

updateOtherPaymentOptions = () ->
  fetchPayTypeCookie()
  credit_card_radio_button = '#order_pay_type_creditdebit_cardnet_banking'
  paypal_radio_button = '#order_pay_type_paypal'
  current_selected_opt = $('input[name="order[pay_type]"]:checked')
  if current_selected_opt.is(':disabled') || current_selected_opt.length == 0
    if $('#order_billing_country').val() is 'India'
      $(credit_card_radio_button).attr('checked', 'checked')
      pay_type_info = $(credit_card_radio_button).attr('data-info')
    else
      $(credit_card_radio_button).attr('checked', 'checked')
      pay_type_info = $(credit_card_radio_button).attr('data-info')
  else
    if $('#order_billing_country').val() is 'India'
      if current_selected_opt.val() == 'PayUmoney'
        $('#payment_gateway_logo').show()
        $('#payment_gateway_logo').attr 'src', '/assets/payumoney.png'
      if current_selected_opt.val() == 'Credit/Debit Card/Net Banking'
        $('#payment_gateway_logo').show()
        #$('#payment_gateway_logo').attr 'src', '/assets/payu.png'
        $('#razorpay_gateway_logo').show()
        #$('#razorpay_gateway_logo').attr 'src', '/assets/razorpay-logo.png'


    pay_type_info = $(current_selected_opt).attr('data-info')


  pageUpdates(pay_type_info)

updatePlaceOrderText = () ->
  text_value = $("input[name='order[pay_type]']:checked").next('label').text()
  if text_value != 'Cash On Delivery'
    $('#wallet_payment_option').show();
    $('#wallet_cant_be_used_for_cod').hide();
  if text_value == 'PayPal ( What is PayPal? )'
    $('.pay_type_selected').text('You have selected PayPal')
  else
    $('.pay_type_selected').text('You have selected ' + text_value)

$ ->

  if $('#order_billing_pincode').length > 0
    updateCashPaymentOptions = (data, pincode) ->
      if data.cart_value < data.min_cart_value
        $('#order_pay_type_cash_on_delivery').attr('disabled', true)
        $('.cod_pay_type_message').html('(Not available)')
        $('#cod1_status_always').removeClass().addClass('cod_error alert alert-danger').
        text("Cash on delivery is available for orders above "+data.symbol+" "+ data.min_cart_value + ".Your Current Items Total is " + data.symbol+" "+data.cart_value)
      else if data.cod
        $('#order_pay_type_cash_on_delivery').attr('disabled', false)
        if gon.auto_select_cod
          $('#order_pay_type_cash_on_delivery').attr('checked', true)
        $('.cod_pay_type_message').html('(Available)')
        $('#cod1_status_always').removeClass().addClass('cod_success alert alert-success').text("Cash On delivery available for " +  pincode)
        $('#order_cod_charge').html(data.cod_c)
      else if data.error_cod == 1
        $('#order_pay_type_cash_on_delivery').attr('disabled', true)
        $('.cod_pay_type_message').html('(Not Available)')
        $('#cod1_status_always').removeClass().addClass('cod_error alert alert-danger').text("Add 1 or more items to cart to Avail Cash On Delivery for " +  pincode)
      else if data.error_cod == 3
        $('#order_pay_type_cash_on_delivery').attr('disabled', true)
        $('.cod_pay_type_message').html('(Not Available)')
        if data.designer_unshippable.length == 1
          $('#cod1_status_always').removeClass().addClass('cod_error alert alert-danger').text("Marked design is not available for Cash On Delivery to " +  pincode + " Sorry For Inconvenience")  
        else
          $('#cod1_status_always').removeClass().addClass('cod_error alert alert-danger').text("Marked designs are not available for Cash On Delivery to " +  pincode + " Sorry For Inconvenience")
        for design_id in data.designer_unshippable
          $("#design_"+design_id).css('border','2px solid #03A9F4')
          $('#design_'+design_id).addClass('designer_unshippable')
        unless $('#ship_to_same_address').is("checked")
          $('.cod_error').hide()
      else
        $('#order_pay_type_cash_on_delivery').attr('disabled', true)
        $('.cod_pay_type_message').html('(Not Available)')
        $('#cod1_status_always').removeClass().addClass('cod_error alert alert-danger').text("Invalid Pincode. Please enter a valid pincode")

      if data.cbd && !data.cod
        $('#order_pay_type_cash_before_delivery').parent().removeClass('hide')
        $('#order_pay_type_cash_before_delivery').attr('disabled', false)
        $('.gharpay_pay_type_message').html('(Available)')
        # $('#cod_status_always').removeClass().addClass('cod_success alert alert-success').text("Cash before delivery available for " +  pincode)
      else
        $('#order_pay_type_cash_before_delivery').parent().addClass('hide')
        $('#order_pay_type_cash_before_delivery').attr('disabled', true)
        $('.gharpay_pay_type_message').html('(Not Available)')
        #$('#cod1_status_always').removeClass().addClass('cod_error alert alert-danger').text("Cash before delivery not available for " +  pincode)
      shippingCountry()

    updateCityandStateOptions = (data, pincode, city_selector, state_selector) ->
      if data.fCity == "NA" || data.fCity == "Nil"
        $("##{city_selector}").val(data.fDistrict)
      else
        $("##{city_selector}").val(data.fCity)
      if typeof data.fState != 'undefined'
        $("##{state_selector}").val(data.fState).prop('selected', true);

    updateInternationalCityandStateOptions = (data, city_selector, state_selector) ->
      if data['places'] != undefined && data['places'][0] !=undefined
        if data['places'].length == 1
          $("##{city_selector}").val(data['places'][0]['place name'])
        else
          place_names = (place['place name'] for place in data['places'])
          $("##{city_selector}").val('')
          $("##{city_selector}").autocomplete({source: place_names})
        state_name = data['places'][0]['state']
        if state_name == '' || state_name == undefined
          state_code = data['places'][0]['state abbreviation']
          if state_code == '' || state_code == undefined
            state_name = data['places'][0]['place name']
          else 
            state_name = state_code
        if stateListFuse != undefined && (state_option = stateListFuse.search(state_name)[0]) != undefined
          state_name = state_option.value
        $("##{state_selector}").val(state_name).prop('selected', true)

    paramCheckCashOptions = (cart_id, pincode) ->
      type: 'GET'
      data:
        pincode: pincode
        cart_id: cart_id
      url: '/api/cod_cbd'
      datatype: 'JSON'
      success: (data, status, jqhxr) ->
        updateCashPaymentOptions(data, pincode)
        updateOtherPaymentOptions()

    paramFetchCityStateOptions = (pincode, city_selector, state_selector) ->
      type: 'GET'
      data:
        pincode: pincode
      url: '/api/pincode_info'
      datatype: 'JSON'
      success: (data, status, jqhxr) ->
       updateCityandStateOptions(data, pincode, city_selector, state_selector)

    paramInternationalFetchCityStateOptions = (country_code, pincode, city_selector, state_selector) ->
      type: 'GET'
      url: '//api.zippopotam.us/'+country_code+'/'+pincode
      datatype: 'JSON'
      success: (data, status, jqhxr) ->
       updateInternationalCityandStateOptions(data, city_selector, state_selector)

    checkPaymentOptions = () ->
      pincode = $('#order_billing_pincode').val()
      country = $('#order_billing_country').val()
      city_selector = 'order_billing_city'
      state_selector = 'order_billing_state'
      is_india = $('#order_billing_country').val() == 'India'
      cart_id = $('#cart_final_id').val()
      order_country = $('#order_country').val()
      if country != ''
        if is_india
          #$('#payment_gateway_logo').attr 'src', '/assets/payu.png'
          #$('#order_pay_type_paypal').attr('disabled', true)
          $('#order_pay_type_payumoney').removeAttr('disabled')
          $('#order_pay_type_paytm').removeAttr('disabled')
          if pincode.length == 6 && /^[1-9]\d{5}$/.test(pincode)
            if $('#paypal-button').length > 0 
              $('#paypal-button').css("pointer-events", 'auto')
            if $('#paypal-card-button').length > 0
              $('#paypal-card-button').css("pointer-events", 'auto')
            $.ajax(paramCheckCashOptions(cart_id, pincode))
            $.ajax(paramFetchCityStateOptions(pincode, city_selector, state_selector))
            $('#green_button_box').find('input[type=submit]').attr('disabled', false)
          else
            message = "Pincode should be six digits"
            $("##{city_selector}").val("")
            $("##{state_selector}").val("")

            if pincode.length > 0
              $('#cod1_status_always').removeClass().addClass('cod_error alert alert-danger').text(message)
            else
              $('#cod1_status_always').removeClass()
              $('#cod1_status_always').html('')
            if $('#paypal-button').length > 0 
              $('#paypal-button').css("pointer-events", "none")
            if $('#paypal-card-button').length > 0
              $('#paypal-card-button').css("pointer-events", "none")
            $('#order_pay_type_cash_on_delivery').parent().show()
            $('#order_pay_type_cash_on_delivery').attr('disabled', true)
            $('#order_pay_type_cash_before_delivery').attr('disabled', true)
            $('#cod_status_always').removeClass()
            $('#cod_status_always').html('')
            $('.cod_pay_type_message').html('(Pincode Required)')
            $('.gharpay_pay_type_message').html('(Pincode Required)')
            $('#green_button_box').find('input[type=submit]').attr('disabled', true)
            updateOtherPaymentOptions()
        else if !is_india
          $('#payment_gateway_logo').attr 'src', '/assets/paypal.png'
          $('#razorpay_gateway_logo').hide()
          $('#order_pay_type_paypal').attr('disabled', false)
          $('#cod1_status_always').removeClass()
          $('#cod1_status_always').html('')
          $('.gharpay_pay_type_message').html('(Not Available)')
          $('#order_pay_type_payumoney').attr('disabled', true)
          $('#order_pay_type_paytm').attr('disabled', true)
          updateOtherPaymentOptions()
          if countryCode != undefined && zipRegex.test(pincode)
            $.ajax(paramInternationalFetchCityStateOptions(countryCode, pincode, city_selector, state_selector))
      
      if $('#ship_to_same_address').is(":checked")
        order_country = country
      if order_country != 'India' && order_country != ''
        order_country = order_country.toLowerCase()
        # this enables cod if country is present in the system constant ENABLE_COD_COUNTRIES that enables cod for certain countries and if all the products in the cart are available in warehouse
        if gon.enable_cod_countries.includes(order_country) and gon.enable_cod_basis_products
          $('#order_pay_type_cash_on_delivery').parent().show()
          $('#order_pay_type_cash_on_delivery').attr('disabled', false)
          $('.cod_pay_type_message').html('(Available)')
          $('.payment-text').show()
        else
          $('#cod_status_always').removeClass()
          $('#cod_status_always').html('')
          $('#order_pay_type_cash_before_delivery').attr('disabled', true)
          $('#order_pay_type_cash_on_delivery').parent().hide()
          $('#order_pay_type_cash_on_delivery').removeAttr('checked')
          $('.cod_pay_type_message').html('(Not Available)')

    checkPaymentOptionsForShipping = () ->
      country = $('#order_billing_country').val()
      city_selector = 'order_city'
      state_selector = 'order_buyer_state'
      is_india = $('#order_country').val() == 'India'
      if country != ''
        if is_india
          pincode = $('#order_pincode').val()
          if pincode.length == 6
            $.ajax(paramFetchCityStateOptions(pincode, city_selector, state_selector))
          else
            message = "Pincode should be six digits"
            $("##{city_selector}").val("")
            $("##{state_selector}").val("")

    hideCODandHighlight = () ->
      $('.cod').hide()
      $('.designer_unshippable').css('border','')
      $('.order_content').children().removeClass('designer_unshippable')

    $('#order_billing_pincode').keyup ->
      hideCODandHighlight()
      checkPaymentOptions()
    $('#order_billing_pincode').on 'input paste', (e) ->
      if e.target.id == 'order_billing_pincode'
        hideCODandHighlight()
        checkPaymentOptions()

    $('#order_pincode').keyup ->
      checkPaymentOptionsForShipping()
    $('#order_pincode').on 'input paste', (e) ->
      if e.target.id == 'order_pincode'
        checkPaymentOptionsForShipping()

    changeCreditCardLabel = () ->
      country_name = $('#order_billing_country').val()
      if country_name is 'India' || country_name is ''
        $('label[for="order_pay_type_creditdebit_cardnet_banking"]').html('Credit/Debit Card/Net Banking/Wallet')
      else
        $('label[for="order_pay_type_creditdebit_cardnet_banking"]').html('Credit Card')


    $('#order_billing_country').change ->
      changeCreditCardLabel()
      checkPaymentOptions()
      hideCODandHighlight()
      if $("#order_country").not(":visible")
        internationalMinVal()
      if $('#ship_to_same_address').is(":checked")
        updateTotalNShipping(this)

    changeCreditCardLabel()
    checkPaymentOptions()
    shippingCountry()
  
    $("#order_country").change ->
      checkPaymentOptions()

    $('#ship_to_same_address').click ->
      checkPaymentOptions()


$ ->
  $('#cbd-check').click ->
    if $('#order_billing_pincode').val().length is 6
      checkPaymentOptions()

$ ->
  $("#order_country").change ->
    updateTotalNShipping(this)
    appendPhoneCode('order_country', 'phone-code2')
    rakhiPreOrder()
    shippingCountry()
    internationalMinVal()
    displayCustomsChargeMessage($(this).val())

$ ->
  $("#order_billing_city").change ->
    if $('#ship_to_same_address').is(":checked")
      updateDeliveryTime($('#order_billing_country').val(), $('#order_billing_city').val(), true, $('#order_billing_pincode').val() )
    else
      updateDeliveryTime($('#order_country').val(), $('#order_city').val(), true, $('#order_pincode').val())
$ ->
  $('#ship_to_same_address').click ->
    updateForm()
    rakhiPreOrder()
    shippingCountry()
    internationalMinVal()

  $('#ship_to_same_address').on('change', (event) ->
    if ($(this).prop('checked'))
      displayCustomsChargeMessage($('#order_billing_country').val())
      updateDeliveryTime($('#order_billing_country').val(), $('#order_billing_city').val(), true, $('#order_billing_pincode'))
    else
      displayCustomsChargeMessage($('#order_country').val())
      updateDeliveryTime($('#order_billing_country').val(), $('#order_billing_city').val(), true, $('#order_pincode'))
  )

$ ->
  updateForm()

pageUpdates = (pay_type_info) ->
  $('.ship_to_same_address_note').hide()
  $('.payment-info-text').html(pay_type_info)

  updateForm()
  updatePlaceOrderText()
$ ->
  if Cookies.get 'prefered_pay_type'
    selected_payment_option = Cookies.get 'prefered_pay_type'
    $('#'+selected_payment_option).prop 'checked', 'checked'

updateExpressShipping = (country = undefined) ->
  if country is undefined
    if $('#ship_to_same_address').is(':checked')
      country = $('#order_billing_country').val()
    else
      country = $('#order_country').val()
  shipping_message = 'Get your order delivered within '
  if (country in gon.express_delivery_countries && gon.express_delivery_time != undefined)
    $('#regular_shipping').show()
    if $('[id=return_checkbox]').length > 0 && document.getElementById('return_checkbox').innerHTML == 'Cancel'
      $('#express_shipping').hide()
      $('#express_delievery_cannot_be_applied_for_wallet').show()
    else
      $('#express_shipping').show()
    maximum_express_time = parseInt(gon.express_delivery_time) + 1
    $('#delivery_message_express').text(shipping_message + maximum_express_time + ' days.').show()
  else
    $('#regular_shipping').show()
    $('#delivery_type_regular').attr('checked', true)
    $('#express_shipping').hide()
  if country == 'India'
    $('#shipping_option').hide()
    $('#payment_box_number').text '2. Payment Method'
  else
    $('#shipping_option').show()
    $('#payment_box_number').text '3. Payment Method'
  if country.toLowerCase() == 'india'
    shipping_message += Math.round(gon.indian_shipping_time) + ' days'
  else
    shipping_message += (Math.round(gon.shipping_time + gon.country_wise_shipping_time[country.toLowerCase()]) + ' days')
  $('#delivery_message_regular').text shipping_message

updateShippingAndDeliveryTime = () ->
  $('.shipping_cost').text gon.express_delivery_charge_with_currency
  $('.delivery_time').text gon.express_delivery_time.concat(' days')

$ ->
  updateExpressShipping()

$ ->
  $('input[name="delivery_type"]').click ->
    updateForm()

$ ->
  paypal_smartpay_available = () ->
    $('#paypal-button').length > 0

  card_smartpay_available = () ->
    $('#paypal-card-button').length > 0

  $('input[name="order[pay_type]"]').click ->
    pay_type_value = $(this).val()
    selected_payment_option = $(this).attr('id')
    Cookies.set 'prefered_pay_type', selected_payment_option, {expires: 7, path: '/'}
    $('#payment_gateway_logo').show()
    $('#razorpay_gateway_logo').hide()
    $('#paypal-button').hide()
    $('#paypal-card-button').hide()
    $('#green_button_box').show()
    if pay_type_value == 'PayPal'
      $('#payment_gateway_logo').attr 'src', '/assets/paypal.png'
      if paypal_smartpay_available() && !$("#international_min_val").hasClass('alert alert-danger')
        $('#paypal-button').show()
        $('#green_button_box').hide()
        $('#payment_gateway_logo').hide()
    else if pay_type_value == 'Credit/Debit Card/Net Banking'
      if card_smartpay_available() && !$("#international_min_val").hasClass('alert alert-danger')
        $('#paypal-card-button').show()
        $('#green_button_box').hide()
        $('#payment_gateway_logo').hide()
      else if $('#order_billing_country').val() isnt "India"
        $('#payment_gateway_logo').attr 'src', '/assets/paypal.png'
      else
        $('#payment_gateway_logo').attr 'src', '/assets/payu.png'
        $('#razorpay_gateway_logo').attr 'src', '/assets/razorpay-logo.png'
        $('#razorpay_gateway_logo').show()        
    else if pay_type_value == 'PayUmoney'
        $('#payment_gateway_logo').attr 'src', '/assets/payumoney.png'
    else if pay_type_value == 'Paytm'
        $('#payment_gateway_logo').attr 'src', '/assets/paytm.png'
    else
      $('#payment_gateway_logo').hide()
    pay_type_info = $(this).attr('data-info')
    pageUpdates(pay_type_info)

  if !$("#international_min_val").hasClass('alert alert-danger')
    payment_gateway = $('input[name="order[pay_type]"]:checked').val()
    if paypal_smartpay_available() && payment_gateway == 'PayPal'
      $('#paypal-button').show()
      $('#green_button_box').hide()
      $('#payment_gateway_logo').hide()
    else if card_smartpay_available() && payment_gateway == 'Credit/Debit Card/Net Banking'
      $('#paypal-card-button').show()
      $('#green_button_box').hide()
      $('#payment_gateway_logo').hide()

$ ->
  $('[id^="saved_address_"]').each ->
    $(this).click ->
      idx = $(this).attr('id').replace('saved_address_', '')
      $('#order_billing_first_name').val(gon.addresses[idx].first_name)
      $('#order_billing_last_name').val(gon.addresses[idx].last_name)
      $('#order_billing_phone').val(gon.addresses[idx].phone)
      $('#order_billing_pincode').val(gon.addresses[idx].pincode)
      $('#order_billing_street_line_1').val(gon.addresses[idx].street_address_line_1)
      $('#order_billing_street_line_2').val(gon.addresses[idx].street_address_line_2)
      $('#order_billing_city').val(gon.addresses[idx].city)
      $('#order_billing_state').val(gon.addresses[idx].state)
      $('#order_billing_country').val(gon.addresses[idx].country)
      $('#change_address').modal('hide')

$ ->
  $('#show_full_cart').click (e) ->
    e.preventDefault()
    showcart()

$ ->
  $('#new_order').submit ->
    # for elem in gon.items
      # ga('ec:addProduct', elem);
    # ga('ec:setAction','checkout', {'step': 2});
    # ga('send', 'event', 'UX', 'click', 'Place Order');
    if typeof _osCheckout != 'undefined'
      _osCheckout(_osParamsObj)

    # if (typeof fbq != 'undefined') && $("#new_order").valid()
      # fbq('track', 'AddPaymentInfo')

    if $('#ship_to_same_address').is(":checked")
      pincode = $('#order_billing_pincode').val()
    else
      pincode = $('#order_pincode').val()
    if pincode.length is 6
      Cookies.set 'pincode', pincode, path: '/'
    grand_value_array = $("li#grand_total").text().split(" ")
    item_value = 0
    for item in gon.items_data
      item_value += item.price
    if $("#new_order").valid()
      ga4_shipping_params = {event: "ga4_add_shipping_info", ecommerce: {currency: "INR", country_code: gon.country_code, value: gon.item_total_price, shipping_tier: "Regular",tax: gon.tax_amount || 0,coupon: gon.coupon_code || "",coupon_discounts: gon.coupon_price || 0,customization: gon.addon_charges || "",gift_wrap: gon.gift_wrap,offers_discount: gon.bmgn_discount_rounded_val, items: gon.items_data}}
      dataLayer.push({ ecommerce: null });
      dataLayer.push(ga4_shipping_params)
      console.log(ga4_shipping_params)
      pay_type_value = $('input[name="order[pay_type]"]:checked').val() || "Credit Card"
      ga4_payment_params = {event: "ga4_add_payment_info", ecommerce: {currency: "INR", country_code: gon.country_code, value: gon.item_total_price,tax: gon.tax_amount || 0,coupon: gon.coupon_code || "",coupon_discounts: gon.coupon_price || 0,customization: gon.addon_charges || "",gift_wrap: gon.gift_wrap,offers_discount: gon.bmgn_discount_rounded_val, items: gon.items_data, payment_type: pay_type_value}}
      dataLayer.push({ ecommerce: null });
      dataLayer.push(ga4_payment_params)
      console.log(ga4_payment_params)
    

$ ->
  if $('#order_billing_country, #order_country').length > 0
    createStateDropDown = (state_list) ->
      options = ''
      $.each state_list, (index, state) ->
        options += "<option value = '" + state + "'>" + state + "</option>"
      options

    paramsGetStates = (country_code, id, selected_value, clear_pin_and_city=true) ->
      type: 'GET'
      url: '/country/' + country_code + '/get_states'
      success: (data, status, jqhxr) ->
        options = createStateDropDown(data)
        if id == 'order_billing_country'
          if clear_pin_and_city
            $("#order_billing_city").val("")
            $("#order_billing_pincode").val("")
          if options == ''
            $('#order_billing_state').replaceWith('<input id="order_billing_state" name="order[billing_state]" size="30">')
          else
            options = "<option value = ''>Please Select</option>" + options
            list = "<select id=order_billing_state name =order[billing_state]>" + options + "</select>"
            $('#order_billing_state').replaceWith(list)
          if typeof selected_value != 'undefined'
            $('#order_billing_state').val(selected_value)
          $.ajax(showPincodeFormat(country_code,id))
        else
          $("#order_city").val("")
          $("#order_pincode").val("")
          if options == ''
            $('#order_buyer_state').replaceWith('<input id="order_buyer_state" name="order[buyer_state]" size="30">')
          else
            options = "<option value = ''>Please Select</option>" + options
            list = "<select id=order_buyer_state name =order[buyer_state]>" + options + "</select>"
            $('#order_buyer_state').replaceWith(list)
          if typeof selected_value != 'undefined'
            $('#order_buyer_state').val(selected_value)
          $.ajax(showPincodeFormat(country_code,id))
        $('#order_billing_pincode').trigger('input')

    $countryList = $('#order_billing_country, #order_country')

    showPincodeFormat = (country_code, id)  ->
      type: 'GET'
      url: '/country/' + country_code + '/get_pincode_format'
      success: (data, status, jqhxr) ->
        pincode_format = data[0][0]
        if id == 'order_billing_country'
          if pincode_format != "" && pincode_format != null
            $("#showpincodefields").show()
            $("#pincode_format_notice").text "Please enter Pincode / Zip / Postal Code in following format. Example : " +pincode_format
            $("#pincode_format_notice").css('display','inline-block')
          else
            $("#order_billing_pincode").val("None")
            $("#showpincodefields").hide()
            $("#pincode_format_notice").css('display','none')
        else
          if pincode_format != "" && pincode_format != null
            $("#showpincodefieldsshipping").show()
            $("#pincode_format_notice_shipping").text "Please enter Pincode / Zip / Postal Code in following format. Example : " +pincode_format
            $("#pincode_format_notice_shipping").css('display','inline-block')
          else
            $("#order_pincode").val("None")
            $("#showpincodefieldsshipping").hide()
            $("#pincode_format_notice_shipping").css('display','none')
        countryCode = data[1][0]
        zipRegex = new RegExp(data[1][1],'i')
        if countryCode == undefined
          stateListFuse = undefined
        else
          options = {shouldSort: true,threshold: 0.85,location: 0,distance: 100,maxPatternLength: 32,minMatchCharLength: 2,keys: ["value"]};
          stateListFuse = new Fuse($('#order_billing_state option'), options);


    $countryList.on('change', (e) ->
      if $(this).val() != ''
        $.ajax(paramsGetStates($(this).val(), $(this)[0].id))
    )

    if $countryList.val() != ''
      $.ajax(paramsGetStates($countryList.val(), $countryList[0].id, $('#order_billing_state').val(), false))
    shippingCountry()

  $('#order_billing_country').on('change', (e) ->
    rakhiPreOrder()
    appendPhoneCode('order_billing_country', 'phone-code1')

    if $('#ship_to_same_address').prop('checked')
      displayCustomsChargeMessage($(this).val())
  )

displayCustomsChargeMessage($('#order_billing_country').val())


$ ->
  $('.toggle-breakdown').click (e) ->
    e.preventDefault()
    breakdownDetails = $(this).closest('.subscription-container').find('.breakdown-details')
    if breakdownDetails.is(':visible')
      breakdownDetails.slideUp()
      $(this).text('View Breakdown')
    else
      breakdownDetails.slideDown()
      $(this).text('Hide Breakdown')

  handleSubscriptionChange = ->
    subscriptionContainer = $('.subscription-container')
    checkbox = subscriptionContainer.find('input[type="checkbox"]#subscription_enabled')
    subscriptionId = subscriptionContainer.data('subscription-plan-id')
    form = checkbox.closest('form')
    hiddenInputName = 'subscription_plan'

    form.find("input[name='#{hiddenInputName}']").remove()

    if checkbox.is(':checked')
      $('<input>').attr(
        type: 'hidden'
        name: hiddenInputName
        value: subscriptionId
      ).appendTo(form)
    
    updateForm()

  handleSubscriptionChange()

  $('input[name="enable_subscription"]').change ->
    handleSubscriptionChange()