@import 'variables';

$placeholder_order: #404040;
.order-fields-format-notify-text{
    margin-left: 33%;
    margin-top: 5px;
    text-align: center;
    font-size: 12px;
    background-color: rgb(223, 240, 216);
    color: rgb(60, 118, 61);
    padding: 5px;
    border: 1px solid transparent;
    border-radius: 4px;
    width: 60%;
    display: inline-block;
}

.input-phone-box{
    width: 50% !important;
    border-radius: 0px 4px 4px 0px !important;
}
#designer_order_items{
    width: 85%; 
    margin: 25px auto;
    background: #80808038;
    .panel-heading{
        width: fit-content;
        padding: 5px;
        a{
            color: white !important;
        }
    }
}
#post_order_stitching_submit {
    width: 75%;
    margin: auto;

    & input {
        background: none;
        border: 2px solid #80808038;
        background-color: #670b19;
        padding: 1em 1.2em;
        color: #fff;
        margin-bottom: 2em;
    }
}
.label-info{
    background: #c695a3 !important;
    border-radius: 0;
}
.hr_line{
    border-top: 1px solid #d6d6d7 !important;
}
//oder show page css---new template
#designer_order_group{
    padding-bottom: 15px;
    #item_image{
      img{
        height: 150px;
        width: 152px;
      }
      border: 1px solid white;
      margin-left: 20px;
      float: left;
      height: 100%;
    }
    td{
      background: none;
    }
    td.form_links{
      margin-left: 15px;
      a{
        margin-left: 20px;
      }
    }
    #details_strip{
      display: flex;
      margin-bottom: 20px;
      margin-top: 10px;
      .product_title{
        font-weight: bolder;
        font-size: 15px;
        margin-bottom: 10px;
      }
      a{
        text-decoration: none;
        font-size: 15px;
        display: flex;
      }
      .sub_total{
        display: inline-block;
      }
      b{
        color: orange;
      }
    } 
    .status_pipeline{
      margin: 10px 0px 10px 0px;
    }
    .circle {
      display: inline-block;
      width:20px;
      height:20px;
      border-radius:50%;
      border: 2px solid #615a5a;
      font-size:12px;
      line-height:50px;
      text-align:center;
      background:$background-color;
      margin-left: 30px;
    }
    .stage_name {
      position: absolute;
      font-size: 12px;
      display: inline-block;
      text-align:center;
      margin: 25px 0px 0px 13px;
    }
    .horizontal_line {
      width: 175px;
      height: 4px;
      background: $background-color;
      position: absolute;
      z-index: 0;
      margin: 9px 1px;
      border-radius: 6px;
    }
    .all_order_stages{
      width: 100%;
      height: 35px;;
      text-align: center;
      .left-div{
        width: 20%;
        float: left;
      }
    }
    .tracking_details{
      width: 60%;
      margin: 20px auto 0px;
      text-align: center;
      padding: 5px;
      font-weight: 600;
      color: #670b19;
      a{
        text-decoration: underline;
      }
    }
    #item_price{
      float: left;
      padding-left: 20px;
      .product_details{
        line-height: 20px;
      }
      span{
        color: $background-color;
      }
    }
    #item_addons{
      .item_addon, .sub{
        line-height: 25px;
      }
    }
}




#order_status {
    position: relative;
    .left_div {
      position: relative;
      float:left;
      top: 40px;
      &:first-child{
        margin-left: 40px;
      }

      .circle {
        display: inline-block;
        width:50px;
        height:50px;
        border-radius:50%;
        font-size:12px;
        line-height:50px;
        text-align:center;
        background:$background-color;
        margin-left: 30px;
      }
      .stage_name {
        top: 55px;
        position: absolute;
        font-size: 12px;
        display: inline-block;
        text-align: center;
        left: 0px !important;
      }

      .horizontal_line {
        width:68px;
        height:3px;
        background:$background-color;
        position: absolute;
        top: 45%;
        z-index: 0;
        left: 68%;
      }
    }

    .all_order_stages {
      border: 1px solid $background-color;
      height: 160px;

      .order_title {
        position:absolute;
        text-align:center;
        background-color: $background-color;
        color: $white;
        width:160px;
        padding: 6px;
      }
    }

    .step_image {
      background: transparent;
      position: relative;
      top: 5px;
    }

    .status_notice {
      background:  $light-red-background;
      margin-top: 8px;
      margin-bottom: 10px;
      text-align: center;
      padding: 8px 15px;
      margin-left: auto;
      margin-right: auto;
    }
  }

/*Return pipeline*/
#return_status{
  position: relative;
  margin-top: 20px !important;
    .left_div {
      position: relative;
      float:left;

      .circle {
        border: none;
        background: none;
        display: inline-block;
        width:40px;
        height:40px;
        border-radius:0%;
        font-size:12px;
        line-height:50px;
        text-align:center;
        span{
          color: $dark-red-text;
        }
      }
      .stage_name {
        position: absolute;
        top: 15px;
        width: 70px;
        font-weight:bold;
        font-size: 15px;
        display: inline-block;
        text-align: center;
        color: $dark-red-text;
        left: 0px !important;
        margin-left: 15px;
      
      .timestamp{
          font-size: 12px;
          color: black;
        }
      }
      .horizontal_line {
        width:58px;
        height:4px;
        position: absolute;
        color: $dark-red-text;
        top: 30%;
        z-index: 0;
        left: 68%;
        right: 68%;
        border-radius:0% !important; 
      }
    }

    .all_order_stages {
      background: #eeeeee;
      height: 130px;
      .order_title {
        position:relative;
        text-align:center;
        padding: 5px;
        margin-bottom: 7px;
        background-color: $background-color;
        color: $white;
        width: 120px;
      }
    }

    .step_image {
      background: transparent;
      top: 5px;
    }
  }
#checkout_wrapper {

    padding-top:25px;

    @media screen and (max-width: $mobile_device){
        padding-top:15px;
    }
    .shipping-payment-text{
        padding: 12px 0px;
        color: $background-color;
        font-size: 16px;
    }
    .free_shipping_box {
        padding: 16px 0px;
        background: #303030;
        ul li{
            margin-right:7px;
            list-style: none;
            float: left;
            padding: 0;
        }
    }

    .row{
        margin-bottom: 0px !important;
    }

    p{
        font-size: 12px;
        color: $global-text-color;
        line-height: 20px;
        letter-spacing: 0.5px;
        text-align: justify;
    }

    a{
        color: $background-color !important;
    }
    #customs_charge_message{
      text-align: right;
      color: $background-color;
      margin-bottom: 15px;
    }
    #checkout_button input[type="submit"]{
        background: $shop-now-button-color;
        padding: 10px 60px;
        border: 1px solid $background-color;
        color: white;
        text-transform: uppercase;
        font-size: 18px;
        font-weight: bold;
    }

    h3{
        color: $global-text-color;
        margin-bottom: 13px !important;
        letter-spacing: 1px;
        font-weight: normal;
    }

}
.survey-form-block{
    background: #808080;
    text-align: center;
    padding: 7px 16px;
    margin-left: auto;
    margin-right: auto;
    width: 85%;
    .survey-form{
        color: $white !important;
        text-decoration: underline;
        font-size: medium;
    }
}
.button-block{
    display: flex; 
    justify-content: center;
    margin-top: 10px;
    .button-block-info{
        margin: 0px 5px;
    }
}
.track_return_button{
    background: #615a5a;
    padding: 10px 25px;
    color: white !important;
    text-transform: uppercase;
}

#shipping_option{
    // border-bottom: 1px solid $payment-text-bg;
    // border-top: 1px solid $payment-text-bg;
}
.big_button a,
.big_button input[type="submit"]{
    @media screen and (max-width: $mobile_device){
        background-position: 185px 14px;
        padding-left: 8px;
        width: auto;
    }
}

.center_button{
    position: relative;
    left: 40%;
    @media screen and (max-width: $mobile_device){
        position: relative;
        left: 20%;
        padding-top: 0px;
    }
}

#how_to_pay{
    ul li{
        list-style: disc;
        padding-left:5px;
        list-style-position: inside;
        color: $global-text-color;
        padding-bottom:10px;
        font-size: 12px;
    }
    
}

p.minimum_order{
    font-size: 15px !important;
    color: #ADACAC !important;

    @media screen and (max-width: $mobile_device){
        text-align: center;

    }

}

.floatr{
    float: right;
}
.payment_box_style{
    border: 1px solid $payment-text-bg;
    background-color: $light-background;
    font-size: 14px;
    color: $global-text-color;
    margin-bottom: 10px;
    padding: 0px 18px;
    .cod_pay_type_message{
        color: $background-color !important;
    }
    .amount_payable{
        color: $global-text-color !important;
    }
    .payment_row{
      margin: 10px 0px 0px 0px;
    }
    #cod_status_always, #cod1_status_always {
        padding-bottom: 5px;
        padding-top: 5px;
    }

    h4{
        font-size: 20px;
        color: $global-text-color;
        float: left;
    }

    li label{
         font-weight: normal;
    }

    ul li{
        list-style: none !important;
        padding-bottom: 5px;
        overflow: hidden;
        line-height: 18px;

    }

    #billing_details li{
        clear:both;
        margin-bottom: 10px;
    }

    #billing_details li label{
        width: 33%;
        float: left;
        text-align: right;
        padding-right: 12px;
    }
    #billing_details li label.error{
        width: 40%;
        float: right;
        padding-right: 12px;
        color: #FF0404;
        @media screen and (max-width: $mobile_device){
            width: 70%;
        }
    }

    #billing_details li input{
        float: left;
    }
    
    #billing_details {
      #seamless-account-check.is-hidden, .seamless-account-password.is-hidden {
        display: none;
      }
      
      #seamless-account-check {
        padding-left: 33%;
        text-align: left;
        
        label, input {
          display: inline;
          float: left;
          width: auto;
        }
      }
    }

    input, button, select, textarea {
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
        background-color: $payment-text-bg;
        border: 1px solid $payment-text-bg;
        border-radius: 4px;
        color: #000 !important;
    }


    ul#billing_details{

        input, button, select, textarea{
             width: 60%;

             @media screen and (max-width: $mobile_device){
                width: 70%;
             }

        }

        textarea{
            height: 100px;
        }

        ::-webkit-input-placeholder { /* WebKit, Blink, Edge */
            color:    $placeholder_order;
        }
        :-moz-placeholder { /* Mozilla Firefox 4 to 18 */
           color:    $placeholder_order;
        }
        ::-moz-placeholder { /* Mozilla Firefox 19+ */
           color:    $placeholder_order;
        }
        :-ms-input-placeholder { /* Internet Explorer 10-11 */
           color:    $placeholder_order;
        }


    }

    input, button, textarea, select {
        padding: 10px;
    }

    .address input{
        width: auto !important;
        float: left;
    }

    .address label{
        display: inline-block;
        float: left;
        padding-top: 4px;
        padding-left: 10px;
        font-weight:normal;
    }

    .ship_to_label{
        text-align: left !important;
        padding-right: 0px !important;
        width: 40% !important;

        @media screen and (max-width: $mobile_device){
            width: 68% !important;
        }
    }

    .delivery_price {
      float: right;
      margin-top:5px;
    }

    .delivery_days{
      color: $background-color;
      margin-top: 7px;
      margin-left: 17px;
    }

    .delivery_message {
      color: black;
      margin-top:10px;
      padding: 5px;
      padding-right:0px;
      border-radius: 5px !important;
    }
}
.input-phone-addon{
    width:11% !important;
    float: left !important;
    padding: 11px;
    border-radius: 4px 0px 0px 4px !important;
    border: 1px solid #cacaca !important;
    background: #ccc !important;
    color: #191919 !important;
}

.checkout_head{
padding: 10px 0px;
}

.change_address {
    float: right;
    a{
    color: $global-text-color !important;
    font-size: 14px;
    text-decoration: none;
    margin-top: 3px;
    display: block;
    }
}


#payment_method{
    .payment-text{
        padding: 15px;
        margin-bottom: 20px;
        background: $payment-text-bg;
        line-height: 20px;
    }

    ul li input{
        float: left;
        height: 18px
    }

    ul li label{
        margin-top: 4px;
        float: left;
        padding-left: 8px;
        color: $global-text-color;
    }

}


.checkoutCart{
    width: 100%;

    ul{
        li{
            float: left;
            padding: 5px;
            text-align: center;
            
        }
    }

    .order_head{
        overflow: hidden;
        background-color: $payment-text-bg;
        line-height: 25px;

        li.head_padding{
            font-weight:normal;
            color: $global-text-color;
            
        }
    }

    .order_content{
        overflow: hidden;
        border: 1px solid $payment-text-bg;
        margin: 5px 0px;
        li:first-child,
        li.first-child{
            padding-top: 5px;
        }

        li{
            padding-top: 22px;

            @media screen and (max-width: $mobile_device){
                font-size: 12px;
            }
            img{
                border: 1px solid grey;
            }
        }

        .mobile_entry{
            padding-top: 0;
            li:first-child{
                text-align: left;
            }
            
            ul li{
                padding: 0 0 5px !important;
            }
        }
    }

    .order_total{
        float: right;
        width: 50%;
        color: #DFDADA;
        font-size: 16px;
        line-height: 22px;
        .notice_class {
          color: $background-color;
          font-size:12px;
          line-height:15px;
          font-style:italic
        }

        li{
            padding-top: 5px !important;
            text-align: right;
            width: 50%;
        }

        .free{
            color: #568801;
        }

        .order_content:last-child,
        .order_content.last-child{
            border-bottom: 0;
        }

        @media screen and (max-width: 1199px){
            width: 60%;
        }

        @media screen and (max-width: 990px){
            width: 79%;
        }

        @media screen and (max-width: $mobile_device){
            width: 65%;

            .order_content{
                li{
                    font-size: 14px;
                }
            }
        }
    }

    .net-total{
        font-weight: bold;
        width: 100%;
        overflow: hidden;
        color: #000;
        background-color: $payment-text-bg;
        font-size: 16px;
        padding: 5px 0;
        line-height: 15px;

        @media screen and (max-width: $mobile_device){
            padding: 0;
        }

        .order_content{
            float: right;
            width: 50%;

            @media screen and (max-width: 1199px){
                width: 60%;
            }

            @media screen and (max-width: 990px){
                width: 79%;
            }

            @media screen and (max-width: $mobile_device){
                width: 65%;

                li{
                    font-size: 14px;
                }
            }
        }

        li{
            padding-top: 5px !important;
            text-align: right;
            width: 50%;
        }

        .order_content:last-child,
        .order_content.last-child{
            border-bottom: 0;
        }
    }

    .img-responsive{
        display: inline-block;
    }
}

.place_order_wrapper{

    @media screen and (max-width: $mobile_device){
        float: none;
    }

}


.ccav_secure_banner {
margin-right: 21px;
margin-top: 8px;

    @media screen and (max-width: $mobile_device){
        margin-bottom: 12px;
    }
}

.selected_card{
    color: #DFDADA;
    margin-top: 10px;
    font-size: 13px;

    @media screen and (max-width: $mobile_device){
            width: 100%;
        }
}

#change_address{
    line-height: normal;

    .address_title{
        font-size: 18px;
        line-height: normal;
    }

    .modal-dialog{
        width: 40%;
    }

    @media screen and (max-width: 992px){
        .modal-dialog{
            width: 70%;
        }
    }

    .col-md-5,
    .col-sm-5{
        border: 1px dotted #BEC2BE;
        padding: 15px;
        margin: 0 4% 4%;
        line-height: 18px;
        font-size: 12px;
    }

    .col-md-5:last-child,
    .col-sm-5:last-child{
        margin-bottom: 0;
    }


    @media screen and (max-width: $mobile_device){
        .modal-sm{
            width: 100%;
        }

        .modal_header_div{
            overflow: hidden;
        }

        .modal-content{
            padding: 15px;
            height: auto;
        }

        .modal-header div{
            text-align: left;
        }

        .modal-header div.close_wrap{
            text-align: right;
        }

        .close_wrap a img{
            vertical-align: sub;
        }
        .modal-header{
            border-bottom: 0;
            padding-bottom: 0;
        }

        .col-md-5,
        .col-sm-5{
            margin: 0;
        }

        .col-xs-12{
            margin-bottom: 15px;
            padding-bottom: 15px;
            padding-top: 15px;

        }
    }

    .modal-body:hover{
        cursor: pointer;
    }
}

.close_wrap a img {
    width: 25px;
    height: auto;
}

.modal-header .close {
    margin-top: 2px;
}

.order_acknowledgement p{
    color: #BBB9B9;
    font-size: 14px;
    line-height: 20px;
}

#order_details{
    color: $white_font;
    padding: 0 !important;

    .order_title{
        float:left;
        font-size: 14px;
        padding: 0 !important;
    }
    .order_title_content{
        float: left;
        font-size: 12px;
        padding: 0 !important;
        line-height: 14px;
        margin-left: 3px;
    }

}

.social-links{ overflow: hidden;}

.order-status{
    float: left;
    width:100%;
    border: 1px solid $white_font;
    color: $white_font;
    font-size: 14px;
    line-height: 20px;
    background: $black_color;
    margin-top: 25px;
    padding: 5px;

    .order-status-title{
        width: 97%;
        background: $white_font;
        border-radius: 3px;
        color: $black_color;
        text-align: center;
        margin-top: -17px;
        line-height: 26px;
        margin-bottom: 5px;
        margin-left: 5px;

        @media screen and (max-width: $ipad_max_width){
            margin-left: 10px;
        }
        @media screen and (max-width: $ipad_min_width){
            margin-left: 8px;
        }
    }

    .order-status-content{
        overflow: hidden;
    }
}

.order_cart_list{
    text-align: center;
    margin-top: 30px;

    ul{

        li{
           color: #aaa;
           font-size: 14px;
           border-bottom: 1px solid #2C2C2C;
        }

        li:last-child,
        li.last-child{
            border-bottom:none;
        }

        li.list_header1{
            height: 30px;
            line-height: 29px;
            background: #3A3A39;
            border: 1px solid #494949;

        }

        li.list_content{
            padding-top: 10px;
            padding-bottom: 8px;
        }
    }
}

.designer1,
.item1,
.price_box1,
.quantity1,
.addons1,
.sub_total1,
.note_link1,
.item_box1
{
    padding-left: 5px;
    word-wrap: break-word;
}

.designer1{
    width: 14%;
}

.item_box1{
    width: 8%;

    .img-responsive{
        display: inline-block;
    }
}

.price_box1{
    width: 10%;
}

.quantity1{
    width: 10%;
    input{
        width: 30%;
        border-radius: 4px;
        border: 0px;
        padding-left: 5px;
        padding-top: 2px;
        padding-bottom: 2px;
        padding-right: 2px;
        margin-bottom: 10px;

        @media screen and(max-width: $mobile_device){
            width: 60%;
        }
    }

}

.addons1{
    width: 22%;

    .entry{
        padding: 0 0 5px;
        line-height: 14px;
    }
}

.sub_total1{
    width: 10%;
}

.note_link1{
    width: 25%;

    span{
        font-size: 12px;
        margin-top: 5px;
        display: block;
    }
}

.total_product{

    ul{

        li{
            color: #fff;
            font-size: 14px;
            height: 25px;
            line-height: 25px;
            border-bottom: #ddd 1px solid;
            span{
                width: 105px;
                display: inline-block;
            }

            /*
            span.amount{
                width: auto;
            }
            */
        }

        li:last-child,
        li.last-child{
            border-bottom:0px;
        }
    }
}

@media screen and (max-width: $mobile_device){
    #when-get-items,
    #get_in_touch{
        padding-top: 10px;
    }
}



//mobile order_acknowledgement css
.mobile_note{
    width: 35%;

    input{
        border: 0 none;
        border-radius: 4px;
        margin-bottom: 10px;
        padding: 2px 5px;
        text-align: center;
        width: 100%;
    }

}

.days_passed{
  background-color:red;
  color:white;
  padding:3px;
}

.cart_mini_info {
  .free_shipment_text {
    color: $background-color;
    margin: 1em;
    padding: .2em;
    line-height: 1.3;
    width: 40%;
    @media screen and (max-width: $mobile_device){
      width: 100%;
      margin: 1em 0em;
    }
  }
}

//cod_confirmation_modal css
#codModal{
    #cod-confirmation-message{
      color: #808080;
    }
    .modal-content{
      border-radius: 0px;
      width: 700px;
      height: 100%;
      margin: 10% auto auto;
      background-color: $white;
      color: $global-text-color;
      font-size: 1.25em;
      line-height: 1.5;
      .close{
        color: $global-text-color;
        opacity: 0.5;
      }
      .modal-footer{
        text-align: center;
        padding: 5% 0 1%;
      }
      #cod-confirm-wo-otp{
        float: right;
        width: 20%;
        }
      .otp-verification-form{
        padding: 2%;
        #cod-otp{
         font-size: 14px;
          text-align: center;
          height: 39px;
          width: 230px;
        }
        #otp-error-msg{
          display: none;
          color: red;
          font-size: 14px;
        }
        .otp-resend-message{
            font-size: 0.7em;
            #resend-otp{
                border: none;
                background: none;
                display: inline;
                color: steelblue;
            }
        }
        .btn-lg{
          width: 31%;
          margin: 2%;
        }
      }
    }
}

.cashback-reminder {
    background-color: #eee;
    padding: 1.3em;
    margin-bottom: 0.7em;
    text-align: center;
    border: 2px solid #bdbdbd;
}

.phone {
    width: 49% !important;
}

.subscription-container {
    border: 1px solid #e0e0e0;
    padding: 20px;
    background-color: #f9f9f9;
    font-family: Arial, sans-serif;
    margin: 20px auto;
    padding-left: 20px;
    margin-right: 0px;
    margin-left: 0px;
  }
  
  input[type="checkbox"]#subscription_enabled {
    display: none;
  }
  
  .subscription-header {
    display: flex;
    align-items: center;
  
    input[type="checkbox"]#subscription_enabled {
      display: none;
    }
  
    label[for="subscription_enabled"] {
      position: relative;
      cursor: pointer;
      margin-right: 10px;
  
      &::before {
        content: '';
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 2px solid #ccc;
        border-radius: 4px;
        background-color: #fff;
        vertical-align: middle;
        transition: all 0.2s ease-in-out;
      }
    }
  
    input[type="checkbox"]#subscription_enabled:checked + label::before {
      background-color: red;
      border-color: red;
    }
  
    label[for="subscription_enabled"]::after {
      content: '\2713';
      position: absolute;
      top: -2px;
      left: 4px;
      color: white;
      font-size: 16px;
      line-height: 1;
      opacity: 0;
      transition: opacity 0.2s ease-in-out;
    }
  
    input[type="checkbox"]#subscription_enabled:checked + label::after {
      opacity: 1;
    }
  }

  .subscription-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  
    .fa {
      color: #4CAF50;
      margin-right: 10px;
    }
  
    h3 {
      margin: 0;
      font-size: 1.2em;
      font-weight: bold;
    }
  
    .saved-label {
      background-color: #d4edda;
      color: #155724;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 0.8em;
      margin-left: 10px;
      font-weight: bold;
    }
  }
  
  .subscription-details {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    font-size: 0.9em;
  
    p {
      margin: 0;
      line-height: 1.4;
      color: #555;
    }
  
    .saved-amount {
      color: #4CAF50;
      font-weight: bold;
      font-size: 1em;
      text-align: right;
    }
  }
  
  .breakdown-link {
    margin-bottom: 15px;
  
    a {
      color: #555;
      text-decoration: none;
  
      &:hover {
        text-decoration: underline;
      }
    }
  }
  
  .breakdown-details {
    display: none;
    table {
      width: 100%;
      border-collapse: collapse;
  
      td {
        padding: 10px 0;
        color: #555;
      }
  
      .price {
        text-align: right;
      }
  
      .discount {
        color: #d9534f;
      }
  
      .final-payable {
        font-weight: bold;
        border-top: 1px solid #ccc;
        margin-top: 10px;
        padding-top: 10px;
  
        .price {
          font-size: 1.1em;
        }
      }
    }
  }